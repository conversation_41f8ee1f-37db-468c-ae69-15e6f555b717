from prometheus_client import Counter, Histogram

# Cognitive metrics
TOPOLOGICAL_RETRIEVAL_PRECISION = Histogram(
    "aura_topological_retrieval_precision",
    "Precision of topological retrieval",
    buckets=[0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0],
)

REASONING_LOOP_DEPTH = Histogram(
    "aura_reasoning_loop_depth",
    "Depth of reasoning loops",
    buckets=[1, 2, 3, 4, 5, 10, 20, 50],
)
