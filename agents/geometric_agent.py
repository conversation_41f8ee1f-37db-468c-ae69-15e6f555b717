"""
Minimal Geometric Agent using LangGraph
Simple, focused agent that leverages geometric tools
NOT a complex multi-agent system - just one agent with powerful tools
"""

from typing import Dict, List, Any, Optional, TypedDict, Annotated
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import Too<PERSON><PERSON>xecutor, ToolInvocation
from langgraph.checkpoint import MemorySaver
import numpy as np
import asyncio
import json
import logging
from dataclasses import dataclass, asdict
from enum import Enum

# Import our geometric modules
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from retrieval.geometric_retrieval import GeometricRetrievalEngine, RetrievalMethod, Document
from geometric_core.tda_engine import AdvancedTDAEngine
from geometric_core.hyperbolic_engine import HyperbolicEngine
from geometric_core.sheaf_engine import SheafConsistencyEngine


class AgentState(TypedDict):
    """State for the geometric agent"""
    # Input
    query: str
    embedding: Optional[List[float]]
    context: Optional[List[Dict]]
    
    # Processing
    retrieval_results: Optional[List[Dict]]
    tda_features: Optional[Dict]
    hyperbolic_coords: Optional[List[float]]
    consistency_score: Optional[float]
    anomaly_detected: bool
    
    # Output
    response: Optional[str]
    confidence: float
    explanation: Optional[str]
    
    # Control flow
    should_use_council: bool
    error: Optional[str]


class GeometricTool:
    """Base class for geometric tools"""
    
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
        
    async def execute(self, **kwargs) -> Dict[str, Any]:
        """Execute the tool"""
        raise NotImplementedError


class TDAAnalysisTool(GeometricTool):
    """Tool for topological data analysis"""
    
    def __init__(self):
        super().__init__(
            name="tda_analysis",
            description="Analyze topological features of data using persistent homology"
        )
        self.engine = AdvancedTDAEngine()
        
    async def execute(self, points: np.ndarray, method: str = "auto") -> Dict[str, Any]:
        """Compute TDA features"""
        result = self.engine.compute_persistence(points, method=method)
        
        return {
            "betti_numbers": result.betti_numbers,
            "persistence_entropy": result.persistence_entropy,
            "anomaly_score": result.anomaly_score,
            "n_features": len(result.diagram),
            "computation_time": result.computation_time,
            "method_used": result.method_used
        }


class HyperbolicEmbeddingTool(GeometricTool):
    """Tool for hyperbolic embeddings"""
    
    def __init__(self):
        super().__init__(
            name="hyperbolic_embedding",
            description="Embed data in hyperbolic space to preserve hierarchy"
        )
        self.engine = HyperbolicEngine()
        
    async def execute(self, data: np.ndarray) -> Dict[str, Any]:
        """Compute hyperbolic embedding"""
        # Project to hyperbolic space
        coords = self.engine.model.project(data)
        
        # Compute distance from origin (hierarchy level)
        origin = np.zeros(len(coords))
        distance = self.engine.model.distance(origin, coords)
        
        return {
            "coordinates": coords.tolist(),
            "hierarchy_level": float(distance),
            "model": self.engine.model_name
        }


class ConsistencyCheckTool(GeometricTool):
    """Tool for checking global consistency using sheaf cohomology"""
    
    def __init__(self):
        super().__init__(
            name="consistency_check",
            description="Check global consistency of retrieved information"
        )
        self.engine = SheafConsistencyEngine()
        
    async def execute(self, documents: List[Dict]) -> Dict[str, Any]:
        """Check consistency across documents"""
        if len(documents) < 2:
            return {"consistent": True, "score": 0.0, "explanation": "Too few documents"}
            
        # Build graph and check consistency (simplified)
        import networkx as nx
        G = nx.complete_graph(len(documents))
        
        # Use embeddings as local data
        local_data = {}
        for i, doc in enumerate(documents):
            if 'embedding' in doc:
                local_data[i] = np.array(doc['embedding'][:2])  # Use first 2 dims
            else:
                local_data[i] = np.random.randn(2)
                
        result = self.engine.check_consistency(G, local_data)
        
        return {
            "consistent": result.consistent,
            "score": result.score,
            "h0_dim": result.h0_dim,
            "h1_dim": result.h1_dim,
            "explanations": result.explanations
        }


class GeometricRetrievalTool(GeometricTool):
    """Tool for geometric retrieval"""
    
    def __init__(self, retrieval_engine: GeometricRetrievalEngine):
        super().__init__(
            name="geometric_retrieval",
            description="Retrieve documents using geometric methods"
        )
        self.engine = retrieval_engine
        
    async def execute(self, query: str, embedding: np.ndarray, 
                      k: int = 10, method: str = "geometric") -> Dict[str, Any]:
        """Perform geometric retrieval"""
        result = await self.engine.retrieve(
            query=query,
            query_embedding=embedding,
            k=k,
            method=RetrievalMethod(method)
        )
        
        # Convert results to serializable format
        retrieval_results = []
        for r in result['results']:
            retrieval_results.append({
                'doc_id': r.doc_id,
                'score': r.score,
                'semantic_score': r.semantic_score,
                'topological_score': r.topological_score,
                'hyperbolic_score': r.hyperbolic_score,
                'consistency_score': r.consistency_score,
                'explanation': r.explanation
            })
            
        return {
            'results': retrieval_results,
            'lift': result.get('lift_over_semantic', 0),
            'anomaly_score': result.get('anomaly_score', 0),
            'latency': result['latency']
        }


class MinimalGeometricAgent:
    """
    Minimal but powerful agent using geometric tools
    Simple workflow: Embed → Retrieve → Analyze → Respond
    NOT a complex multi-agent system
    """
    
    def __init__(self, retrieval_engine: Optional[GeometricRetrievalEngine] = None):
        # Initialize tools
        self.tda_tool = TDAAnalysisTool()
        self.hyperbolic_tool = HyperbolicEmbeddingTool()
        self.consistency_tool = ConsistencyCheckTool()
        
        # Retrieval engine
        if retrieval_engine is None:
            retrieval_engine = GeometricRetrievalEngine()
        self.retrieval_tool = GeometricRetrievalTool(retrieval_engine)
        
        # Build workflow
        self.workflow = self._build_workflow()
        
        # Thresholds
        self.anomaly_threshold = 0.7
        self.consistency_threshold = 0.5
        
        # Logger
        self.logger = logging.getLogger(__name__)
        
    def _build_workflow(self) -> StateGraph:
        """Build the agent workflow graph"""
        workflow = StateGraph(AgentState)
        
        # Add nodes
        workflow.add_node("embed_query", self._embed_query)
        workflow.add_node("geometric_retrieve", self._geometric_retrieve)
        workflow.add_node("analyze_topology", self._analyze_topology)
        workflow.add_node("check_consistency", self._check_consistency)
        workflow.add_node("generate_response", self._generate_response)
        workflow.add_node("council_review", self._council_review)
        
        # Add edges
        workflow.add_edge("embed_query", "geometric_retrieve")
        workflow.add_edge("geometric_retrieve", "analyze_topology")
        
        # Conditional routing based on anomaly detection
        workflow.add_conditional_edges(
            "analyze_topology",
            self._route_by_anomaly,
            {
                "normal": "check_consistency",
                "anomaly": "council_review"
            }
        )
        
        workflow.add_edge("check_consistency", "generate_response")
        workflow.add_edge("council_review", "generate_response")
        
        # Set entry point
        workflow.set_entry_point("embed_query")
        
        return workflow.compile()
        
    async def _embed_query(self, state: AgentState) -> AgentState:
        """Embed the query (placeholder - would use real embedding model)"""
        # In production, use a real embedding model
        if state.get('embedding') is None:
            # Generate random embedding for demo
            state['embedding'] = np.random.randn(768).tolist()
            
        self.logger.info(f"Query embedded: {state['query'][:50]}...")
        return state
        
    async def _geometric_retrieve(self, state: AgentState) -> AgentState:
        """Retrieve using geometric methods"""
        try:
            # Perform retrieval
            result = await self.retrieval_tool.execute(
                query=state['query'],
                embedding=np.array(state['embedding']),
                k=10,
                method="geometric"
            )
            
            state['retrieval_results'] = result['results']
            
            # Check for anomalies
            if result['anomaly_score'] > self.anomaly_threshold:
                state['anomaly_detected'] = True
                self.logger.warning(f"Anomaly detected: score={result['anomaly_score']}")
            else:
                state['anomaly_detected'] = False
                
            # Log lift
            if result['lift'] > 0:
                self.logger.info(f"Geometric lift: {result['lift']:.2%}")
                
        except Exception as e:
            state['error'] = f"Retrieval error: {str(e)}"
            self.logger.error(state['error'])
            state['retrieval_results'] = []
            
        return state
        
    async def _analyze_topology(self, state: AgentState) -> AgentState:
        """Analyze topological features of retrieved documents"""
        if not state.get('retrieval_results'):
            return state
            
        try:
            # Create point cloud from retrieved embeddings
            # In production, would use actual document embeddings
            points = np.random.randn(len(state['retrieval_results']), 10)
            
            # Compute TDA features
            tda_result = await self.tda_tool.execute(points)
            state['tda_features'] = tda_result
            
            # Update anomaly detection based on topology
            if tda_result['anomaly_score'] > self.anomaly_threshold:
                state['anomaly_detected'] = True
                
            self.logger.info(f"TDA analysis: {tda_result['n_features']} features, "
                           f"entropy={tda_result['persistence_entropy']:.3f}")
                           
        except Exception as e:
            state['error'] = f"TDA error: {str(e)}"
            self.logger.error(state['error'])
            
        return state
        
    def _route_by_anomaly(self, state: AgentState) -> str:
        """Route based on anomaly detection"""
        if state.get('anomaly_detected', False):
            self.logger.info("Routing to council review due to anomaly")
            return "anomaly"
        return "normal"
        
    async def _check_consistency(self, state: AgentState) -> AgentState:
        """Check global consistency of retrieved information"""
        if not state.get('retrieval_results'):
            return state
            
        try:
            # Check consistency
            result = await self.consistency_tool.execute(state['retrieval_results'])
            
            state['consistency_score'] = result['score']
            
            # Flag for council if inconsistent
            if result['score'] > self.consistency_threshold:
                state['should_use_council'] = True
                self.logger.warning(f"Inconsistency detected: score={result['score']}")
                
            # Add explanation
            if result['explanations']:
                state['explanation'] = "; ".join(result['explanations'])
                
        except Exception as e:
            state['error'] = f"Consistency check error: {str(e)}"
            self.logger.error(state['error'])
            
        return state
        
    async def _council_review(self, state: AgentState) -> AgentState:
        """
        Council review for anomalous/inconsistent cases
        In production, this would involve multiple specialized agents
        For now, just flag it and adjust confidence
        """
        self.logger.info("Council review triggered")
        
        state['should_use_council'] = True
        state['confidence'] = 0.5  # Lower confidence due to anomaly
        
        # Add explanation about council review
        explanation = "Council review triggered due to: "
        reasons = []
        
        if state.get('anomaly_detected'):
            reasons.append("topological anomaly detected")
            
        if state.get('consistency_score', 0) > self.consistency_threshold:
            reasons.append(f"high inconsistency (score={state['consistency_score']:.2f})")
            
        state['explanation'] = explanation + ", ".join(reasons)
        
        return state
        
    async def _generate_response(self, state: AgentState) -> AgentState:
        """Generate final response"""
        # Compile response from retrieved documents
        if state.get('retrieval_results'):
            top_docs = state['retrieval_results'][:3]
            
            response_parts = [f"Based on geometric analysis of your query '{state['query']}':"]
            
            for i, doc in enumerate(top_docs, 1):
                response_parts.append(f"\n{i}. Document {doc['doc_id']} (score: {doc['score']:.3f})")
                response_parts.append(f"   - {doc['explanation']}")
                
            # Add topology insights
            if state.get('tda_features'):
                tda = state['tda_features']
                response_parts.append(f"\nTopological analysis: {tda['n_features']} persistent features detected")
                
            # Add consistency info
            if state.get('consistency_score') is not None:
                if state['consistency_score'] < 0.3:
                    response_parts.append("\n✓ Information is globally consistent")
                else:
                    response_parts.append(f"\n⚠ Potential inconsistencies detected (score: {state['consistency_score']:.2f})")
                    
            # Add confidence
            if not state.get('confidence'):
                # Calculate confidence based on signals
                confidence = 1.0
                if state.get('anomaly_detected'):
                    confidence *= 0.7
                if state.get('consistency_score', 0) > 0.5:
                    confidence *= 0.8
                state['confidence'] = confidence
                
            response_parts.append(f"\nConfidence: {state['confidence']:.1%}")
            
            # Add explanation if present
            if state.get('explanation'):
                response_parts.append(f"\nNote: {state['explanation']}")
                
            state['response'] = "\n".join(response_parts)
        else:
            state['response'] = f"Unable to process query: {state.get('error', 'Unknown error')}"
            state['confidence'] = 0.0
            
        return state
        
    async def process(self, query: str, embedding: Optional[np.ndarray] = None) -> Dict[str, Any]:
        """
        Process a query through the geometric agent
        
        Args:
            query: User query
            embedding: Optional pre-computed embedding
            
        Returns:
            Agent response with metadata
        """
        # Initialize state
        initial_state: AgentState = {
            'query': query,
            'embedding': embedding.tolist() if embedding is not None else None,
            'context': None,
            'retrieval_results': None,
            'tda_features': None,
            'hyperbolic_coords': None,
            'consistency_score': None,
            'anomaly_detected': False,
            'response': None,
            'confidence': 0.0,
            'explanation': None,
            'should_use_council': False,
            'error': None
        }
        
        # Run workflow
        final_state = await self.workflow.ainvoke(initial_state)
        
        # Extract key results
        return {
            'response': final_state.get('response', 'No response generated'),
            'confidence': final_state.get('confidence', 0.0),
            'anomaly_detected': final_state.get('anomaly_detected', False),
            'consistency_score': final_state.get('consistency_score'),
            'should_use_council': final_state.get('should_use_council', False),
            'explanation': final_state.get('explanation'),
            'error': final_state.get('error')
        }
        
    def visualize_workflow(self) -> str:
        """Get workflow visualization"""
        return """
        Geometric Agent Workflow:
        
        [Query] → [Embed] → [Geometric Retrieve] → [Analyze Topology]
                                                           ↓
                                        [Anomaly?] → Yes → [Council Review]
                                             ↓ No            ↓
                                    [Check Consistency]       ↓
                                             ↓                ↓
                                        [Generate Response] ←─┘
        
        Tools:
        - TDA Analysis: Compute topological features
        - Hyperbolic Embedding: Preserve hierarchy
        - Consistency Check: Sheaf cohomology
        - Geometric Retrieval: Multi-signal search
        """