load("//bazel:api.bzl", "modular_py_binary", "requirement")

package(default_visibility = ["//visibility:public"])

filegroup(
    name = "testdata",
    testonly = True,
    srcs = glob(
        ["*"],
        exclude = ["*.py"],
    ),
)

modular_py_binary(
    name = "gen_external_checkpoints",
    testonly = True,
    srcs = ["gen_external_checkpoints.py"],
    deps = [
        requirement("torch"),
        requirement("gguf"),
        requirement("click"),
        requirement("numpy"),
        requirement("safetensors"),
    ],
)
