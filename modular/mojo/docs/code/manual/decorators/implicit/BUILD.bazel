load("//bazel:api.bzl", "modular_run_binary_test", "mojo_binary")

MOJO_SRCS = glob(["*.mojo"])

[
    mojo_binary(
        name = src.split(".")[0],
        srcs = [src],
        deps = [
            "@mojo//:stdlib",
        ],
    )
    for src in MOJO_SRCS
]

[
    modular_run_binary_test(
        name = src.split(".")[0] + "_test",
        size = "small",
        binary = src.split(".")[0],
    )
    for src in MOJO_SRCS
]
