---
title: Basics of GPU programming with Mojo
sidebar_label: Basics
description: Learn the basics of GPU programming with Mojo.
---

import Requirements from '@site/src/components/Requirements';
import { requirementsWithGPU } from '@site/docs/max/requirements';

If you have any questions or feedback for this content, please post it in the
[Modular forum thread
here](https://forum.modular.com/t/gpu-programming-manual/755).

This documentation aims to build your GPU programming knowledge from the ground
up, starting with the lowest levels of the stack before progressing to
higher-level functionality. It's designed for a diverse audience, from
experienced GPU developers to programmers new to GPU coding. Mojo allows you to
program NVIDIA GPUs, with direct access to low-level GPU primitives, while
sharing types and functions that can also run on CPUs where applicable.  If
you're experienced with [NVIDIA Compute Unified Device
Architecture](https://developer.nvidia.com/cuda-toolkit) (CUDA), what you'll
learn here will enable you to expand your reach as we release support for more
hardware.

## Introduction to massively parallel programming

We can no longer rely on new generations of CPUs to increase application
performance through improved clock speeds. Power demands and heat dissipation
limits have stalled that trend, pushing the hardware industry toward increasing
the number of physical cores. Modern consumer CPUs now boast 16 cores or more,
capable of running in parallel, which forces programmers to rethink how they
maximize performance. This shift is especially evident in AI applications, where
performance scales remarkably well with additional cores.

NVIDIA's breakthrough came with CUDA, a general programming model that allows
developers to target both server and consumer GPUs for any application domain.
This vision sparked an AI revolution when Alex Krizhevsky, Ilya Sutskever, and
Geoffrey Hinton trained AlexNet on consumer GPUs, significantly outperforming
traditional computer vision methods. GPUs pack thousands of cores, the NVIDIA
H100 can run 16,896 threads in parallel in a single clock cycle, with over
270,000 threads queued and ready to go. They're also engineered in a way where
the cost of scheduling threads is much lower compared to a traditional CPU.

Harnessing this hardware requires a new programming mindset. Mojo represents a
chance to rethink GPU programming and make it more approachable. C/C++ is at the
core of GPU programming, but we've seen leaps in ergonomics and memory safety
from systems programming languages in recent years. Mojo expands on Python's
familiar syntax, adds direct access to low-level CPU and GPU intrinsics for
systems programming, and introduces ergonomic and safety improvements from
modern languages. This course aims to empower programmers with minimal
specialized knowledge to build high-performance, GPU-enabled applications. By
lowering the barrier to entry, we aim to fuel more breakthroughs and accelerate
innovation.

## Setup

System requirements:

<Requirements requirementsData={requirementsWithGPU} />

:::note

These examples can run on many consumer NVIDIA GeForce GPUs, though they aren't
officially supported yet. Make sure you have the latest NVIDIA drivers.

:::

All of these notebook cells are runnable through a VS Code extension. You can
install
[Markdown Lab](https://marketplace.visualstudio.com/items?itemName=jackos.mdlab),
then clone the repo that contains the markdown that generated this website:

```sh
<NAME_EMAIL>:modular/modular
cd modular/mojo/docs/manual/gpu
```

And open `basics.mdx` to run the code cells interactively. If you don't have
`mojo` on your system PATH it will automatically download and install it the
first time you run a Mojo cell.

If you prefer the traditional approach using a CLI, first install
[`pixi`](https://pixi.sh/latest/) if you don't have it:

```sh
curl -fsSL https://pixi.sh/install.sh | sh
```

Then restart your terminal, create a project, install the `mojo` package, and
enter the virtual environment:

```sh
pixi init gpu-basics \
  -c https://conda.modular.com/max-nightly/ -c conda-forge

cd gpu-basics

pixi add mojo

pixi shell # enter virtual environment
```

You can now create file such as `main.mojo` and put everything except the
imports into a `def main`:

```mojo :once
from gpu import thread_idx
from gpu.host import DeviceContext

def main():
    fn printing_kernel():
        print("GPU thread: [", thread_idx.x, thread_idx.y, thread_idx.z, "]")

    var ctx = DeviceContext()

    ctx.enqueue_function[printing_kernel](grid_dim=1, block_dim=4)
    ctx.synchronize()
```

```text
GPU thread: [ 0 0 0 ]
GPU thread: [ 1 0 0 ]
GPU thread: [ 2 0 0 ]
GPU thread: [ 3 0 0 ]
```

Then compile and run the file using `mojo main.mojo`.

When you're ready to exit the virtual environment run the command: `exit`.

## Imports

These are all the imports required to run the examples, put this at the top of
your file if you're running from `mojo main.mojo`:

```mojo
from gpu import thread_idx, block_idx, warp, barrier
from gpu.host import DeviceContext, DeviceBuffer, HostBuffer
from gpu.memory import AddressSpace
from memory import stack_allocation
from layout import Layout, LayoutTensor
from math import iota
from sys import sizeof
```

## Your first kernel

In the context of GPU programming, a kernel is a program that runs on each
thread that you launch:

```mojo
fn printing_kernel():
    print("GPU thread: [", thread_idx.x, thread_idx.y, thread_idx.z, "]")
```

:::note

We're using `fn` here without the `raises` keyword because a kernel function is
not allowed to raise an error condition. When you define a Mojo function with
`def`, the compiler always assumes that the function *can* raise an error
condition. See [Functions](/mojo/manual/functions) more information.

:::

We can pass this function as a parameter to `enqueue_function()` to compile it
for your attached GPU and launch it. First we need to get the
[`DeviceContext`](/mojo/stdlib/gpu/host/device_context/DeviceContext) for your
GPU:

```mojo
var ctx = DeviceContext()
```

Now we have the `DeviceContext` we can compile and launch the kernel:

```mojo :once
ctx.enqueue_function[printing_kernel](grid_dim=1, block_dim=4)

# Wait for the kernel to finish executing before handing back to CPU
ctx.synchronize()
```

```text
GPU thread: [ 0 0 0 ]
GPU thread: [ 1 0 0 ]
GPU thread: [ 2 0 0 ]
GPU thread: [ 3 0 0 ]
```

:::note

The term `kernel` in this context originated in the 1980s with the introduction
of the [Single Program, Multiple
Data](https://en.wikipedia.org/wiki/Single_program,_multiple_data) (SPMD)
parallel programming technique, which underpins ROCm and CUDA. In this approach,
a kernel executes concurrently across distinct elements of large data
structures.

:::

## Threads

Because we passed `block_dim=4`, we launched 4 threads on the x dimension, the
kernel code we wrote is executed on each thread. The printing can be out of
order depending on which thread reaches that `print()` call first.

Now add the y and z dimensions with `block_dim=(2, 2, 2)`:

:::note

For the `grid_dim` and `block_dim` arguments you can use a single value or a
tuple. A single value will launch N blocks/threads on the x dimension, while
using a tuple with up to three values will determine the (x, y, z) dimensions.

:::

```mojo :once
ctx.enqueue_function[printing_kernel](grid_dim=1, block_dim=(2, 2, 2))
ctx.synchronize()
```

```text
GPU thread: [ 0 0 0 ]
GPU thread: [ 1 0 0 ]
GPU thread: [ 0 1 0 ]
GPU thread: [ 1 1 0 ]
GPU thread: [ 0 0 1 ]
GPU thread: [ 1 0 1 ]
GPU thread: [ 0 1 1 ]
GPU thread: [ 1 1 1 ]
```

We're now launching 8 (2x2x2) threads in total.

## Host vs device

You'll see the word host which refers to the CPU that schedules work for the
device, device refers to the accelerator which in this case is a GPU.

When you're running device side code, it means that the host is scheduling the
operation to execute asynchronously on the device. If your host-side code relies
on the outcome of device-side code, you need to call `ctx.synchronize()`. For
instance, printing from the CPU without first synchronizing might result in
out-of-order output:

```mojo :once
ctx.enqueue_function[printing_kernel](grid_dim=1, block_dim=4)
print("This might print before the GPU has completed its work")
```

```text
This might print before the GPU has completed its work
GPU thread: [ 0 0 0 ]
GPU thread: [ 1 0 0 ]
GPU thread: [ 2 0 0 ]
GPU thread: [ 3 0 0 ]
```

In the above example we failed to call `synchronize()` before printing on the
host, the device could be slightly slower to finish its work, so you might
see that output after the host output. Let's add a `synchronize()` call:

```mojo :once
ctx.enqueue_function[printing_kernel](grid_dim=1, block_dim=4)
ctx.synchronize()
print("This will print after the GPU has completed its work")
```

```text
GPU thread: [ 0 0 0 ]
GPU thread: [ 1 0 0 ]
GPU thread: [ 2 0 0 ]
GPU thread: [ 3 0 0 ]
This will print after the GPU has completed its work
```

Any method you run on a `DeviceContext` that interacts with the device, will run
in the order that you called them. You only have to synchronize when you're
doing something from the host which is dependent on the results of the device.

In GPU programming with Mojo, when there's a tradeoff between GPU performance
and safety or ergonomics, performance takes priority, aligning with the
expectations of kernel engineers. For instance while we could force
synchronization for each call that interacts with the device, this would come at
a performance cost where we want to run multiple functions asynchronously and
synchronize once.

:::warning Synchronization

For any methods or functions that interact with the device, you must synchronize
before running CPU code that is dependent on it's execution. Multiple method or
function calls for a single GPU is safe, as they are scheduled to run in the
order you call them.

:::

Mojo enhances the safety and ergonomics of C++ GPU programming where it doesn't
sacrifice performance. For example, ASAP destruction automatically frees buffer
memory on last use of the object, eliminating memory leaks and ensuring memory
is released as early as possible. This is an evolution on modern memory
management solutions such as C++ RAII, which is scope based and may hold onto
memory for longer than expected, which is a precious resource in AI
applications.

## Blocks

This kernel demonstrates how blocks work:

```mojo :once
fn block_kernel():
    print(
        "block: [",
        block_idx.x,
        block_idx.y,
        block_idx.z,
        "]",
        "thread: [",
        thread_idx.x,
        thread_idx.y,
        thread_idx.z,
        "]"
    )

ctx.enqueue_function[block_kernel](grid_dim=(2, 2), block_dim=2)
ctx.synchronize()
```

```text
block: [ 0 0 0 ] thread: [ 0 0 0 ]
block: [ 0 0 0 ] thread: [ 1 0 0 ]
block: [ 0 1 0 ] thread: [ 0 0 0 ]
block: [ 0 1 0 ] thread: [ 1 0 0 ]
block: [ 1 1 0 ] thread: [ 0 0 0 ]
block: [ 1 1 0 ] thread: [ 1 0 0 ]
block: [ 1 0 0 ] thread: [ 0 0 0 ]
block: [ 1 0 0 ] thread: [ 1 0 0 ]
```

We're still launching 8 (2x2x2) threads, where there are 4 blocks, each with 2
threads. In GPU programming this grouping of blocks and threads is important,
each block can have its own fast SRAM (Static Random Access Memory) which allows
threads to communicate. The threads within a block can also communicate through
registers, we'll cover this concept when we get to warps. For now the
important information to internalize is:

- `grid_dim` defines how many blocks are launched.
- `block_dim` defines how many threads are launched in each block.

## Tiles

The x, y, z dimensions of blocks are important for splitting up large jobs into
tiles, so each thread can work on its own subset of the problem. Below is a
visualization for how a contiguous array of data can be split up into tiles, if
we have an array of UInt32 (Unsigned Integer 32bit) data like:

```plaintext
[ 0 1 2 3 4 5 6 7 8 9 10 11 12 13 14 15 ]
```

We could split work up between threads and blocks, we're only going to use the x
dimension for threads and blocks to get started:

```plaintext
Thread  |    0  1  2  3
-------------------------
block 0 | [  0  1  2  3 ]
block 1 | [  4  5  6  7 ]
block 2 | [  8  9 10 11 ]
block 3 | [ 12 13 14 15 ]
```

If you had a much larger data array you could further split it up into tiles,
e.g. tile with widths [2, 2] at index (0, 0) would be:

```plaintext
[ 0 1 ]
[ 4 5 ]
```

And index (2, 0) would be:

```plaintext
[ 2 3 ]
[ 6 7 ]
```

This is where you'd introduce the y dimension, later we'll begin working on
image data which is a tensor with 3 dimensions: (height, width, color_channels).
For now we're going to focus on how blocks and threads interact, splitting up an
array into 1 row per block, and 1 value per thread.

## Buffers

First we'll initialize a contiguous array on the GPU:

```mojo
alias dtype = DType.uint32
alias blocks = 4
alias threads = 4
alias elements_in = blocks * threads # one element per thread

var in_buffer = ctx.enqueue_create_buffer[dtype](elements_in)
```

Creating the GPU buffer is allocating _global memory_ which can be accessed from
any block and thread inside a GPU kernel, this memory is relatively slow
compared to _shared memory_ which is shared between all of the threads in a
block, more on that later.

We can't access memory in a GPU address space from CPU to initialize the values
unless we map it to host:

```mojo
with in_buffer.map_to_host() as host_buffer:
    iota(host_buffer.unsafe_ptr(), elements_in)
    print(host_buffer)
```

```text
HostBuffer([0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15])
```

If you're loading or storing values from a buffer allocated on GPU, mapping to
host ensures the values are copied into the CPU address space when the context
manager enters (start of the `with` block), and back to the GPU address space
when the context manager exits (end of the `with` block). Note that
`map_to_host()` will call `synchronize()` before writing the data back to CPU,
so you don't have to call it separately.

## Tensor indexing from threads

Now that we have the data set up, we can wrap the data in a
[LayoutTensor](/mojo/kernels/layout/layout_tensor/LayoutTensor/) so that we can
reason about how to index into the array, allowing each thread to get its
corresponding value:

```mojo :clear
alias layout = Layout.row_major(blocks, threads)

var in_tensor = LayoutTensor[dtype, layout](in_buffer)
```

:::note Memory Layout

"Row major" means the values are stored sequentially in memory:

[ 0 1 2 3 4 5 6 7 8 9 10 11 12 13 14 15 ]

"Column major" means memory advances down each column first, then moves to the
next column. This layout is used in some GPU tiling kernels because it can align
with coalesced column accesses:

[ 0 4 8 12 1 5 9 13 2 6 10 14 3 7 11 15 ]

:::

`LayoutTensor` is a view of the data in buffer, it does not own the underlying
memory. It's a powerful abstraction and offers many advanced methods which we'll
dive into in later chapters.

We'll create an alias so that we don't have to repeat the type information for
each kernel launch:

```mojo :clear
alias InTensor = LayoutTensor[dtype, layout, MutableAnyOrigin]
```

More information on [origins here](/mojo/manual/values/lifetimes).

Initially we'll just print the values to confirm it's indexing as we expect:

```mojo :once
fn print_values_kernel(in_tensor: InTensor):
    var bid = block_idx.x
    var tid = thread_idx.x
    print("block:", bid, "thread:", tid, "val:", in_tensor[bid, tid])

ctx.enqueue_function[print_values_kernel](
    in_tensor, grid_dim=blocks, block_dim=threads,
)
ctx.synchronize()
```

```text
block: 0 thread: 0 val: 0
block: 0 thread: 1 val: 1
block: 0 thread: 2 val: 2
block: 0 thread: 3 val: 3
block: 1 thread: 0 val: 4
block: 1 thread: 1 val: 5
block: 1 thread: 2 val: 6
block: 1 thread: 3 val: 7
block: 3 thread: 0 val: 12
block: 3 thread: 1 val: 13
block: 3 thread: 2 val: 14
block: 3 thread: 3 val: 15
block: 2 thread: 0 val: 8
block: 2 thread: 1 val: 9
block: 2 thread: 2 val: 10
block: 2 thread: 3 val: 11
```

As in the visualization above, the block/thread is getting the corresponding
value that we expect. You can see `block: 3 thread: 3` has the last value 15.
Try experimenting with different `grid_dim`, `block_dim` and indexing values
to see how the behavior changes.

## Multiply kernel

Now that we've verified we're getting the correct values when indexing, we'll
launch a kernel to multiply each value:

```mojo :once
fn multiply_kernel[multiplier: Int](in_tensor: InTensor):
    in_tensor[block_idx.x, thread_idx.x] *= multiplier

ctx.enqueue_function[multiply_kernel[2]](
    in_tensor,
    grid_dim=blocks,
    block_dim=threads,
)

# Map to host and print as 2D array
with in_buffer.map_to_host() as host_buffer:
    var host_tensor = LayoutTensor[dtype, layout](host_buffer)
    print(host_tensor)
```

```text
0 2 4 6
8 10 12 14
16 18 20 22
24 26 28 30
```

Congratulations! You've successfully run a kernel that modifies values from your
GPU, and printed the result on your CPU. You can see above that each thread
multiplied a single value by 2 in parallel on the GPU, and copied the result
back to the CPU.

## Sum reduce output

We're going to set up a new buffer which will have all the reduced values with
the sum of each thread in the block:

```plaintext
Output: [ block[0] block[1] block[2] block[3] ]
```

Set up the output buffer/tensor for the host and device:

```mojo :clear
var out_buffer = ctx.enqueue_create_buffer[dtype](blocks)

# Zero the values on the device as they'll be used to accumulate results
_ = out_buffer.enqueue_fill(0)

alias out_layout = Layout.row_major(blocks)
alias OutTensor = LayoutTensor[dtype, out_layout, MutableAnyOrigin]

var out_tensor = OutTensor(out_buffer)
```

The problem here is that we can't have all the threads summing their values into
the same index in the output buffer as that will introduce race conditions.
We're going to introduce new concepts to deal with this.

## Shared memory

This kernel uses shared memory to accumulate values. Shared memory is much
faster than global memory because it resides on-chip, closer to the processing
cores, reducing latency and increasing bandwidth. It's not an optimal solution
for this kind of reduction operation, but it's a good way to introduce shared
memory in a simple example.  We'll cover better solutions in the next sections.

```mojo :once
fn sum_reduce_kernel(
    in_tensor: InTensor, out_tensor: OutTensor
):
    # This allocates memory to be shared between threads in a block prior to the
    # kernel launching. Each kernel gets a pointer to the allocated memory.
    var shared = stack_allocation[
        threads,
        Scalar[dtype],
        address_space = AddressSpace.SHARED,
    ]()

    # Place the corresponding value into shared memory
    shared[thread_idx.x] = in_tensor[block_idx.x, thread_idx.x][0]

    # Await all the threads to finish loading their values into shared memory
    barrier()

    # If this is the first thread, sum and write the result to global memory
    if thread_idx.x == 0:
        for i in range(threads):
            out_tensor[block_idx.x] += shared[i]

ctx.enqueue_function[sum_reduce_kernel](
    in_tensor,
    out_tensor,
    grid_dim=blocks,
    block_dim=threads,
)

# Copy the data back to the host and print out the buffer
with out_buffer.map_to_host() as host_buffer:
    print(host_buffer)
```

```text
HostBuffer([6, 22, 38, 54])
```

For our first block/tile we summed the values:

```plaintext
sum([ 0 1 2 3 ]) == 6
```

And the reduction resulted in the output having the sum of 6 in the first
position. Every tile/block has been reduced to:

```plaintext
[ 6 22 38 54]
```

## Sum multiple values from a single thread

We could skip using shared memory altogether by launching a single thread per
block. Each thread can load more than a single value, here we'll be launching
one thread per block, loading the 4 corresponding values from that block, and
summing them together:

```mojo :once
fn simd_reduce_kernel(
    in_tensor: InTensor, out_tensor: OutTensor
):
    # The [4] means it loads 4 sequential values before doing the `reduce_add`
    out_tensor[block_idx.x] = in_tensor.load[4](block_idx.x, 0).reduce_add()

ctx.enqueue_function[simd_reduce_kernel](
    in_tensor,
    out_tensor,
    grid_dim=blocks,
    block_dim=1, # one thread per block
)

# Ensure we have the same result
with out_buffer.map_to_host() as host_buffer:
    print(host_buffer)
```

```text
HostBuffer([6, 22, 38, 54])
```

This is cleaner and faster, instead of 4 threads writing to shared memory, we're
using 1 thread per block and summing them together without the intermediate
step. However, this can be even faster by launching one thread per value and
doing a single instruction in parallel using warps.

## Warps

:::note Warps

Warp level instructions are an advanced concept, this section is to demonstrate
that these low-level primitives are available from Mojo. We'll go into more
depth on warps later, so don't worry if it doesn't make sense yet.

:::

A _warp_ is a group of threads (32 on NVIDIA GPUs) within a block. Threads
within the same warp can synchronize their execution, and take advantage of
[Single Instruction, Multiple
Threads](https://en.wikipedia.org/wiki/Single_instruction,_multiple_threads)
(SIMT). SIMT (GPU-focused) allows multiple threads to execute the same
instruction on different data with independent control flow and thread states,
while SIMD (CPU-focused) applies a single instruction to multiple data elements
simultaneously with no thread independence.

We have only 4 threads within each block, well under the 32 limit, if this
wasn't the case you'd have to do two reductions, one from each warp to shared
memory, then another from shared memory to the output buffer or tensor.

Here is a simple warp reduction kernel:

```mojo :once
fn warp_reduce_kernel(
    in_tensor: InTensor, out_tensor: OutTensor
):
    var value = in_tensor.load[1](block_idx.x, thread_idx.x)

    # Each thread gets the value from one thread higher, summing them as they go
    value = warp.sum(value)

    # Print each reduction step in the first block
    if block_idx.x == 0:
        print("thread:", thread_idx.x, "value:", value)

    # Thread 0 has the reduced sum of the values from all the other threads
    if thread_idx.x == 0:
        out_tensor[block_idx.x] = value

ctx.enqueue_function[warp_reduce_kernel](
    in_tensor,
    out_tensor,
    grid_dim=blocks,
    block_dim=threads,
)

# Ensure we have the same result
with out_buffer.map_to_host() as host_buffer:
    print(host_buffer)
```

```text
thread: 0 value: 6
thread: 1 value: 6
thread: 2 value: 6
thread: 3 value: 6
HostBuffer([6, 22, 38, 54])
```

You can see in the output that the first block had the values [0 1 2 3] and was
reduced from top to bottom (shuffle down) in this way, where the sum result of
one thread is passed to the next thread down:

| Thread | value | next_value | result |
|--------|-------|------------|--------|
| 3      | 3     | N/A        | 3      |
| 2      | 2     | 3          | 5      |
| 1      | 1     | 5          | 6      |
| 0      | 0     | 6          | 6      |

## Exercise

Now that we've covered some of the core primitives for GPU programming, here's
an exercise to solidify your understanding. Feel free to revisit the examples as
you work through it the first time, then challenge yourself to write the code
independently. Experimenting with the code and observing the results is also a
highly valuable way to deepen your skills, don't hesitate to tweak things and
see what happens!

1. Create a host buffer for the input of `DType` `Float32`, with 32 elements,
and initialize the numbers ordered sequentially. Copy the host buffer to the
device.
2. Create a in_tensor that wraps the host buffer, with the dimensions (8, 4)
3. Create an host and device buffer for the output of `DType` `Float32`, with 8
elements, don't forget to zero the values with `enqueue_fill()`.
4. Launch a GPU kernel with 8 blocks and 4 threads that reduce sums the values,
using your preferred method to write to the output buffer.
5. Copy the device buffer to the host buffer, and print it out on the CPU.

<details>
  <summary>Click to expand answer.</summary>

```mojo :reset
from gpu import thread_idx, block_idx, warp
from gpu.host import DeviceContext
from layout import Layout, LayoutTensor
from math import iota

alias dtype = DType.float32
alias blocks = 8
alias threads = 4
alias elements_in = blocks * threads

# Create context
var ctx = DeviceContext()

# Create buffers
var in_buffer = ctx.enqueue_create_buffer[dtype](elements_in)
var out_buffer = ctx.enqueue_create_buffer[dtype](blocks)

# Fill in input values sequentially and copy to device
with in_buffer.map_to_host() as host_buffer:
    iota(host_buffer.unsafe_ptr(), elements_in)

# Zero output buffer values
_ = out_buffer.enqueue_fill(0)

# Create the LayoutTensors
alias layout = Layout.row_major(blocks, threads)
alias InTensor = LayoutTensor[dtype, layout, MutableAnyOrigin]
var in_tensor = InTensor(in_buffer)

alias out_layout = Layout.row_major(blocks)
alias OutTensor = LayoutTensor[dtype, out_layout, MutableAnyOrigin]
var out_tensor = OutTensor(out_buffer)

fn reduce_sum(in_tensor: InTensor, out_tensor: OutTensor):
    var value = in_tensor.load[1](block_idx.x, thread_idx.x)
    value = warp.sum(value)
    if thread_idx.x == 0:
        out_tensor[block_idx.x] = value

ctx.enqueue_function[reduce_sum](
    in_tensor,
    out_tensor,
    grid_dim=blocks,
    block_dim=threads,
)

with out_buffer.map_to_host() as host_buffer:
    print(host_buffer)
```

```text
HostBuffer([6.0, 22.0, 38.0, 54.0, 70.0, 86.0, 102.0, 118.0])
```

</details>

## Next steps

To continue learning how to write Mojo for GPUs, check out the following
resources:

- [GPU puzzles](https://builds.modular.com/puzzles/):
  Learn how to program for GPUs by solving increasingly challenging puzzles.

- [Build custom ops for GPUs](/max/tutorials/build-custom-ops): Learn to write
  custom ops for MAX graphs that can execute on both CPUs and GPUs.

- [GPU programming examples in
  GitHub](https://github.com/modular/modular/tree/main/examples/mojo/gpu-functions):
  A variety of GPU code examples, from simple to complex.
