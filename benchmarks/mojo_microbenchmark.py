"""
Mojo Micro-benchmark for AURA TDA
==================================
This benchmark compares Python vs Mojo implementations of the hottest
TDA computation loops to make data-driven decisions about optimization.
"""

import numpy as np
import time
import pytest
import pandas as pd
import matplotlib.pyplot as plt
from typing import List, Tuple, Dict, Any
import subprocess
import os
import json
from pathlib import Path
import ctypes


class TDABenchmark:
    """Benchmark suite for TDA computations"""
    
    def __init__(self):
        self.results = []
        self.mojo_available = self._check_mojo_available()
    
    def _check_mojo_available(self) -> bool:
        """Check if Mojo compiler is available"""
        try:
            result = subprocess.run(['mojo', '--version'], 
                                  capture_output=True, text=True)
            return result.returncode == 0
        except FileNotFoundError:
            return False
    
    def python_witness_complex_kernel(self, 
                                    points: np.ndarray, 
                                    landmarks: np.ndarray, 
                                    max_distance_sq: float) -> set:
        """
        Pure Python implementation of witness complex construction.
        This is the hottest loop in TDA computation.
        """
        witnesses = set()
        n_points = len(points)
        n_landmarks = len(landmarks)
        
        for p_idx in range(n_points):
            point = points[p_idx]
            min_dist = float('inf')
            closest_lm = -1
            
            # Find closest landmark
            for l_idx in range(n_landmarks):
                landmark = landmarks[l_idx]
                
                # Compute squared distance
                dist_sq = 0.0
                for d in range(3):  # Assuming 3D points
                    diff = point[d] - landmark[d]
                    dist_sq += diff * diff
                
                if dist_sq < min_dist:
                    min_dist = dist_sq
                    closest_lm = l_idx
            
            # Add witness if within threshold
            if min_dist <= max_distance_sq:
                witnesses.add((closest_lm, p_idx))
        
        return witnesses
    
    def numpy_witness_complex_kernel(self,
                                   points: np.ndarray,
                                   landmarks: np.ndarray,
                                   max_distance_sq: float) -> set:
        """NumPy-optimized version using broadcasting"""
        # Compute all pairwise distances at once
        # points: (n_points, 3), landmarks: (n_landmarks, 3)
        # Result: (n_points, n_landmarks)
        diff = points[:, np.newaxis, :] - landmarks[np.newaxis, :, :]
        dist_sq = np.sum(diff ** 2, axis=2)
        
        # Find closest landmark for each point
        closest_landmarks = np.argmin(dist_sq, axis=1)
        min_distances = np.min(dist_sq, axis=1)
        
        # Create witnesses
        witnesses = set()
        for p_idx, (closest_lm, min_dist) in enumerate(
            zip(closest_landmarks, min_distances)
        ):
            if min_dist <= max_distance_sq:
                witnesses.add((int(closest_lm), p_idx))
        
        return witnesses
    
    def compile_mojo_kernel(self) -> bool:
        """Compile the Mojo kernel"""
        if not self.mojo_available:
            return False
        
        mojo_code = '''
from python import Python
from memory import UnsafePointer
from algorithm import vectorize
from sys.intrinsics import strided_load
import math

@always_inline
fn squared_distance(
    p1: UnsafePointer[Float64],
    p2: UnsafePointer[Float64],
    dim: Int
) -> Float64:
    """Compute squared Euclidean distance"""
    var dist: Float64 = 0.0
    
    @parameter
    fn vec_dist[simd_width: Int](idx: Int) -> None:
        let diff = strided_load[simd_width](p1, idx) - strided_load[simd_width](p2, idx)
        dist += (diff * diff).reduce_add()
    
    vectorize[vec_dist, 4](dim)
    return dist

fn mojo_witness_complex_kernel(
    points_ptr: UnsafePointer[Float64],
    num_points: Int,
    landmarks_ptr: UnsafePointer[Float64],
    num_landmarks: Int,
    dim: Int,
    max_dist_sq: Float64,
) -> PythonObject:
    """Mojo implementation with SIMD optimization"""
    let py = Python.import_module("builtins")
    let witnesses = py.set()
    
    # Process each point
    for p_idx in range(num_points):
        var min_dist: Float64 = 1e308  # ~inf
        var closest_lm: Int = -1
        let p_ptr = points_ptr + p_idx * dim
        
        # Find closest landmark
        for l_idx in range(num_landmarks):
            let l_ptr = landmarks_ptr + l_idx * dim
            let dist_sq = squared_distance(p_ptr, l_ptr, dim)
            
            if dist_sq < min_dist:
                min_dist = dist_sq
                closest_lm = l_idx
        
        # Add witness if close enough
        if min_dist <= max_dist_sq:
            let witness_tuple = py.tuple((py.int(closest_lm), py.int(p_idx)))
            witnesses.add(witness_tuple)
    
    return witnesses

# Additional optimized version using parallel processing
fn mojo_witness_complex_parallel(
    points_ptr: UnsafePointer[Float64],
    num_points: Int,
    landmarks_ptr: UnsafePointer[Float64],
    num_landmarks: Int,
    dim: Int,
    max_dist_sq: Float64,
) -> PythonObject:
    """Parallel Mojo implementation"""
    let py = Python.import_module("builtins")
    let witnesses = py.set()
    let lock = py.threading.Lock()
    
    @parameter
    fn process_point(p_idx: Int) -> None:
        var min_dist: Float64 = 1e308
        var closest_lm: Int = -1
        let p_ptr = points_ptr + p_idx * dim
        
        for l_idx in range(num_landmarks):
            let l_ptr = landmarks_ptr + l_idx * dim
            let dist_sq = squared_distance(p_ptr, l_ptr, dim)
            
            if dist_sq < min_dist:
                min_dist = dist_sq
                closest_lm = l_idx
        
        if min_dist <= max_dist_sq:
            with lock:
                let witness_tuple = py.tuple((py.int(closest_lm), py.int(p_idx)))
                witnesses.add(witness_tuple)
    
    # Parallel execution
    parallelize[process_point](num_points)
    
    return witnesses
'''
        
        # Write Mojo code to file
        mojo_file = Path("witness_kernel.mojo")
        mojo_file.write_text(mojo_code)
        
        # Compile to Python module
        try:
            result = subprocess.run(
                ['mojo', 'build', '--format', 'package', 'witness_kernel.mojo'],
                capture_output=True,
                text=True
            )
            return result.returncode == 0
        except Exception as e:
            print(f"Mojo compilation failed: {e}")
            return False
    
    def run_benchmark(self, n_points: int = 5000, n_landmarks: int = 100) -> Dict[str, Any]:
        """Run comprehensive benchmark comparing implementations"""
        # Generate test data
        np.random.seed(42)
        points = np.random.rand(n_points, 3).astype(np.float64) * 10
        landmarks = np.random.rand(n_landmarks, 3).astype(np.float64) * 10
        max_dist_sq = 2.0
        
        results = {}
        
        # Benchmark Python implementation
        start = time.perf_counter()
        py_witnesses = self.python_witness_complex_kernel(points, landmarks, max_dist_sq)
        py_time = time.perf_counter() - start
        results['python'] = {
            'time': py_time,
            'witnesses': len(py_witnesses),
            'speedup': 1.0
        }
        
        # Benchmark NumPy implementation
        start = time.perf_counter()
        np_witnesses = self.numpy_witness_complex_kernel(points, landmarks, max_dist_sq)
        np_time = time.perf_counter() - start
        results['numpy'] = {
            'time': np_time,
            'witnesses': len(np_witnesses),
            'speedup': py_time / np_time
        }
        
        # Verify correctness
        assert py_witnesses == np_witnesses, "NumPy results don't match Python"
        
        # Benchmark Mojo if available
        if self.mojo_available and self.compile_mojo_kernel():
            try:
                import witness_kernel
                
                # Prepare data for Mojo
                points_ptr = points.ctypes.data_as(ctypes.POINTER(ctypes.c_double))
                landmarks_ptr = landmarks.ctypes.data_as(ctypes.POINTER(ctypes.c_double))
                
                # Benchmark standard Mojo
                start = time.perf_counter()
                mojo_witnesses = witness_kernel.mojo_witness_complex_kernel(
                    points_ptr, n_points,
                    landmarks_ptr, n_landmarks,
                    3, max_dist_sq
                )
                mojo_time = time.perf_counter() - start
                
                results['mojo'] = {
                    'time': mojo_time,
                    'witnesses': len(mojo_witnesses),
                    'speedup': py_time / mojo_time
                }
                
                # Benchmark parallel Mojo
                start = time.perf_counter()
                mojo_par_witnesses = witness_kernel.mojo_witness_complex_parallel(
                    points_ptr, n_points,
                    landmarks_ptr, n_landmarks,
                    3, max_dist_sq
                )
                mojo_par_time = time.perf_counter() - start
                
                results['mojo_parallel'] = {
                    'time': mojo_par_time,
                    'witnesses': len(mojo_par_witnesses),
                    'speedup': py_time / mojo_par_time
                }
                
            except ImportError:
                print("Mojo module import failed")
        
        return results
    
    def run_scaling_benchmark(self) -> pd.DataFrame:
        """Test performance scaling with problem size"""
        sizes = [1000, 2500, 5000, 10000, 20000]
        all_results = []
        
        for size in sizes:
            print(f"Benchmarking with {size} points...")
            results = self.run_benchmark(n_points=size)
            
            for impl, data in results.items():
                all_results.append({
                    'implementation': impl,
                    'n_points': size,
                    'time': data['time'],
                    'speedup': data['speedup']
                })
        
        return pd.DataFrame(all_results)
    
    def plot_results(self, df: pd.DataFrame):
        """Create visualization of benchmark results"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # Plot execution times
        for impl in df['implementation'].unique():
            impl_data = df[df['implementation'] == impl]
            ax1.plot(impl_data['n_points'], impl_data['time'], 
                    marker='o', label=impl)
        
        ax1.set_xlabel('Number of Points')
        ax1.set_ylabel('Execution Time (seconds)')
        ax1.set_title('TDA Kernel Performance Scaling')
        ax1.set_xscale('log')
        ax1.set_yscale('log')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Plot speedups
        for impl in df['implementation'].unique():
            if impl != 'python':
                impl_data = df[df['implementation'] == impl]
                ax2.plot(impl_data['n_points'], impl_data['speedup'], 
                        marker='o', label=f'{impl} vs Python')
        
        ax2.set_xlabel('Number of Points')
        ax2.set_ylabel('Speedup Factor')
        ax2.set_title('Performance Improvement vs Pure Python')
        ax2.set_xscale('log')
        ax2.axhline(y=2.5, color='r', linestyle='--', 
                   label='Decision Threshold (2.5x)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('mojo_benchmark_results.png', dpi=150)
        plt.show()
    
    def generate_decision_report(self, df: pd.DataFrame) -> str:
        """Generate decision report based on benchmark results"""
        report = ["# Mojo Performance Benchmark Report\n"]
        report.append(f"Date: {pd.Timestamp.now()}\n")
        
        # Calculate average speedups
        avg_speedups = df.groupby('implementation')['speedup'].mean()
        
        report.append("## Average Speedup Factors:\n")
        for impl, speedup in avg_speedups.items():
            if impl != 'python':
                report.append(f"- {impl}: {speedup:.2f}x faster than Python\n")
        
        # Decision logic
        report.append("\n## Recommendation:\n")
        
        if 'mojo' in avg_speedups:
            mojo_speedup = avg_speedups['mojo']
            if mojo_speedup > 2.5:
                report.append(f"✅ **ADOPT MOJO**: {mojo_speedup:.2f}x speedup exceeds 2.5x threshold\n")
                report.append("- Allocate sprint for full TDA module port\n")
                report.append("- Expected ROI: Significant performance gains\n")
            else:
                report.append(f"⏸️ **DEFER MOJO**: {mojo_speedup:.2f}x speedup below 2.5x threshold\n")
                report.append("- Continue with NumPy optimizations\n")
                report.append("- Re-evaluate in 6 months\n")
        else:
            report.append("❌ **MOJO NOT AVAILABLE**: Cannot make decision\n")
            report.append("- Install Mojo SDK to run benchmarks\n")
        
        # Additional insights
        report.append("\n## Performance Insights:\n")
        report.append(f"- NumPy provides {avg_speedups.get('numpy', 0):.2f}x speedup\n")
        report.append(f"- Test sizes ranged from {df['n_points'].min()} to {df['n_points'].max()} points\n")
        
        return ''.join(report)


# Pytest integration
@pytest.mark.benchmark(group="tda_kernel")
def test_python_kernel(benchmark):
    """Benchmark Python implementation"""
    bench = TDABenchmark()
    points = np.random.rand(5000, 3) * 10
    landmarks = np.random.rand(100, 3) * 10
    
    result = benchmark(bench.python_witness_complex_kernel, 
                      points, landmarks, 2.0)
    assert len(result) > 0


@pytest.mark.benchmark(group="tda_kernel")
def test_numpy_kernel(benchmark):
    """Benchmark NumPy implementation"""
    bench = TDABenchmark()
    points = np.random.rand(5000, 3) * 10
    landmarks = np.random.rand(100, 3) * 10
    
    result = benchmark(bench.numpy_witness_complex_kernel, 
                      points, landmarks, 2.0)
    assert len(result) > 0


def main():
    """Run full benchmark suite and generate report"""
    print("🚀 AURA TDA Mojo Micro-benchmark")
    print("=" * 50)
    
    bench = TDABenchmark()
    
    # Check Mojo availability
    if bench.mojo_available:
        print("✅ Mojo compiler detected")
    else:
        print("❌ Mojo compiler not found - will benchmark Python/NumPy only")
    
    # Run scaling benchmark
    print("\nRunning scaling benchmarks...")
    df = bench.run_scaling_benchmark()
    
    # Save results
    df.to_csv('mojo_benchmark_results.csv', index=False)
    print("\nResults saved to: mojo_benchmark_results.csv")
    
    # Generate plots
    print("\nGenerating performance plots...")
    bench.plot_results(df)
    
    # Generate decision report
    report = bench.generate_decision_report(df)
    with open('mojo_decision_report.md', 'w') as f:
        f.write(report)
    
    print("\nDecision report saved to: mojo_decision_report.md")
    print("\n" + report)
    
    # CI/CD integration output
    if 'CI' in os.environ:
        # Output for GitHub Actions
        if 'mojo' in df['implementation'].values:
            avg_speedup = df[df['implementation'] == 'mojo']['speedup'].mean()
            print(f"::set-output name=mojo_speedup::{avg_speedup:.2f}")
            print(f"::set-output name=mojo_decision::{'adopt' if avg_speedup > 2.5 else 'defer'}")


if __name__ == "__main__":
    main()