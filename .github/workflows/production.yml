name: Production CI/CD Pipeline

on:
  push:
    branches: [main, integration-dev-env]
  pull_request:
    branches: [main]
  release:
    types: [created]

env:
  REGISTRY: gcr.io
  IMAGE_NAME: geometric-intelligence
  RUST_VERSION: 1.75.0
  PYTHON_VERSION: 3.11
  NODE_VERSION: 18

jobs:
  # ============================================================================
  # TESTING PHASE
  # ============================================================================
  
  test-python:
    name: Python Tests
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov pytest-asyncio pytest-benchmark
    
    - name: Run unit tests
      run: |
        pytest tests/test_complete_system.py \
          --cov=geometric_core \
          --cov=retrieval \
          --cov=agents \
          --cov=embeddings \
          --cov-report=xml \
          --cov-report=term-missing \
          --benchmark-disable
    
    - name: Run integration tests
      run: |
        python tests/test_system_simulation.py
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: python
        name: Python Coverage
    
    - name: Performance benchmarks
      run: |
        pytest tests/test_retrieval_benchmarks.py \
          --benchmark-only \
          --benchmark-json=benchmark_results.json
    
    - name: Check performance regression
      run: |
        python scripts/check_regression.py \
          --baseline=benchmark_baseline.json \
          --current=benchmark_results.json \
          --threshold=0.05
      continue-on-error: true

  test-rust:
    name: Rust Tests
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Rust
      uses: dtolnay/rust-toolchain@stable
      with:
        toolchain: ${{ env.RUST_VERSION }}
        components: rustfmt, clippy
    
    - name: Cache Rust dependencies
      uses: Swatinem/rust-cache@v2
      with:
        workspaces: rust_core
    
    - name: Check formatting
      run: |
        cd rust_core
        cargo fmt -- --check
    
    - name: Run clippy
      run: |
        cd rust_core
        cargo clippy --all-features -- -D warnings
    
    - name: Run tests
      run: |
        cd rust_core
        cargo test --all-features --verbose
    
    - name: Build release
      run: |
        cd rust_core
        cargo build --release --all-features
    
    - name: Run benchmarks
      run: |
        cd rust_core
        cargo bench --all-features

  test-helm:
    name: Helm Chart Tests
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Helm
      uses: azure/setup-helm@v3
      with:
        version: '3.13.0'
    
    - name: Lint Helm chart
      run: |
        helm lint k8s/helm/geometric-intelligence
    
    - name: Template Helm chart
      run: |
        helm template geometric-intelligence k8s/helm/geometric-intelligence \
          --values k8s/helm/geometric-intelligence/values.yaml \
          > /tmp/rendered.yaml
    
    - name: Validate Kubernetes manifests
      uses: instrumenta/kubeval-action@master
      with:
        files: /tmp/rendered.yaml

  security-scan:
    name: Security Scanning
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy results to GitHub Security
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'
    
    - name: Python dependency check
      run: |
        pip install safety
        safety check --json

  # ============================================================================
  # BUILD PHASE
  # ============================================================================
  
  build-docker:
    name: Build Docker Image
    needs: [test-python, test-rust, test-helm]
    runs-on: ubuntu-latest
    
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Log in to registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: _json_key
        password: ${{ secrets.GCP_SA_KEY }}
    
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ secrets.GCP_PROJECT }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=sha,prefix={{branch}}-
    
    - name: Build and push Docker image
      id: build
      uses: docker/build-push-action@v5
      with:
        context: .
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        build-args: |
          RUST_VERSION=${{ env.RUST_VERSION }}
          PYTHON_VERSION=${{ env.PYTHON_VERSION }}

  # ============================================================================
  # DEPLOY TO STAGING
  # ============================================================================
  
  deploy-staging:
    name: Deploy to Staging
    needs: build-docker
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/integration-dev-env'
    environment:
      name: staging
      url: https://staging.geometric-ai.example.com
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'v1.28.0'
    
    - name: Authenticate to GKE
      uses: google-github-actions/auth@v1
      with:
        credentials_json: ${{ secrets.GCP_SA_KEY }}
    
    - name: Get GKE credentials
      uses: google-github-actions/get-gke-credentials@v1
      with:
        cluster_name: geometric-intelligence-staging
        location: us-central1
        project_id: ${{ secrets.GCP_PROJECT }}
    
    - name: Deploy with Helm
      run: |
        helm upgrade --install geometric-staging \
          k8s/helm/geometric-intelligence \
          --namespace staging \
          --create-namespace \
          --set image.tag=${{ needs.build-docker.outputs.image-tag }} \
          --set environment=staging \
          --wait \
          --timeout 10m
    
    - name: Run smoke tests
      run: |
        kubectl run smoke-test \
          --image=${{ needs.build-docker.outputs.image-tag }} \
          --namespace=staging \
          --rm -i --restart=Never \
          -- python tests/smoke_test.py
    
    - name: Load test staging
      run: |
        pip install locust
        locust -f tests/load_test.py \
          --host=https://staging.geometric-ai.example.com \
          --users=50 \
          --spawn-rate=5 \
          --time=2m \
          --headless \
          --only-summary \
          --html=staging_load_test.html
    
    - name: Upload load test results
      uses: actions/upload-artifact@v3
      with:
        name: staging-load-test
        path: staging_load_test.html

  # ============================================================================
  # CANARY DEPLOYMENT
  # ============================================================================
  
  deploy-canary:
    name: Canary Deployment
    needs: [build-docker, deploy-staging]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment:
      name: production-canary
      url: https://canary.geometric-ai.example.com
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Authenticate to GKE
      uses: google-github-actions/auth@v1
      with:
        credentials_json: ${{ secrets.GCP_SA_KEY }}
    
    - name: Get GKE credentials
      uses: google-github-actions/get-gke-credentials@v1
      with:
        cluster_name: geometric-intelligence-prod
        location: us-central1
        project_id: ${{ secrets.GCP_PROJECT }}
    
    - name: Deploy canary
      run: |
        helm upgrade --install geometric-canary \
          k8s/helm/geometric-intelligence \
          --namespace production \
          --set image.tag=${{ needs.build-docker.outputs.image-tag }} \
          --set canary.enabled=true \
          --set canary.weight=10 \
          --set replicaCount=2 \
          --wait \
          --timeout 5m
    
    - name: Monitor canary metrics
      run: |
        python scripts/monitor_canary.py \
          --duration=10m \
          --error-threshold=0.01 \
          --latency-p95-threshold=100 \
          --prometheus-url=${{ secrets.PROMETHEUS_URL }}
    
    - name: Canary analysis
      id: canary
      run: |
        python scripts/analyze_canary.py \
          --baseline-tag=production \
          --canary-tag=${{ needs.build-docker.outputs.image-tag }} \
          --metrics-window=10m
      continue-on-error: true
    
    - name: Rollback canary if failed
      if: steps.canary.outcome == 'failure'
      run: |
        helm rollback geometric-canary 0 --namespace production
        exit 1

  # ============================================================================
  # PRODUCTION DEPLOYMENT
  # ============================================================================
  
  deploy-production:
    name: Deploy to Production
    needs: [build-docker, deploy-canary]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment:
      name: production
      url: https://api.geometric-ai.example.com
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Authenticate to GKE
      uses: google-github-actions/auth@v1
      with:
        credentials_json: ${{ secrets.GCP_SA_KEY }}
    
    - name: Get GKE credentials
      uses: google-github-actions/get-gke-credentials@v1
      with:
        cluster_name: geometric-intelligence-prod
        location: us-central1
        project_id: ${{ secrets.GCP_PROJECT }}
    
    - name: Blue-green deployment
      run: |
        # Deploy new version as blue
        helm upgrade --install geometric-blue \
          k8s/helm/geometric-intelligence \
          --namespace production \
          --set image.tag=${{ needs.build-docker.outputs.image-tag }} \
          --set service.selector=blue \
          --set replicaCount=10 \
          --wait \
          --timeout 10m
        
        # Switch traffic to blue
        kubectl patch service geometric-intelligence \
          --namespace production \
          --patch '{"spec":{"selector":{"version":"blue"}}}'
        
        # Wait for stability
        sleep 60
        
        # Delete old green deployment
        helm delete geometric-green --namespace production || true
    
    - name: Verify deployment
      run: |
        kubectl rollout status deployment/geometric-blue \
          --namespace production \
          --timeout=5m
    
    - name: Run production tests
      run: |
        python tests/production_test.py \
          --url=https://api.geometric-ai.example.com \
          --expected-version=${{ needs.build-docker.outputs.image-tag }}
    
    - name: Update monitoring
      run: |
        python scripts/update_monitoring.py \
          --version=${{ needs.build-docker.outputs.image-tag }} \
          --grafana-url=${{ secrets.GRAFANA_URL }} \
          --grafana-token=${{ secrets.GRAFANA_TOKEN }}

  # ============================================================================
  # POST-DEPLOYMENT
  # ============================================================================
  
  notify:
    name: Notify Deployment
    needs: [deploy-production]
    runs-on: ubuntu-latest
    if: always()
    
    steps:
    - name: Send Slack notification
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        text: |
          Deployment ${{ job.status }}
          Repository: ${{ github.repository }}
          Branch: ${{ github.ref }}
          Commit: ${{ github.sha }}
          Author: ${{ github.actor }}
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
    
    - name: Create deployment record
      if: success()
      run: |
        curl -X POST ${{ secrets.DEPLOYMENT_API }} \
          -H "Authorization: Bearer ${{ secrets.API_TOKEN }}" \
          -H "Content-Type: application/json" \
          -d '{
            "version": "${{ needs.build-docker.outputs.image-tag }}",
            "environment": "production",
            "timestamp": "'$(date -u +%Y-%m-%dT%H:%M:%SZ)'",
            "commit": "${{ github.sha }}",
            "author": "${{ github.actor }}"
          }'