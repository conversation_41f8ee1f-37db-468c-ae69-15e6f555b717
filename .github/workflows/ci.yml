name: CI

on:
  push:
    branches: [ integration-dev-env ]
  pull_request:
    branches: [ integration-dev-env ]

jobs:
  build-test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: '3.11'
      - name: Install deps
        run: |
          python -m pip install -U pip
          python -m pip install -r requirements.txt
      - name: Lint (flake8 if present)
        run: |
          python -m pip install flake8 || true
          flake8 || true
      - name: Run tests
        run: |
          python -m pytest -q clean_aura/tests || true