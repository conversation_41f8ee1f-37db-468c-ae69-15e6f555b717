#!/usr/bin/env python3
"""
Mojo Performance Validation Script
Final check: Does Mojo deliver the promised 10x speedup?

This script provides the definitive answer to whether the Mojo implementation
meets the performance requirements for geometric intelligence.
"""
import sys
import os
import numpy as np
import time
from pathlib import Path
from typing import Dict, Tuple, Optional
import json

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class MojoValidator:
    """
    Final validation of Mojo performance claims.
    Pass/Fail determination for the geometric intelligence project.
    """
    
    def __init__(self):
        self.validation_results = {}
        self.backend = None
        
        # Try to import the backend
        try:
            from geo_engine_final.compute.gpu_backend import GPUBackend
            self.backend = GPUBackend()
            print("✅ GPUBackend loaded successfully")
        except Exception as e:
            print(f"❌ Failed to load GPUBackend: {e}")
            self.backend = None
    
    def validate_correctness(self, n_points: int = 100) -> bool:
        """
        Validate that <PERSON><PERSON> produces correct results.
        Compare against NumPy ground truth.
        """
        print(f"\n🔍 Validating correctness ({n_points} points)...")
        
        if not self.backend:
            print("❌ No backend available")
            return False
        
        # Generate test data
        np.random.seed(42)
        points = np.random.randn(n_points, 3).astype(np.float32)
        
        try:
            # Get results from different backends
            numpy_result = self.backend.run_distance_matrix(points, backend='numpy')
            numpy_distances = numpy_result['distances']
            
            if 'mojo' in self.backend.available_backends:
                mojo_result = self.backend.run_distance_matrix(points, backend='mojo')
                mojo_distances = mojo_result['distances']
                
                # Compare results
                max_diff = np.max(np.abs(numpy_distances - mojo_distances))
                relative_error = max_diff / np.max(numpy_distances)
                
                print(f"   Max absolute difference: {max_diff:.6f}")
                print(f"   Relative error: {relative_error:.6f}")
                
                # Tolerance check
                tolerance = 1e-4
                is_correct = relative_error < tolerance
                
                if is_correct:
                    print("   ✅ Correctness validated")
                else:
                    print(f"   ❌ Correctness failed (error > {tolerance})")
                
                return is_correct
            else:
                print("   ⚠️  Mojo backend not available, skipping correctness check")
                return True
                
        except Exception as e:
            print(f"   ❌ Correctness validation failed: {e}")
            return False
    
    def validate_performance(self) -> Dict[str, float]:
        """
        Validate performance across different problem sizes.
        Returns speedup factors for each test size.
        """
        print(f"\n⚡ Validating performance...")
        
        if not self.backend:
            print("❌ No backend available")
            return {}
        
        test_sizes = [100, 500, 1000]
        speedups = {}
        
        for n_points in test_sizes:
            print(f"\n   Testing {n_points} points...")
            
            # Generate test data
            np.random.seed(42)
            points = np.random.randn(n_points, 3).astype(np.float32)
            
            try:
                # Benchmark NumPy
                numpy_times = []
                for _ in range(3):
                    result = self.backend.run_distance_matrix(points, backend='numpy')
                    numpy_times.append(result['time_ms'])
                numpy_time = min(numpy_times)
                
                # Benchmark Mojo (if available)
                if 'mojo' in self.backend.available_backends:
                    mojo_times = []
                    for _ in range(3):
                        result = self.backend.run_distance_matrix(points, backend='mojo')
                        mojo_times.append(result['time_ms'])
                    mojo_time = min(mojo_times)
                    
                    speedup = numpy_time / mojo_time if mojo_time > 0 else 0
                    speedups[n_points] = speedup
                    
                    print(f"     NumPy: {numpy_time:.2f}ms")
                    print(f"     Mojo:  {mojo_time:.2f}ms")
                    print(f"     Speedup: {speedup:.1f}x")
                    
                    if speedup >= 10.0:
                        print("     ✅ Target achieved!")
                    elif speedup >= 5.0:
                        print("     🟡 Good progress")
                    else:
                        print("     🔴 Needs improvement")
                else:
                    print("     ⚠️  Mojo not available")
                    speedups[n_points] = 0
                    
            except Exception as e:
                print(f"     ❌ Performance test failed: {e}")
                speedups[n_points] = 0
        
        return speedups
    
    def validate_scalability(self) -> bool:
        """
        Validate that performance scales appropriately.
        Check that larger problems don't degrade disproportionately.
        """
        print(f"\n📈 Validating scalability...")
        
        if not self.backend or 'mojo' not in self.backend.available_backends:
            print("   ⚠️  Mojo not available, skipping scalability test")
            return True
        
        test_sizes = [100, 500, 1000, 2000]
        times_per_element = []
        
        for n_points in test_sizes:
            print(f"   Testing {n_points} points...")
            
            # Generate test data
            np.random.seed(42)
            points = np.random.randn(n_points, 3).astype(np.float32)
            
            try:
                result = self.backend.run_distance_matrix(points, backend='mojo')
                time_ms = result['time_ms']
                
                # Calculate time per distance computation
                n_distances = n_points * (n_points - 1) // 2
                time_per_distance = time_ms / n_distances
                times_per_element.append(time_per_distance)
                
                print(f"     Time: {time_ms:.2f}ms")
                print(f"     Time per distance: {time_per_distance:.6f}ms")
                
            except Exception as e:
                print(f"     ❌ Scalability test failed: {e}")
                return False
        
        # Check if scaling is reasonable (should be roughly constant or sub-linear)
        if len(times_per_element) >= 2:
            scaling_factor = times_per_element[-1] / times_per_element[0]
            print(f"   Scaling factor (2000pts vs 100pts): {scaling_factor:.2f}")
            
            if scaling_factor < 5.0:  # Allow some degradation
                print("   ✅ Scalability acceptable")
                return True
            else:
                print("   ❌ Poor scalability")
                return False
        
        return True
    
    def run_full_validation(self) -> Dict[str, any]:
        """
        Run complete validation suite.
        Returns comprehensive results.
        """
        print("🎯 MOJO PERFORMANCE VALIDATION")
        print("=" * 50)
        print("Mission: Prove 10x speedup for geometric intelligence")
        print("Target: 1000 points in <450ms (vs 4500ms NumPy)")
        
        # Run all validation tests
        correctness_passed = self.validate_correctness()
        performance_speedups = self.validate_performance()
        scalability_passed = self.validate_scalability()
        
        # Determine overall result
        target_speedup = performance_speedups.get(1000, 0)
        mission_success = (
            correctness_passed and 
            target_speedup >= 10.0 and 
            scalability_passed
        )
        
        # Compile results (ensure JSON serializable)
        results = {
            'timestamp': time.time(),
            'correctness_passed': bool(correctness_passed),
            'performance_speedups': performance_speedups,
            'scalability_passed': bool(scalability_passed),
            'target_speedup_1000pts': float(target_speedup),
            'mission_success': bool(mission_success),
            'available_backends': self.backend.available_backends if self.backend else []
        }
        
        self.validation_results = results
        return results
    
    def print_final_verdict(self):
        """Print the final pass/fail verdict."""
        print("\n" + "=" * 50)
        print("🏆 FINAL VERDICT")
        print("=" * 50)
        
        if not self.validation_results:
            print("❌ No validation results available")
            return
        
        results = self.validation_results
        
        print(f"Correctness: {'✅ PASS' if results['correctness_passed'] else '❌ FAIL'}")
        print(f"Target Speedup (1000pts): {results['target_speedup_1000pts']:.1f}x")
        print(f"Scalability: {'✅ PASS' if results['scalability_passed'] else '❌ FAIL'}")
        
        if results['mission_success']:
            print("\n🎉 MISSION SUCCESS!")
            print("✅ Mojo delivers 10x speedup")
            print("✅ Geometric intelligence vision validated")
            print("🚀 Ready for next phase:")
            print("   - Port hyperbolic embeddings")
            print("   - Implement LNN feedback loop")
            print("   - Scale to full Ripser algorithm")
        else:
            print("\n❌ MISSION FAILED")
            print("Mojo did not achieve 10x speedup target")
            print("🔄 Recommended actions:")
            
            if results['target_speedup_1000pts'] < 2.0:
                print("   - Debug Mojo implementation")
                print("   - Check SIMD utilization")
                print("   - Verify parallelization")
            elif results['target_speedup_1000pts'] < 5.0:
                print("   - Optimize memory access patterns")
                print("   - Tune SIMD width")
                print("   - Profile bottlenecks")
            else:
                print("   - Fine-tune kernel parameters")
                print("   - Consider C++/CUDA fallback")
        
        print(f"\nDetailed results saved to validation_results.json")
    
    def save_results(self, filename: str = "validation_results.json"):
        """Save validation results to file."""
        output_path = Path(__file__).parent / filename
        
        with open(output_path, 'w') as f:
            json.dump(self.validation_results, f, indent=2)
        
        print(f"💾 Results saved to: {output_path}")

def main():
    """Main validation execution."""
    validator = MojoValidator()
    validator.run_full_validation()
    validator.print_final_verdict()
    validator.save_results()
    
    # Return exit code based on success
    return 0 if validator.validation_results.get('mission_success', False) else 1

if __name__ == "__main__":
    exit(main())
