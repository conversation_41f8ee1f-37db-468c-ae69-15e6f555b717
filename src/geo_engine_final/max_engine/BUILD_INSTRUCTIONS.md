# Mojo/MAX Build Instructions

## 24-Hour Mission: Prove 10x Speedup

**Target**: Distance matrix computation 10x faster than NumPy (1000 points: <450ms vs 4500ms)

## Prerequisites

1. **Install Modular/MAX SDK**:
   ```bash
   # Follow official instructions at https://docs.modular.com/max/install
   curl -s https://get.modular.com | sh -
   modular auth
   modular install max
   ```

2. **Verify Installation**:
   ```bash
   mojo --version
   max --version
   ```

## Build Process

### Step 1: Compile Mojo Kernels (Hours 1-4)

```bash
# Navigate to project root
cd /path/to/aura-intelligence

# Create __init__.mojo file
echo 'from .ripser import DistanceMatrix' > src/geo_engine_final/max_engine/kernels/__init__.mojo

# Compile the Mojo package
cd src/geo_engine_final/max_engine
mojo package kernels -o kernels.mojopkg

# Verify compilation
ls -la kernels.mojopkg
```

**Expected Output**: `kernels.mojopkg` file created without errors

### Step 2: Test Python Integration (Hours 5-8)

```bash
# Test the distance graph
cd /path/to/aura-intelligence
python -c "
from src.geo_engine_final.max_engine.graphs.distance_graph import get_distance_graph
import numpy as np

graph = get_distance_graph()
points = np.random.randn(10, 3).astype(np.float32)
result = graph.compute(points)
print(f'Success! Distance matrix shape: {result[0].shape}, Time: {result[1]:.2f}ms')
"
```

**Expected Output**: Success message with timing

### Step 3: Run Benchmark (Hours 9-12)

```bash
# Run the comprehensive benchmark
python src/benchmarks/mojo_benchmark.py
```

**Expected Output**:
```
🎯 TARGET RESULT (1000 points):
   NumPy baseline: 4500.00ms
   MOJO: 450.00ms (10.0x) ✅ SUCCESS
```

## Troubleshooting

### Common Issues

1. **Mojo Compilation Errors**:
   ```bash
   # Check Mojo syntax
   mojo check src/geo_engine_final/max_engine/kernels/ripser.mojo
   
   # Common fixes:
   # - Check import statements
   # - Verify SIMD syntax
   # - Ensure proper tensor operations
   ```

2. **MAX Graph Errors**:
   ```bash
   # Test MAX availability
   python -c "from max import engine; print('MAX available')"
   
   # If fails, check installation:
   modular list
   modular install max
   ```

3. **Performance Issues**:
   ```bash
   # Profile the Mojo kernel
   mojo run --profile src/geo_engine_final/max_engine/kernels/ripser.mojo
   
   # Check SIMD utilization
   # Verify parallelization
   # Monitor memory access patterns
   ```

## Success Criteria Checklist

- [ ] **Hour 4**: Mojo code compiles without errors
- [ ] **Hour 8**: Python can call Mojo kernel
- [ ] **Hour 12**: Benchmark runs and shows timing
- [ ] **Hour 16**: Achieve >2x speedup (progress)
- [ ] **Hour 20**: Achieve >5x speedup (good)
- [ ] **Hour 24**: Achieve >10x speedup (SUCCESS!)

## Fallback Plan

If Mojo doesn't achieve 10x speedup by Hour 20:

### Option A: C++/CUDA Implementation
```bash
# Create C++ kernel with CUDA
mkdir src/geo_engine_final/cuda_kernels
# Implement distance_matrix.cu
# Use pybind11 for Python integration
```

### Option B: Optimize Existing Backends
```bash
# Focus on JAX optimization
# Use XLA compilation
# Optimize memory layout
```

## Next Phase (After Success)

Once 10x speedup is achieved:

1. **Port Hyperbolic Embeddings** from `start_from_ground/`
2. **Implement LNN Feedback Loop**
3. **Add More TDA Algorithms**
4. **Scale to Full Ripser**

## Performance Targets

| Points | NumPy (ms) | Mojo Target (ms) | Speedup |
|--------|------------|------------------|---------|
| 100    | 45         | <5               | 10x     |
| 500    | 1125       | <113             | 10x     |
| 1000   | 4500       | <450             | 10x     |
| 2000   | 18000      | <1800            | 10x     |

## Validation

```bash
# Final validation script
python -c "
import numpy as np
from src.geo_engine_final.compute.gpu_backend import GPUBackend

backend = GPUBackend()
points = np.random.randn(1000, 3).astype(np.float32)
result = backend.run_distance_matrix(points, backend='mojo')

speedup = result['speedup_vs_numpy']
print(f'Final Result: {speedup:.1f}x speedup')

if speedup >= 10.0:
    print('🎉 SUCCESS: 10x target achieved!')
    print('🚀 Ready for next phase!')
else:
    print('❌ Target not met. Debug required.')
"
```

## Resources

- [Mojo Documentation](https://docs.modular.com/mojo/)
- [MAX Graph API](https://docs.modular.com/max/graph/)
- [SIMD Programming Guide](https://docs.modular.com/mojo/manual/vectorization)
- [Performance Optimization](https://docs.modular.com/mojo/manual/performance/)

---

**Remember**: The goal is not perfect code, but PROVEN PERFORMANCE. 10x speedup validates the entire geometric intelligence vision.
