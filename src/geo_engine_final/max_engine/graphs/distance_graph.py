"""
MAX Graph for Distance Matrix Computation
Bridges Python and Mojo for high-performance distance calculations.
"""
import numpy as np
from typing import Tuple
import time

# Try to import MAX - fallback gracefully if not available
try:
    from max.graph import Graph, TensorType
    from max import engine
    MAX_AVAILABLE = True
except ImportError:
    MAX_AVAILABLE = False
    print("WARNING: MAX not available, using fallback implementation")

class DistanceMatrixGraph:
    """
    MAX Graph wrapper for Mojo distance matrix computation.
    Provides Python interface to high-performance Mojo kernels.
    """
    
    def __init__(self):
        self.max_available = MAX_AVAILABLE
        
        if self.max_available:
            self._init_max_graph()
        else:
            print("Using NumPy fallback for distance matrix")
    
    def _init_max_graph(self):
        """Initialize MAX graph with Mojo kernel."""
        try:
            # Create graph
            self.graph = Graph("distance_matrix")
            self.engine = engine.Engine()
            
            # Define inputs - dynamic shapes for flexibility
            points = self.graph.input(
                "points", 
                TensorType(engine.DType.float32, shape=[-1, -1])
            )
            
            # Add custom Mojo kernel
            distances = self.graph.custom(
                "DistanceMatrix::compute",
                inputs=[points],
                output_type=TensorType(engine.DType.float32, shape=[-1, -1])
            )
            
            # Set output
            self.graph.output(distances, name="distances")
            
            # Compile the graph
            self.model = self.engine.compile(self.graph)
            print("✅ MAX Graph compiled successfully")
            
        except Exception as e:
            print(f"❌ MAX Graph compilation failed: {e}")
            self.max_available = False
    
    def compute(self, points: np.ndarray) -> Tuple[np.ndarray, float]:
        """
        Compute distance matrix with timing.
        
        Args:
            points: Input points [n_points, n_dims]
            
        Returns:
            (distance_matrix, time_ms)
        """
        if self.max_available:
            return self._compute_max(points)
        else:
            return self._compute_numpy_fallback(points)
    
    def _compute_max(self, points: np.ndarray) -> Tuple[np.ndarray, float]:
        """Execute using MAX/Mojo."""
        start = time.perf_counter()
        
        try:
            # Execute the compiled graph
            result = self.model.execute(points=points)
            distances = result['distances']
            
            end = time.perf_counter()
            time_ms = (end - start) * 1000
            
            return distances, time_ms
            
        except Exception as e:
            print(f"❌ MAX execution failed: {e}")
            print("Falling back to NumPy...")
            return self._compute_numpy_fallback(points)
    
    def _compute_numpy_fallback(self, points: np.ndarray) -> Tuple[np.ndarray, float]:
        """Fallback NumPy implementation for comparison."""
        start = time.perf_counter()
        
        n = points.shape[0]
        distances = np.zeros((n, n), dtype=np.float32)
        
        # Naive nested loop implementation (slow but correct)
        for i in range(n):
            for j in range(i + 1, n):
                diff = points[i] - points[j]
                dist = np.sqrt(np.sum(diff * diff))
                distances[i, j] = dist
                distances[j, i] = dist
        
        end = time.perf_counter()
        time_ms = (end - start) * 1000
        
        return distances, time_ms

# Global instance for reuse
_distance_graph = None

def get_distance_graph() -> DistanceMatrixGraph:
    """Get singleton distance graph instance."""
    global _distance_graph
    if _distance_graph is None:
        _distance_graph = DistanceMatrixGraph()
    return _distance_graph
