#!/usr/bin/env python3
"""
Mojo Performance Benchmark - Prove 10x Speedup
Target: Distance matrix computation 10x faster than NumPy baseline.

Usage:
    python src/benchmarks/mojo_benchmark.py
    
Expected Results:
    - NumPy (1000 points): ~4500ms
    - Mojo (1000 points): <450ms (10x speedup)
"""
import sys
import os
import numpy as np
import time
from typing import Dict, List, Tuple
import json
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from geo_engine_final.compute.gpu_backend import GPUBackend
    BACKEND_AVAILABLE = True
except ImportError as e:
    print(f"❌ Cannot import GPUBackend: {e}")
    BACKEND_AVAILABLE = False

class MojoBenchmark:
    """
    Comprehensive benchmark suite for Mojo distance matrix performance.
    """
    
    def __init__(self):
        self.results = []
        self.backend = None
        
        if BACKEND_AVAILABLE:
            try:
                self.backend = GPUBackend()
                print("✅ GPUBackend initialized")
            except Exception as e:
                print(f"❌ GPUBackend initialization failed: {e}")
    
    def generate_test_data(self, n_points: int, n_dims: int = 3, seed: int = 42) -> np.ndarray:
        """Generate reproducible test data."""
        np.random.seed(seed)
        return np.random.randn(n_points, n_dims).astype(np.float32)
    
    def run_single_benchmark(self, n_points: int, n_dims: int = 3) -> Dict:
        """Run benchmark for a single configuration."""
        print(f"\n🔬 Benchmarking {n_points} points, {n_dims} dimensions")
        
        # Generate test data
        points = self.generate_test_data(n_points, n_dims)
        
        if not self.backend:
            print("❌ No backend available, skipping...")
            return {}
        
        # Test all available backends
        results = {
            'n_points': n_points,
            'n_dims': n_dims,
            'backends': {}
        }
        
        for backend_name in self.backend.available_backends:
            try:
                print(f"  Testing {backend_name}...")
                
                # Run multiple times for stable timing
                times = []
                for run in range(3):
                    result = self.backend.run_distance_matrix(points, backend=backend_name)
                    times.append(result['time_ms'])
                
                # Record best time
                best_time = min(times)
                avg_time = sum(times) / len(times)
                
                results['backends'][backend_name] = {
                    'best_time_ms': best_time,
                    'avg_time_ms': avg_time,
                    'all_times': times
                }
                
                print(f"    {backend_name}: {best_time:.2f}ms (best), {avg_time:.2f}ms (avg)")
                
            except Exception as e:
                print(f"    ❌ {backend_name} failed: {e}")
                results['backends'][backend_name] = {'error': str(e)}
        
        # Calculate speedups
        if 'numpy' in results['backends'] and 'best_time_ms' in results['backends']['numpy']:
            numpy_time = results['backends']['numpy']['best_time_ms']
            
            for backend_name, backend_result in results['backends'].items():
                if backend_name != 'numpy' and 'best_time_ms' in backend_result:
                    speedup = numpy_time / backend_result['best_time_ms']
                    backend_result['speedup_vs_numpy'] = speedup
                    
                    if speedup >= 10.0:
                        print(f"    ✅ {backend_name}: {speedup:.1f}x speedup (TARGET ACHIEVED!)")
                    elif speedup >= 2.0:
                        print(f"    🟡 {backend_name}: {speedup:.1f}x speedup (good progress)")
                    else:
                        print(f"    🔴 {backend_name}: {speedup:.1f}x speedup (needs work)")
        
        return results
    
    def run_comprehensive_benchmark(self) -> List[Dict]:
        """Run benchmarks across multiple problem sizes."""
        print("🚀 Starting Comprehensive Mojo Benchmark")
        print("=" * 60)
        
        # Test configurations
        test_configs = [
            (100, 3),    # Small: should be very fast
            (500, 3),    # Medium: good for optimization
            (1000, 3),   # Large: the main target
            (2000, 3),   # XL: stress test
        ]
        
        all_results = []
        
        for n_points, n_dims in test_configs:
            result = self.run_single_benchmark(n_points, n_dims)
            if result:
                all_results.append(result)
                self.results.append(result)
        
        return all_results
    
    def print_summary(self):
        """Print benchmark summary."""
        print("\n" + "=" * 60)
        print("📊 BENCHMARK SUMMARY")
        print("=" * 60)
        
        if not self.results:
            print("❌ No results to summarize")
            return
        
        # Find the 1000-point result (main target)
        target_result = None
        for result in self.results:
            if result.get('n_points') == 1000:
                target_result = result
                break
        
        if target_result:
            print(f"\n🎯 TARGET RESULT (1000 points):")
            backends = target_result.get('backends', {})
            
            if 'numpy' in backends and 'best_time_ms' in backends['numpy']:
                numpy_time = backends['numpy']['best_time_ms']
                print(f"   NumPy baseline: {numpy_time:.2f}ms")
                
                for backend_name in ['mojo', 'jax', 'cupy']:
                    if backend_name in backends and 'best_time_ms' in backends[backend_name]:
                        time_ms = backends[backend_name]['best_time_ms']
                        speedup = backends[backend_name].get('speedup_vs_numpy', 1.0)
                        
                        status = "✅ SUCCESS" if speedup >= 10.0 else "❌ NEEDS WORK"
                        print(f"   {backend_name.upper()}: {time_ms:.2f}ms ({speedup:.1f}x) {status}")
        
        # Overall assessment
        print(f"\n🏆 OVERALL ASSESSMENT:")
        mojo_success = False
        
        if target_result and 'mojo' in target_result.get('backends', {}):
            mojo_result = target_result['backends']['mojo']
            if 'speedup_vs_numpy' in mojo_result and mojo_result['speedup_vs_numpy'] >= 10.0:
                mojo_success = True
        
        if mojo_success:
            print("   ✅ MOJO PROVES 10X SPEEDUP - VISION VALIDATED!")
            print("   🚀 Ready for next phase: Full Ripser implementation")
        else:
            print("   ❌ MOJO TARGET NOT ACHIEVED")
            print("   🔄 Options: Debug Mojo, try C++/CUDA, or optimize further")
    
    def save_results(self, filename: str = "mojo_benchmark_results.json"):
        """Save results to JSON file."""
        output_path = Path(__file__).parent / filename
        
        with open(output_path, 'w') as f:
            json.dump({
                'timestamp': time.time(),
                'results': self.results,
                'summary': self._generate_summary()
            }, f, indent=2)
        
        print(f"\n💾 Results saved to: {output_path}")
    
    def _generate_summary(self) -> Dict:
        """Generate summary statistics."""
        summary = {
            'total_tests': len(self.results),
            'mojo_available': 'mojo' in (self.backend.available_backends if self.backend else []),
            'target_achieved': False
        }
        
        # Check if 10x target was achieved
        for result in self.results:
            if result.get('n_points') == 1000:
                backends = result.get('backends', {})
                if 'mojo' in backends and 'speedup_vs_numpy' in backends['mojo']:
                    if backends['mojo']['speedup_vs_numpy'] >= 10.0:
                        summary['target_achieved'] = True
                        break
        
        return summary

def main():
    """Main benchmark execution."""
    print("🎯 Mojo Distance Matrix Benchmark")
    print("Target: Prove 10x speedup over NumPy baseline")
    print("-" * 50)
    
    if not BACKEND_AVAILABLE:
        print("❌ Cannot run benchmark - GPUBackend not available")
        print("Make sure you're running from the project root:")
        print("  cd /path/to/aura-intelligence")
        print("  python src/benchmarks/mojo_benchmark.py")
        return 1
    
    # Run benchmark
    benchmark = MojoBenchmark()
    benchmark.run_comprehensive_benchmark()
    benchmark.print_summary()
    benchmark.save_results()
    
    return 0

if __name__ == "__main__":
    exit(main())
