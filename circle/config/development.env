# AURA Intelligence Development Configuration
# Copy this file to .env for local development

# Environment
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=DEBUG

# Database Configuration
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=development_password_change_me
NEO4J_DATABASE=aura

REDIS_URL=redis://localhost:6379
REDIS_DB=0

DATABASE_URL=postgresql://aura:aura_password@localhost:5432/aura_intelligence

# Messaging Configuration
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
KAFKA_SECURITY_PROTOCOL=PLAINTEXT
KAFKA_SCHEMA_REGISTRY_URL=http://localhost:8081

NATS_URL=nats://localhost:4222

RABBITMQ_URL=amqp://guest:guest@localhost:5672/

# Observability Configuration
OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4317
OTEL_SERVICE_NAME=aura-intelligence-dev
PROMETHEUS_PORT=9090
JAEGER_ENDPOINT=http://localhost:14268

# Services Configuration
TDA_SERVICE_URL=http://localhost:8080
TEMPORAL_HOST=localhost:7233
TEMPORAL_NAMESPACE=default
MEM0_BASE_URL=http://localhost:8080

# Security Configuration (Development Only)
JWT_SECRET=development_jwt_secret_change_in_production
ENCRYPTION_KEY=development_encryption_key_32_chars_long!
MIN_PASSWORD_LENGTH=8
REQUIRE_SPECIAL_CHARS=true

# Feature Flags
FEATURE_TDA_SPECSEQ_PLUS=true
FEATURE_TDA_SIMBA_GPU=false
FEATURE_TDA_NEURAL_SURVEILLANCE=true
FEATURE_WORKFLOW_AUTO_RECOVERY=true
FEATURE_MONITORING_ENHANCED_TRACING=false