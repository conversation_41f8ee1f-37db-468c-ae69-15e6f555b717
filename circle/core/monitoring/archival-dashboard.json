{"dashboard": {"id": null, "title": "AURA Intelligence - Production Archival System", "tags": ["aura-intelligence", "archival", "production"], "timezone": "browser", "panels": [{"id": 1, "title": "Archival Job Success Rate", "type": "stat", "targets": [{"expr": "rate(archival_job_success_total[5m]) / (rate(archival_job_success_total[5m]) + rate(archival_job_failures_total[5m])) * 100", "legendFormat": "Success Rate %"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 90}, {"color": "green", "value": 95}]}, "unit": "percent"}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}}, {"id": 2, "title": "Archival Job Duration", "type": "stat", "targets": [{"expr": "histogram_quantile(0.95, rate(archival_job_duration_seconds_bucket[5m]))", "legendFormat": "P95 Duration"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 300}, {"color": "red", "value": 600}]}, "unit": "s"}}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}}, {"id": 3, "title": "Records Archived per Hour", "type": "stat", "targets": [{"expr": "rate(archival_records_processed_total[1h])", "legendFormat": "Records/Hour"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0}}, {"id": 4, "title": "Data Volume Archived", "type": "stat", "targets": [{"expr": "archival_data_volume_bytes", "legendFormat": "{{partition_type}}"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "bytes"}}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}}, {"id": 5, "title": "Archival Job Timeline", "type": "timeseries", "targets": [{"expr": "rate(archival_job_success_total[5m])", "legendFormat": "Successful Jobs"}, {"expr": "rate(archival_job_failures_total[5m])", "legendFormat": "Failed Jobs"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "barAlignment": 0, "lineWidth": 1, "fillOpacity": 10, "gradientMode": "none", "spanNulls": false, "insertNulls": false, "showPoints": "never", "pointSize": 5, "stacking": {"mode": "none", "group": "A"}, "axisPlacement": "auto", "axisLabel": "", "scaleDistribution": {"type": "linear"}, "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "thresholdsStyle": {"mode": "off"}}, "unit": "short"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 6, "title": "S3 Operation Performance", "type": "timeseries", "targets": [{"expr": "histogram_quantile(0.95, rate(archival_s3_operation_duration_seconds_bucket{operation_type=\"export\"}[5m]))", "legendFormat": "S3 Export P95"}, {"expr": "histogram_quantile(0.95, rate(archival_s3_operation_duration_seconds_bucket{operation_type=\"verify\"}[5m]))", "legendFormat": "S3 Verify P95"}, {"expr": "histogram_quantile(0.95, rate(archival_s3_operation_duration_seconds_bucket{operation_type=\"commit\"}[5m]))", "legendFormat": "S3 Commit P95"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "s"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 7, "title": "Error Breakdown", "type": "piechart", "targets": [{"expr": "sum by (error_type) (rate(archival_job_failures_total[1h]))", "legendFormat": "{{error_type}}"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "unit": "short"}}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 16}}, {"id": 8, "title": "Kubernetes CronJob Status", "type": "table", "targets": [{"expr": "kube_cronjob_status_last_schedule_time{cronjob=\"aura-intelligence-archival\"}", "legendFormat": "Last Schedule"}, {"expr": "kube_cronjob_status_active{cronjob=\"aura-intelligence-archival\"}", "legendFormat": "Active Jobs"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "red", "value": 1}]}}}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 16}}, {"id": 9, "title": "Resource Utilization", "type": "timeseries", "targets": [{"expr": "rate(container_cpu_usage_seconds_total{pod=~\"aura-intelligence-archival.*\"}[5m]) * 100", "legendFormat": "CPU Usage %"}, {"expr": "container_memory_usage_bytes{pod=~\"aura-intelligence-archival.*\"} / container_spec_memory_limit_bytes{pod=~\"aura-intelligence-archival.*\"} * 100", "legendFormat": "Memory Usage %"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "percent"}}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 16}}], "time": {"from": "now-6h", "to": "now"}, "refresh": "30s", "schemaVersion": 27, "version": 1, "links": [{"title": "AURA Intelligence Main Dashboard", "url": "/d/aura-intelligence-main", "type": "dashboards"}, {"title": "Kubernetes Cluster Overview", "url": "/d/kubernetes-cluster", "type": "dashboards"}]}}