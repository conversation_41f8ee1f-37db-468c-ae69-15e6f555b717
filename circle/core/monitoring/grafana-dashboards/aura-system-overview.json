{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 70}, {"color": "green", "value": 85}]}, "unit": "percent"}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}, "id": 1, "options": {"orientation": "auto", "reduceOptions": {"values": false, "calcs": ["lastNotNull"]}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "8.0.0", "targets": [{"expr": "aura_system_health_score", "refId": "A"}], "title": "System Health Score", "type": "gauge"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"tooltip": false, "viz": false, "legend": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "events/s"}}, "gridPos": {"h": 8, "w": 12, "x": 6, "y": 0}, "id": 2, "options": {"tooltip": {"mode": "single"}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}}, "pluginVersion": "8.0.0", "targets": [{"expr": "rate(aura_events_processed_total[5m])", "legendFormat": "{{event_type}}", "refId": "A"}], "title": "Event Processing Rate", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "none"}}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}, "id": 3, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"values": false, "calcs": ["lastNotNull"]}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.0.0", "targets": [{"expr": "aura_active_debates", "refId": "A"}], "title": "Active Debates", "type": "stat"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"tooltip": false, "viz": false, "legend": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "s"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 4, "options": {"tooltip": {"mode": "single"}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}}, "pluginVersion": "8.0.0", "targets": [{"expr": "aura_projection_lag_seconds", "legendFormat": "{{projection_name}}", "refId": "A"}], "title": "Projection Lag", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"tooltip": false, "viz": false, "legend": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "percent"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 5, "options": {"tooltip": {"mode": "single"}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}}, "pluginVersion": "8.0.0", "targets": [{"expr": "aura_debate_consensus_rate", "legendFormat": "{{time_window}}", "refId": "A"}], "title": "Debate Consensus Rate", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 1}]}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "id": 6, "options": {"showHeader": true}, "pluginVersion": "8.0.0", "targets": [{"expr": "aura_system_component_status", "format": "table", "instant": true, "refId": "A"}], "title": "Component Health Status", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {}, "renameByName": {"component": "Component", "Value": "Status"}}}], "type": "table"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"tooltip": false, "viz": false, "legend": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "ms"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "id": 7, "options": {"tooltip": {"mode": "single"}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}}, "pluginVersion": "8.0.0", "targets": [{"expr": "histogram_quantile(0.95, sum(rate(aura_event_processing_duration_seconds_bucket[5m])) by (event_type, le)) * 1000", "legendFormat": "p95 {{event_type}}", "refId": "A"}, {"expr": "histogram_quantile(0.99, sum(rate(aura_event_processing_duration_seconds_bucket[5m])) by (event_type, le)) * 1000", "legendFormat": "p99 {{event_type}}", "refId": "B"}], "title": "Event Processing Latency", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"tooltip": false, "viz": false, "legend": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "errors/s"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "id": 8, "options": {"tooltip": {"mode": "single"}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}}, "pluginVersion": "8.0.0", "targets": [{"expr": "rate(aura_event_store_errors_total[5m])", "legendFormat": "Event Store {{error_type}}", "refId": "A"}, {"expr": "rate(aura_projection_errors_total[5m])", "legendFormat": "Projection {{projection_name}} {{error_type}}", "refId": "B"}, {"expr": "rate(aura_agent_errors_total[5m])", "legendFormat": "Agent {{agent_id}} {{error_type}}", "refId": "C"}], "title": "Error Rates", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 100}, {"color": "red", "value": 500}]}, "unit": "none"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}, "id": 9, "options": {"orientation": "auto", "reduceOptions": {"values": false, "calcs": ["lastNotNull"]}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "8.0.0", "targets": [{"expr": "sum(aura_projection_dlq_size)", "refId": "A"}], "title": "Total Dead Letter Queue Size", "type": "gauge"}], "refresh": "10s", "schemaVersion": 27, "style": "dark", "tags": ["aura", "overview"], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "AURA Intelligence System Overview", "uid": "aura-system-overview", "version": 0}