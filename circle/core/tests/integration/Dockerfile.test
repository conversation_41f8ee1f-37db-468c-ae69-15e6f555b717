# 🧪 Test Runner Dockerfile - Production-Grade Testing Environment
#
# Multi-stage build for comprehensive end-to-end pipeline testing
# Includes all dependencies for integration, load, and data quality testing

FROM python:3.11-slim as base

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    git \
    build-essential \
    pkg-config \
    libssl-dev \
    libffi-dev \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt requirements-test.txt ./

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir -r requirements-test.txt

# Install additional testing dependencies
RUN pip install --no-cache-dir \
    pytest==7.4.3 \
    pytest-asyncio==0.21.1 \
    pytest-benchmark==4.0.0 \
    pytest-xdist==3.3.1 \
    pytest-cov==4.1.0 \
    pytest-html==4.1.1 \
    great-expectations==0.18.8 \
    locust==2.17.0 \
    duckdb==0.9.2 \
    redis==5.0.1 \
    minio==7.2.0 \
    boto3==1.34.0 \
    pyarrow==14.0.1 \
    pandas==2.1.4 \
    numpy==1.24.4 \
    scikit-learn==1.3.2 \
    aiohttp==3.9.1 \
    asyncio-mqtt==0.16.1 \
    prometheus-client==0.19.0

# Copy source code
COPY src/ ./src/
COPY tests/ ./tests/

# Create test data directories
RUN mkdir -p /app/test-results /app/test-data /data

# Set Python path
ENV PYTHONPATH=/app/src:/app

# Create non-root user for security
RUN useradd --create-home --shell /bin/bash testuser && \
    chown -R testuser:testuser /app /data
USER testuser

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import sys; sys.exit(0)"

# Default command
CMD ["python", "-m", "pytest", "tests/integration", "-v", "--tb=short"]
