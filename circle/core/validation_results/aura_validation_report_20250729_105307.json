{"validation_start": "2025-07-29T10:53:04.723475", "phases": {"system_health": {"status": "success", "checks": {"python_version": true, "required_modules": true, "file_structure": true, "permissions": true, "disk_space": true}, "success_rate": "5/5", "missing_modules": [], "missing_files": []}, "core_functionality": {"status": "partial", "tests": {"import_aura": false, "agent_creation": true, "event_store": true, "projections": true, "debate_system": true}, "success_rate": "4/5", "details": {"agent_test": {"type": "analyst", "capabilities": ["analysis", "reasoning"], "status": "active"}, "event_store_test": {"events_processed": 1000, "idempotency": true, "replay_capability": true}, "projection_test": {"projections_active": 2, "lag_seconds": 0.5, "health_status": "healthy"}, "debate_test": {"debates_completed": 50, "consensus_rate": 0.95, "avg_duration_seconds": 45}}}, "performance": {"status": "success", "benchmarks": {"import_speed": 1.2159347534179688e-05, "memory_usage": 64000, "file_operations": 0.00017189979553222656, "json_processing": 0.0009989738464355469}, "thresholds": {"import_speed": 0.1, "memory_usage": 1000000, "file_operations": 0.01, "json_processing": 0.05}, "passed": "4/4", "performance_score": 1.0}, "security": {"status": "partial", "checks": {"file_permissions": true, "sensitive_data": false, "encryption_ready": true, "access_controls": true}, "success_rate": "3/4", "sensitive_files": ["/home/<USER>/projects/aura-intelligence/core/standalone_demo.py", "/home/<USER>/projects/aura-intelligence/core/demo_observer.py", "/home/<USER>/projects/aura-intelligence/core/test_real_collective_intelligence.py", "/home/<USER>/projects/aura-intelligence/core/minimal_demo.py", "/home/<USER>/projects/aura-intelligence/core/phase1_step2_demo.py", "/home/<USER>/projects/aura-intelligence/core/run_all_validations.py", "/home/<USER>/projects/aura-intelligence/core/minimal_walking_skeleton.py", "/home/<USER>/projects/aura-intelligence/core/architecture_validation.py"]}, "documentation": {"status": "partial", "checks": {"readme_files": true, "api_docs": true, "deployment_guide": true, "troubleshooting": false}, "success_rate": "3/4", "files_found": {"readme": 20, "api_docs": 730, "deployment": 11, "troubleshooting": 0}}, "disaster_recovery": {"status": "success", "scenarios": {"data_backup": true, "service_restart": true, "configuration_recovery": true, "data_restoration": true}, "success_rate": "4/4", "recovery_time_seconds": 2.5}}, "overall_status": "unknown", "duration_seconds": 0, "artifacts": [], "summary": {"total_phases": 6, "successful_phases": 3, "success_rate": "3/6", "validation_timestamp": "2025-07-29T10:53:07.563117", "production_ready": false}}