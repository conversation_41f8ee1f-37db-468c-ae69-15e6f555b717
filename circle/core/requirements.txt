# 🌟 AURA Intelligence Core - Complete Dependencies
# The World's First TDA-Powered Collective Intelligence System

# === CORE FRAMEWORK ===
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0
httpx==0.25.2

# === LANGCHAIN & LANGGRAPH (Enhanced for Phase 1+2) ===
langchain>=0.2.25
langgraph>=0.2.0
langsmith>=0.1.90


# === OBSERVABILITY STACK ===
opentelemetry-api==1.22.0
opentelemetry-sdk==1.22.0
opentelemetry-instrumentation==0.43b0
opentelemetry-instrumentation-fastapi==0.43b0
opentelemetry-instrumentation-httpx==0.43b0
opentelemetry-instrumentation-langchain>=0.2.0
opentelemetry-exporter-otlp-proto-http==1.22.0
opentelemetry-exporter-prometheus==0.43b0
opentelemetry-semantic-conventions==0.43b0
prometheus-client==0.19.0
structlog==23.2.0
python-json-logger==2.0.7

# === INFRASTRUCTURE ===
neo4j>=5.22.0
aiokafka>=0.10.0
aioredis>=2.0.1
psutil>=5.9.8
confluent-kafka==2.3.0
temporalio==1.4.0

# === SHADOW MODE LOGGING ===
aiofiles==23.2.1
aiosqlite==0.19.0

# === BASIC UTILITIES ===
requests==2.31.0
python-multipart==0.0.6
python-dotenv==1.0.0

# === MACHINE LEARNING ===
torch==2.1.1
scikit-learn==1.3.2
numpy==1.25.2
pandas==2.1.4

# === TOPOLOGICAL DATA ANALYSIS ===
gudhi==3.8.0
ripser==0.6.4
scikit-tda==1.0.0
giotto-tda==0.6.0
persim==0.3.2

# === GRAPH PROCESSING ===
networkx==3.2.1
graph-tool==2.57

# === DATABASE ===
sqlalchemy==2.0.23
alembic==1.13.1
asyncpg==0.29.0
motor==3.3.2

# === DISTRIBUTED SCALING (Phase 3 - Behind Feature Flags) ===
# Ray Serve for distributed agent deployments
ray[serve]>=2.9.0

# CrewAI for hierarchical flows
crewai>=0.28.0

# === TESTING ===
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0

