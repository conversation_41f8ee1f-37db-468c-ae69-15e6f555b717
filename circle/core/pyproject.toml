[build-system]
requires = ["hatchling", "maturin>=1.0,<2.0"]
build-backend = "hatchling.build"

[project]
name = "aura-intelligence-ultimate"
version = "5.0.0"
description = "Ultimate AURA Intelligence System - Complete Enterprise Platform"
readme = "README.md"
license = {file = "LICENSE"}
authors = [
    {name = "AURA Intelligence Team", email = "<EMAIL>"},
]
keywords = ["ai", "topology", "agents", "enterprise", "aiops", "tda", "federated"]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Programming Language :: Rust",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: System :: Monitoring",
    "Topic :: System :: Systems Administration",
]
requires-python = ">=3.13"
dependencies = [
    # Core AI/ML Stack
    "numpy>=1.24.0",
    "scipy>=1.10.0",
    "scikit-learn>=1.3.0",
    "pandas>=2.0.0",
    "polars>=0.20.0",
    
    # Memory & Knowledge Systems
    "mem0ai>=0.1.0",
    "neo4j>=5.15.0",
    "qdrant-client>=1.7.0",
    "weaviate-client>=4.4.0",
    "chromadb>=0.4.0",
    
    # Graph & Network Analysis
    "networkx>=3.2.0",
    "torch-geometric>=2.5.0",
    "pyvis>=0.3.2",
    "igraph>=0.11.0",
    
    # Topological Data Analysis
    "ripser>=0.6.4",
    "persim>=0.3.2",
    "scikit-tda>=0.1.0",
    "gudhi>=3.8.0",
    "giotto-tda>=0.6.0",
    
    # Multi-Agent Frameworks
    "langgraph>=0.0.40",
    "langchain>=0.1.0",
    "langchain-openai>=0.0.8",
    "autogen-agentchat>=0.2.0",
    "crewai>=0.28.0",
    "swarm-intelligence>=0.1.0",
    
    # Federated Learning
    "flower>=1.7.0",
    "fedml>=0.8.0",
    "syft>=0.8.0",
    "opacus>=1.4.0",
    
    # High-Performance Computing
    "mojo>=0.7.0",
    "modular>=0.1.0",
    "cupy-cuda12x>=12.3.0",
    "numba>=0.58.0",
    "jax[gpu]>=0.4.0",
    "equinox>=0.11.0",
    
    # Async & Streaming
    "asyncio-mqtt>=0.16.0",
    "nats-py>=2.6.0",
    "redis>=5.0.0",
    "kafka-python>=2.0.0",
    "apache-beam>=2.52.0",
    
    # API & Web Framework
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "httpx>=0.25.0",
    "websockets>=12.0",
    
    # Monitoring & Observability
    "prometheus-client>=0.19.0",
    "structlog>=23.2.0",
    "opentelemetry-api>=1.21.0",
    "opentelemetry-sdk>=1.21.0",
    "opentelemetry-instrumentation-fastapi>=0.42b0",
    
    # Security & Cryptography
    "cryptography>=41.0.0",
    "pycryptodome>=3.19.0",
    "tenseal>=0.3.0",
    "opendp>=0.9.0",
    
    # Development & Testing
    "rich>=13.7.0",
    "typer>=0.9.0",
    "click>=8.1.0",
    "tqdm>=4.66.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-benchmark>=4.0.0",
    "black>=23.9.0",
    "isort>=5.12.0",
    "mypy>=1.6.0",
    "ruff>=0.1.0",
    "pre-commit>=3.5.0",
]

gpu = [
    "torch>=2.1.0",
    "torchvision>=0.16.0",
    "tensorflow>=2.15.0",
    "cudf>=23.12.0",
    "cuml>=23.12.0",
    "cugraph>=23.12.0",
]

quantum = [
    "qiskit>=0.45.0",
    "cirq>=1.3.0",
    "pennylane>=0.33.0",
    "quantum-computing>=0.1.0",
]

enterprise = [
    "kubernetes>=28.1.0",
    "helm>=3.13.0",
    "prometheus-client>=0.19.0",
    "grafana-api>=1.0.3",
    "datadog>=0.47.0",
    "newrelic>=9.2.0",
]

research = [
    "jupyter>=1.0.0",
    "jupyterlab>=4.0.0",
    "matplotlib>=3.8.0",
    "seaborn>=0.13.0",
    "plotly>=5.17.0",
    "wandb>=0.16.0",
    "mlflow>=2.8.0",
]

[project.urls]
Homepage = "https://github.com/aura-intelligence/aura-intelligence-ultimate"
Documentation = "https://docs.aura-intelligence.ai"
Repository = "https://github.com/aura-intelligence/aura-intelligence-ultimate"
Issues = "https://github.com/aura-intelligence/aura-intelligence-ultimate/issues"
Changelog = "https://github.com/aura-intelligence/aura-intelligence-ultimate/blob/main/CHANGELOG.md"

[project.scripts]
aura = "aura_intelligence.cli:main"
aura-server = "aura_intelligence.server:main"
aura-worker = "aura_intelligence.worker:main"
aura-federated = "aura_intelligence.federated:main"
aura-tda = "aura_intelligence.tda:main"
aura-agents = "aura_intelligence.agents:main"

[tool.hatch.build.targets.wheel]
packages = ["src/aura_intelligence"]

[tool.hatch.build.targets.wheel.hooks.maturin]
dependencies = ["maturin"]

[tool.black]
line-length = 88
target-version = ['py313']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | rust_kernels/target
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["aura_intelligence"]
known_third_party = ["numpy", "pandas", "torch", "tensorflow"]

[tool.mypy]
python_version = "3.13"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "mem0.*",
    "neo4j.*",
    "qdrant_client.*",
    "ripser.*",
    "persim.*",
    "gudhi.*",
    "langgraph.*",
    "autogen.*",
    "crewai.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config --cov=aura_intelligence --cov-report=term-missing"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "gpu: marks tests that require GPU",
    "mojo: marks tests that require Mojo",
    "federated: marks tests for federated learning",
    "enterprise: marks tests for enterprise features",
    "quantum: marks tests for quantum computing features",
]

[tool.coverage.run]
source = ["src/aura_intelligence"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/rust_kernels/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.ruff]
target-version = "py313"
line-length = 88
select = [
    "E",   # pycodestyle errors
    "W",   # pycodestyle warnings
    "F",   # pyflakes
    "I",   # isort
    "B",   # flake8-bugbear
    "C4",  # flake8-comprehensions
    "UP",  # pyupgrade
    "N",   # pep8-naming
    "S",   # flake8-bandit
    "T20", # flake8-print
    "SIM", # flake8-simplify
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
    "S101",  # use of assert
    "T201",  # print statements (allowed in CLI)
]

[tool.ruff.per-file-ignores]
"__init__.py" = ["F401"]
"tests/*" = ["B011", "S101"]
"examples/*" = ["T201"]

# Rust kernel configuration
[tool.maturin]
python-source = "python"
module-name = "aura_intelligence._rust_kernels"
bindings = "pyo3"
compatibility = "linux"

[tool.cibuildwheel]
build = "cp311-* cp312-*"
skip = "*-win32 *-manylinux_i686"
