# 🚀 AURA Intelligence Startup Requirements
# Optimized for startup deployment - Phase 1+2 only
# Excludes distributed scaling components until needed

# === CORE FRAMEWORK ===
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# === LANGCHAIN & LANGGRAPH (Enhanced) ===
langchain>=0.2.25
langgraph>=0.2.0
langsmith>=0.1.90
langchain-core>=0.3.0

# === ORCHESTRATION CORE (Phase 1+2) ===
# PostgreSQL for persistent checkpointing
psycopg2-binary>=2.9.9
asyncpg>=0.29.0

# Redis for cross-thread memory
redis>=5.0.0
aioredis>=2.0.1

# Temporal.io for durable workflows
temporalio>=1.0.0

# === OBSERVABILITY STACK ===
opentelemetry-sdk>=1.26.0
opentelemetry-exporter-otlp>=1.26.0
opentelemetry-instrumentation-langchain>=0.2.0
prometheus-client>=0.20.0
structlog>=24.2.0
python-json-logger>=2.0.7

# === INFRASTRUCTURE ===
neo4j>=5.22.0
aiokafka>=0.10.0
psutil>=5.9.8

# === SHADOW MODE LOGGING ===
aiofiles==23.2.1
aiosqlite==0.19.0

# === BASIC UTILITIES ===
requests==2.31.0
python-multipart==0.0.6
python-dotenv==1.0.0

# === MACHINE LEARNING ===
torch==2.1.1
scikit-learn==1.3.2
numpy==1.25.2
pandas==2.1.4

# === TOPOLOGICAL DATA ANALYSIS ===
gudhi==3.8.0
ripser==0.6.4
scikit-tda==1.0.0
giotto-tda==0.6.0
persim==0.3.2

# === GRAPH PROCESSING ===
networkx==3.2.1

# === DATABASE ===
sqlalchemy==2.0.23
alembic==1.13.1
motor==3.3.2

# === TESTING ===
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock>=3.12.0
pytest-timeout>=2.2.0
httpx==0.25.2

# === DEVELOPMENT ===
black>=23.9.0
isort>=5.12.0
mypy>=1.6.0
pre-commit>=3.5.0

# NOTE: Phase 3 distributed scaling dependencies are excluded:
# - ray[serve] (Ray Serve agent deployments)
# - crewai (CrewAI Flows hierarchical coordination)
# - kubernetes (Container orchestration)
# 
# These can be added later by switching to requirements.txt
# or enabling feature flags when scaling is needed.