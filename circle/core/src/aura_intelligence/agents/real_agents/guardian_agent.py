"""
🛡️ Real Guardian Agent - Security & Compliance Enforcement
Professional implementation of the guardian agent for collective intelligence.
"""

import asyncio
import logging
import hashlib
import re
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
import ipaddress

logger = logging.getLogger(__name__)


@dataclass
class SecurityResult:
    """Result from guardian agent security analysis."""
    threat_level: str
    compliance_status: str
    protective_actions: List[Dict[str, Any]]
    security_recommendations: List[Dict[str, Any]]
    incident_logged: bool
    confidence: float
    processing_time: float
    summary: str


class RealGuardianAgent:
    """
    🛡️ Real Guardian Agent
    
    Enforces security policies and compliance requirements for the collective intelligence system.
    Capabilities:
    - Threat detection and classification
    - Security policy enforcement
    - Compliance monitoring
    - Incident response coordination
    - Risk assessment and mitigation
    """
    
    def __init__(self):
        # Security threat patterns
        self.threat_patterns = {
            'injection_attacks': [
                r'(\bUNION\b.*\bSELECT\b)',
                r'(\bDROP\b.*\bTABLE\b)',
                r'(<script[^>]*>.*?</script>)',
                r'(javascript:)',
                r'(\bEXEC\b.*\()'
            ],
            'suspicious_ips': [
                '10.0.0.0/8',      # Private networks (suspicious if external)
                '**********/12',   # Private networks
                '***********/16'   # Private networks
            ],
            'malicious_patterns': [
                r'(\.\.\/){2,}',    # Directory traversal
                r'(\beval\b\s*\()', # Code evaluation
                r'(\bexec\b\s*\()', # Code execution
                r'(\bsystem\b\s*\()', # System commands
            ]
        }
        
        # Compliance frameworks
        self.compliance_frameworks = {
            'gdpr': {
                'data_protection': ['encryption', 'anonymization', 'consent_tracking'],
                'privacy_rights': ['data_access', 'data_deletion', 'data_portability'],
                'breach_notification': ['72_hour_rule', 'authority_notification']
            },
            'sox': {
                'financial_controls': ['audit_trails', 'segregation_duties', 'approval_workflows'],
                'data_integrity': ['change_controls', 'backup_procedures', 'access_controls']
            },
            'hipaa': {
                'phi_protection': ['encryption', 'access_controls', 'audit_logs'],
                'minimum_necessary': ['role_based_access', 'data_minimization']
            },
            'iso27001': {
                'information_security': ['risk_assessment', 'security_policies', 'incident_response'],
                'continuous_improvement': ['security_monitoring', 'regular_audits']
            }
        }
        
        # Security policies
        self.security_policies = {
            'authentication': {
                'min_password_length': 12,
                'require_mfa': True,
                'session_timeout': 3600,  # 1 hour
                'max_failed_attempts': 3
            },
            'authorization': {
                'principle_least_privilege': True,
                'role_based_access': True,
                'regular_access_review': True
            },
            'data_protection': {
                'encryption_at_rest': True,
                'encryption_in_transit': True,
                'data_classification': True,
                'backup_encryption': True
            },
            'network_security': {
                'firewall_enabled': True,
                'intrusion_detection': True,
                'network_segmentation': True,
                'vpn_required': True
            }
        }
        
        logger.info("🛡️ Real Guardian Agent initialized")
    
    async def enforce_security(self, evidence_log: List[Dict[str, Any]], 
                             context: Dict[str, Any] = None) -> SecurityResult:
        """
        Enforce security policies and assess compliance based on evidence.
        
        Args:
            evidence_log: Evidence items indicating security events
            context: Additional context from other agents
            
        Returns:
            SecurityResult with threat assessment and protective actions
        """
        start_time = asyncio.get_event_loop().time()
        
        logger.info(f"🛡️ Starting security enforcement for {len(evidence_log)} evidence items")
        
        # 1. Assess threat level from evidence
        threat_assessment = await self._assess_threat_level(evidence_log)
        
        # 2. Check compliance status
        compliance_status = await self._check_compliance_status(evidence_log, context)
        
        # 3. Determine protective actions
        protective_actions = await self._determine_protective_actions(threat_assessment, evidence_log)
        
        # 4. Execute protective actions
        executed_actions = await self._execute_protective_actions(protective_actions)
        
        # 5. Generate security recommendations
        recommendations = self._generate_security_recommendations(threat_assessment, compliance_status)
        
        # 6. Log security incident
        incident_logged = await self._log_security_incident(evidence_log, threat_assessment, executed_actions)
        
        # 7. Calculate confidence in security assessment
        confidence = self._calculate_security_confidence(threat_assessment, executed_actions)
        
        processing_time = asyncio.get_event_loop().time() - start_time
        
        # 8. Generate summary
        summary = self._generate_security_summary(threat_assessment, executed_actions, compliance_status)
        
        result = SecurityResult(
            threat_level=threat_assessment['level'],
            compliance_status=compliance_status['status'],
            protective_actions=executed_actions,
            security_recommendations=recommendations,
            incident_logged=incident_logged,
            confidence=confidence,
            processing_time=processing_time,
            summary=summary
        )
        
        logger.info(f"🛡️ Security enforcement complete: {threat_assessment['level']} threat, {len(executed_actions)} actions taken")
        
        return result
    
    async def _assess_threat_level(self, evidence_log: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Assess the overall threat level from evidence."""
        
        threat_indicators = {
            'injection_attempts': 0,
            'suspicious_ips': 0,
            'malicious_patterns': 0,
            'failed_authentications': 0,
            'privilege_escalations': 0,
            'data_exfiltration': 0
        }
        
        threat_scores = []
        
        for evidence in evidence_log:
            evidence_type = evidence.get('type', '')
            threat_score = 0
            
            # Analyze different types of security evidence
            if evidence_type == 'security_alert':
                severity = evidence.get('severity', 'low')
                threat_score = {'critical': 10, 'high': 7, 'medium': 4, 'low': 1}.get(severity, 1)
                
                # Check for specific threat patterns
                alert_content = evidence.get('content', '').lower()
                if 'injection' in alert_content:
                    threat_indicators['injection_attempts'] += 1
                    threat_score += 3
                elif 'authentication' in alert_content:
                    threat_indicators['failed_authentications'] += 1
                    threat_score += 2
                elif 'privilege' in alert_content:
                    threat_indicators['privilege_escalations'] += 1
                    threat_score += 4
            
            elif evidence_type == 'intrusion_attempt':
                threat_indicators['suspicious_ips'] += 1
                source_ip = evidence.get('source_ip', '')
                if self._is_suspicious_ip(source_ip):
                    threat_score = 8
                else:
                    threat_score = 5
            
            elif evidence_type == 'malicious_activity':
                threat_indicators['malicious_patterns'] += 1
                activity_type = evidence.get('activity_type', '')
                threat_score = {'code_injection': 9, 'data_theft': 10, 'system_compromise': 10}.get(activity_type, 6)
            
            elif evidence_type == 'data_access_anomaly':
                threat_indicators['data_exfiltration'] += 1
                volume = evidence.get('data_volume', 0)
                threat_score = min(8, max(3, volume // 1000))  # Scale based on data volume
            
            threat_scores.append(threat_score)
        
        # Calculate overall threat level
        if not threat_scores:
            overall_score = 0
        else:
            # Use weighted average with emphasis on highest threats
            threat_scores.sort(reverse=True)
            weights = [0.4, 0.3, 0.2, 0.1] + [0.0] * (len(threat_scores) - 4)
            overall_score = sum(score * weight for score, weight in zip(threat_scores, weights))
        
        # Determine threat level
        if overall_score >= 8:
            threat_level = 'critical'
        elif overall_score >= 6:
            threat_level = 'high'
        elif overall_score >= 3:
            threat_level = 'medium'
        else:
            threat_level = 'low'
        
        threat_assessment = {
            'level': threat_level,
            'score': overall_score,
            'indicators': threat_indicators,
            'evidence_count': len(evidence_log),
            'highest_individual_threat': max(threat_scores) if threat_scores else 0,
            'assessment_timestamp': datetime.now().isoformat()
        }
        
        logger.info(f"🔍 Threat assessment: {threat_level} (score: {overall_score:.2f})")
        return threat_assessment
    
    async def _check_compliance_status(self, evidence_log: List[Dict[str, Any]], 
                                     context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Check compliance status against various frameworks."""
        
        compliance_checks = {}
        
        # Check each compliance framework
        for framework, requirements in self.compliance_frameworks.items():
            framework_compliance = await self._check_framework_compliance(framework, requirements, evidence_log)
            compliance_checks[framework] = framework_compliance
        
        # Calculate overall compliance status
        compliant_frameworks = sum(1 for check in compliance_checks.values() if check['compliant'])
        total_frameworks = len(compliance_checks)
        compliance_percentage = (compliant_frameworks / total_frameworks) * 100 if total_frameworks > 0 else 100
        
        # Determine overall status
        if compliance_percentage >= 90:
            overall_status = 'compliant'
        elif compliance_percentage >= 70:
            overall_status = 'mostly_compliant'
        elif compliance_percentage >= 50:
            overall_status = 'partially_compliant'
        else:
            overall_status = 'non_compliant'
        
        compliance_status = {
            'status': overall_status,
            'percentage': compliance_percentage,
            'framework_checks': compliance_checks,
            'violations': self._identify_compliance_violations(compliance_checks),
            'check_timestamp': datetime.now().isoformat()
        }
        
        logger.info(f"📋 Compliance status: {overall_status} ({compliance_percentage:.1f}%)")
        return compliance_status
    
    async def _check_framework_compliance(self, framework: str, requirements: Dict[str, List[str]], 
                                        evidence_log: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Check compliance for a specific framework."""
        
        compliance_results = {}
        
        for category, controls in requirements.items():
            category_compliance = []
            
            for control in controls:
                # Simulate compliance checking
                # In production, this would check actual system configurations
                is_compliant = await self._check_control_compliance(framework, category, control, evidence_log)
                category_compliance.append({
                    'control': control,
                    'compliant': is_compliant,
                    'last_checked': datetime.now().isoformat()
                })
            
            compliance_results[category] = {
                'controls': category_compliance,
                'compliant': all(c['compliant'] for c in category_compliance)
            }
        
        overall_compliant = all(cat['compliant'] for cat in compliance_results.values())
        
        return {
            'framework': framework,
            'compliant': overall_compliant,
            'categories': compliance_results,
            'compliance_score': sum(1 for cat in compliance_results.values() if cat['compliant']) / len(compliance_results) * 100
        }
    
    async def _check_control_compliance(self, framework: str, category: str, control: str, 
                                      evidence_log: List[Dict[str, Any]]) -> bool:
        """Check compliance for a specific control."""
        
        # Simulate control compliance checking
        # In production, this would integrate with actual security tools and configurations
        
        # Check for evidence of non-compliance
        for evidence in evidence_log:
            evidence_type = evidence.get('type', '')
            
            # Security violations that affect compliance
            if evidence_type == 'security_violation':
                violation_type = evidence.get('violation_type', '')
                if control in violation_type or category in violation_type:
                    return False
            
            # Data protection violations
            elif evidence_type == 'data_breach' and 'data_protection' in category:
                return False
            
            # Access control violations
            elif evidence_type == 'unauthorized_access' and 'access' in control:
                return False
        
        # Default to compliant if no violations found
        # In production, this would be more sophisticated
        return True
    
    async def _determine_protective_actions(self, threat_assessment: Dict[str, Any], 
                                          evidence_log: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Determine appropriate protective actions based on threat level."""
        
        threat_level = threat_assessment['level']
        actions = []
        
        # Base actions for all threat levels
        actions.append({
            'type': 'monitoring_enhancement',
            'description': 'Enhance security monitoring and alerting',
            'priority': 'medium',
            'automated': True,
            'estimated_time': '5 minutes'
        })
        
        # Threat-level specific actions
        if threat_level in ['high', 'critical']:
            actions.extend([
                {
                    'type': 'access_restriction',
                    'description': 'Implement temporary access restrictions',
                    'priority': 'high',
                    'automated': True,
                    'estimated_time': '2 minutes'
                },
                {
                    'type': 'incident_response',
                    'description': 'Activate incident response procedures',
                    'priority': 'critical',
                    'automated': False,
                    'estimated_time': '15 minutes'
                }
            ])
        
        if threat_level == 'critical':
            actions.extend([
                {
                    'type': 'system_isolation',
                    'description': 'Isolate affected systems from network',
                    'priority': 'critical',
                    'automated': False,
                    'estimated_time': '10 minutes'
                },
                {
                    'type': 'emergency_notification',
                    'description': 'Send emergency notifications to security team',
                    'priority': 'critical',
                    'automated': True,
                    'estimated_time': '1 minute'
                }
            ])
        
        # Evidence-specific actions
        for evidence in evidence_log:
            evidence_type = evidence.get('type', '')
            
            if evidence_type == 'intrusion_attempt':
                actions.append({
                    'type': 'ip_blocking',
                    'description': f"Block suspicious IP: {evidence.get('source_ip', 'unknown')}",
                    'priority': 'high',
                    'automated': True,
                    'estimated_time': '1 minute',
                    'target_ip': evidence.get('source_ip')
                })
            
            elif evidence_type == 'malicious_activity':
                actions.append({
                    'type': 'process_termination',
                    'description': f"Terminate malicious process: {evidence.get('process', 'unknown')}",
                    'priority': 'high',
                    'automated': True,
                    'estimated_time': '30 seconds',
                    'target_process': evidence.get('process')
                })
        
        # Sort actions by priority
        priority_order = {'critical': 4, 'high': 3, 'medium': 2, 'low': 1}
        actions.sort(key=lambda x: priority_order.get(x.get('priority', 'low'), 1), reverse=True)
        
        logger.info(f"🛡️ Determined {len(actions)} protective actions for {threat_level} threat")
        return actions
    
    async def _execute_protective_actions(self, protective_actions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Execute protective actions that are marked as automated."""
        
        executed_actions = []
        
        for action in protective_actions:
            if action.get('automated', False):
                execution_result = await self._execute_single_action(action)
                executed_actions.append(execution_result)
            else:
                # For manual actions, create a task/alert
                executed_actions.append({
                    'action': action,
                    'status': 'manual_intervention_required',
                    'executed': False,
                    'timestamp': datetime.now().isoformat(),
                    'message': f"Manual action required: {action.get('description', 'Unknown action')}"
                })
        
        logger.info(f"⚡ Executed {len([a for a in executed_actions if a.get('executed')])} automated actions")
        return executed_actions
    
    async def _execute_single_action(self, action: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a single protective action."""
        
        action_type = action.get('type', 'unknown')
        
        # Simulate action execution
        await asyncio.sleep(0.1)  # Simulate processing time
        
        # In production, this would contain actual security action implementations
        execution_result = {
            'action': action,
            'status': 'executed',
            'executed': True,
            'timestamp': datetime.now().isoformat(),
            'execution_time': action.get('estimated_time', '1 minute'),
            'result': f"Successfully executed {action_type}",
            'details': self._get_action_details(action_type, action)
        }
        
        logger.info(f"✅ Executed protective action: {action_type}")
        return execution_result
    
    def _get_action_details(self, action_type: str, action: Dict[str, Any]) -> Dict[str, Any]:
        """Get detailed results for specific action types."""
        
        details = {'action_type': action_type}
        
        if action_type == 'ip_blocking':
            details.update({
                'blocked_ip': action.get('target_ip', 'unknown'),
                'block_duration': '24 hours',
                'firewall_rule_added': True
            })
        elif action_type == 'access_restriction':
            details.update({
                'restricted_users': 'high_risk_users',
                'restriction_level': 'temporary_suspension',
                'review_required': True
            })
        elif action_type == 'monitoring_enhancement':
            details.update({
                'monitoring_level': 'increased',
                'alert_sensitivity': 'high',
                'log_retention': 'extended'
            })
        
        return details
    
    def _generate_security_recommendations(self, threat_assessment: Dict[str, Any], 
                                         compliance_status: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate security recommendations based on assessment results."""
        
        recommendations = []
        
        # Threat-based recommendations
        threat_level = threat_assessment['level']
        if threat_level in ['high', 'critical']:
            recommendations.extend([
                {
                    'type': 'security_hardening',
                    'description': 'Implement additional security hardening measures',
                    'priority': 'high',
                    'estimated_effort': '2-4 hours',
                    'impact': 'high'
                },
                {
                    'type': 'security_training',
                    'description': 'Conduct emergency security awareness training',
                    'priority': 'medium',
                    'estimated_effort': '1-2 hours',
                    'impact': 'medium'
                }
            ])
        
        # Compliance-based recommendations
        if compliance_status['status'] != 'compliant':
            violations = compliance_status.get('violations', [])
            for violation in violations:
                recommendations.append({
                    'type': 'compliance_remediation',
                    'description': f"Address {violation['framework']} compliance violation: {violation['issue']}",
                    'priority': 'high',
                    'estimated_effort': '4-8 hours',
                    'impact': 'high',
                    'framework': violation['framework']
                })
        
        # General security recommendations
        recommendations.extend([
            {
                'type': 'security_audit',
                'description': 'Conduct comprehensive security audit',
                'priority': 'medium',
                'estimated_effort': '8-16 hours',
                'impact': 'high'
            },
            {
                'type': 'penetration_testing',
                'description': 'Schedule penetration testing',
                'priority': 'low',
                'estimated_effort': '16-32 hours',
                'impact': 'high'
            }
        ])
        
        return recommendations
    
    async def _log_security_incident(self, evidence_log: List[Dict[str, Any]], 
                                   threat_assessment: Dict[str, Any], 
                                   executed_actions: List[Dict[str, Any]]) -> bool:
        """Log security incident for audit and compliance purposes."""
        
        incident_data = {
            'incident_id': hashlib.md5(f"{datetime.now().isoformat()}{len(evidence_log)}".encode()).hexdigest()[:8],
            'timestamp': datetime.now().isoformat(),
            'threat_level': threat_assessment['level'],
            'threat_score': threat_assessment['score'],
            'evidence_count': len(evidence_log),
            'actions_taken': len([a for a in executed_actions if a.get('executed')]),
            'evidence_summary': [{'type': e.get('type'), 'severity': e.get('severity')} for e in evidence_log],
            'response_time': sum(float(a.get('execution_time', '0').split()[0]) for a in executed_actions if a.get('executed')),
            'status': 'logged'
        }
        
        # In production, this would write to a secure audit log
        logger.info(f"📝 Security incident logged: {incident_data['incident_id']}")
        
        return True
    
    def _calculate_security_confidence(self, threat_assessment: Dict[str, Any], 
                                     executed_actions: List[Dict[str, Any]]) -> float:
        """Calculate confidence in security assessment and response."""
        
        # Base confidence on threat assessment accuracy
        threat_confidence = 0.8  # Base confidence in threat assessment
        
        # Factor in action execution success
        total_actions = len(executed_actions)
        successful_actions = len([a for a in executed_actions if a.get('executed')])
        
        action_success_rate = successful_actions / total_actions if total_actions > 0 else 1.0
        
        # Combine factors
        overall_confidence = (threat_confidence * 0.6) + (action_success_rate * 0.4)
        
        return min(overall_confidence, 1.0)
    
    def _generate_security_summary(self, threat_assessment: Dict[str, Any], 
                                 executed_actions: List[Dict[str, Any]], 
                                 compliance_status: Dict[str, Any]) -> str:
        """Generate human-readable summary of security enforcement."""
        
        threat_level = threat_assessment['level']
        actions_count = len([a for a in executed_actions if a.get('executed')])
        compliance = compliance_status['status']
        
        summary_parts = [
            f"Threat level: {threat_level}",
            f"Actions executed: {actions_count}",
            f"Compliance: {compliance}"
        ]
        
        return "; ".join(summary_parts)
    
    # Helper methods
    def _is_suspicious_ip(self, ip_address: str) -> bool:
        """Check if an IP address is suspicious."""
        try:
            ip = ipaddress.ip_address(ip_address)
            for suspicious_range in self.threat_patterns['suspicious_ips']:
                if ip in ipaddress.ip_network(suspicious_range):
                    return True
            return False
        except ValueError:
            return True  # Invalid IP is suspicious
    
    def _identify_compliance_violations(self, compliance_checks: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Identify specific compliance violations."""
        violations = []
        
        for framework, check in compliance_checks.items():
            if not check['compliant']:
                for category, cat_data in check['categories'].items():
                    if not cat_data['compliant']:
                        for control in cat_data['controls']:
                            if not control['compliant']:
                                violations.append({
                                    'framework': framework,
                                    'category': category,
                                    'control': control['control'],
                                    'issue': f"Non-compliant {control['control']} in {category}"
                                })
        
        return violations
