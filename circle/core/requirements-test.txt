# 🧪 Testing Dependencies - Production-Grade Test Suite
#
# Comprehensive testing framework for end-to-end pipeline validation
# Includes integration testing, load testing, and data quality validation

# Core Testing Framework
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-benchmark==4.0.0
pytest-xdist==3.3.1
pytest-cov==4.1.0
pytest-html==4.1.1
pytest-json-report==1.5.0
pytest-mock==3.12.0

# Data Quality & Validation
great-expectations==0.18.8
pandera==0.17.2
cerberus==1.3.5

# Load Testing
locust==2.17.0
aiohttp==3.9.1
httpx==0.25.2

# Database Testing
duckdb==0.9.2
redis==5.0.1
fakeredis==2.20.1

# Cloud Storage Testing
minio==7.2.0
boto3==1.34.0
botocore==1.34.0
s3fs==2023.12.2

# Data Processing & Analysis
pyarrow==14.0.1
pandas==2.1.4
numpy==1.24.4
polars==0.20.2

# Machine Learning Testing
scikit-learn==1.3.2
scipy==1.11.4

# Monitoring & Metrics
prometheus-client==0.19.0
opentelemetry-api==1.21.0
opentelemetry-sdk==1.21.0
opentelemetry-instrumentation==0.42b0

# Async & Networking
asyncio-mqtt==0.16.1
websockets==12.0
aiofiles==23.2.1

# Utilities
pydantic==2.5.2
typer==0.9.0
rich==13.7.0
loguru==0.7.2

# Development & Debugging
ipython==8.18.1
ipdb==0.13.13
memory-profiler==0.61.0
line-profiler==4.1.1

# Container & Orchestration Testing
docker==6.1.3
kubernetes==28.1.0

# Time & Date Testing
freezegun==1.2.2
python-dateutil==2.8.2

# Fixtures & Factories
factory-boy==3.3.0
faker==20.1.0

# Configuration & Environment
python-dotenv==1.0.0
pyyaml==6.0.1
toml==0.10.2

# Security Testing
cryptography==41.0.8
bcrypt==4.1.2

# Performance Profiling
py-spy==0.3.14
scalene==1.5.26

# Documentation Testing
sphinx==7.2.6
sphinx-rtd-theme==1.3.0
