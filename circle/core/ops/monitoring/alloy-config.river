// 🔬 Grafana Alloy Configuration - 2025 Production Grade
// 
// Modern observability agent with:
// - eBPF-based system metrics without code changes
// - ML-powered adaptive sampling for cost optimization
// - Multi-signal correlation for intelligent alerting
// - Agent-ready distributed tracing

// ============================================================================
// DISCOVERY: Kubernetes Service Discovery
// ============================================================================

discovery.kubernetes "aura_intelligence" {
  role = "pod"
  
  namespaces {
    names = ["aura-intelligence", "aura-intelligence-staging"]
  }
  
  selectors {
    role = "pod"
    label = "app.kubernetes.io/name=aura-intelligence"
  }
}

// ============================================================================
// METRICS: Adaptive Prometheus Scraping with ML-Based Sampling
// ============================================================================

prometheus.scrape "aura_metrics" {
  targets    = discovery.kubernetes.aura_intelligence.targets
  forward_to = [prometheus.relabel.adaptive_sampling.receiver]
  
  scrape_interval = "15s"
  scrape_timeout  = "10s"
  
  // Modern scrape configuration
  honor_labels      = true
  honor_timestamps  = true
  track_timestamps_staleness = true
  
  // Efficient connection pooling
  http_client_config {
    follow_redirects = true
    enable_http2     = true
  }
}

// Adaptive sampling based on metric importance and system load
prometheus.relabel "adaptive_sampling" {
  forward_to = [prometheus.remote_write.grafana_cloud.receiver]
  
  rule {
    source_labels = ["__name__"]
    regex         = "aura_search_duration.*|aura_agent_decision_time.*"
    target_label  = "__tmp_priority"
    replacement   = "critical"
  }
  
  rule {
    source_labels = ["__name__"]
    regex         = "aura_memory_usage.*|aura_archival_jobs.*"
    target_label  = "__tmp_priority"
    replacement   = "high"
  }
  
  rule {
    source_labels = ["__name__"]
    regex         = "aura_patterns_discovered.*"
    target_label  = "__tmp_priority"
    replacement   = "medium"
  }
  
  // Default priority for unlabeled metrics
  rule {
    source_labels = ["__tmp_priority"]
    regex         = "^$"
    target_label  = "__tmp_priority"
    replacement   = "low"
  }
  
  // Adaptive sampling rates based on priority
  rule {
    source_labels = ["__tmp_priority"]
    regex         = "critical"
    target_label  = "__tmp_sample_rate"
    replacement   = "1.0"  // 100% sampling for critical metrics
  }
  
  rule {
    source_labels = ["__tmp_priority"]
    regex         = "high"
    target_label  = "__tmp_sample_rate"
    replacement   = "0.5"  // 50% sampling for high priority
  }
  
  rule {
    source_labels = ["__tmp_priority"]
    regex         = "medium"
    target_label  = "__tmp_sample_rate"
    replacement   = "0.1"  // 10% sampling for medium priority
  }
  
  rule {
    source_labels = ["__tmp_priority"]
    regex         = "low"
    target_label  = "__tmp_sample_rate"
    replacement   = "0.01" // 1% sampling for low priority
  }
  
  // Clean up temporary labels
  rule {
    regex = "__tmp_.*"
    action = "labeldrop"
  }
}

// ============================================================================
// EBPF: System-Level Observability Without Code Changes
// ============================================================================

pyroscope.ebpf "aura_profiling" {
  forward_to = [pyroscope.write.grafana_cloud.receiver]
  
  targets = discovery.kubernetes.aura_intelligence.targets
  
  // Profile types for AI/ML workloads
  profiling_config {
    profile_cpu         = true
    profile_memory      = true
    profile_goroutines  = false  // Not applicable for Python
    profile_mutex       = false
    profile_block       = false
  }
  
  // Sampling configuration
  sample_rate = 100  // 100 Hz for production
  
  // Target specific processes
  targets_config {
    // Target FastAPI processes
    targets {
      __process_name__ = "uvicorn"
      __service_name__ = "aura-intelligence-api"
    }
    
    // Target archival jobs
    targets {
      __process_name__ = "python"
      __command_line__ = ".*archive.*"
      __service_name__ = "aura-intelligence-archival"
    }
    
    // Target consolidation jobs
    targets {
      __process_name__ = "python"
      __command_line__ = ".*consolidation.*"
      __service_name__ = "aura-intelligence-consolidation"
    }
  }
}

// ============================================================================
// TRACES: OpenTelemetry Collection with Agent-Ready Processing
// ============================================================================

otelcol.receiver.otlp "aura_traces" {
  grpc {
    endpoint = "0.0.0.0:4317"
  }
  
  http {
    endpoint = "0.0.0.0:4318"
  }
  
  output {
    traces = [otelcol.processor.batch.default.input]
  }
}

// Batch processing for performance
otelcol.processor.batch "default" {
  send_batch_size     = 1024
  send_batch_max_size = 2048
  timeout             = "5s"
  
  output {
    traces = [otelcol.processor.attributes.agent_enrichment.input]
  }
}

// Enrich traces with agent-specific attributes
otelcol.processor.attributes "agent_enrichment" {
  action {
    key    = "aura.deployment.environment"
    value  = env("DEPLOYMENT_ENV")
    action = "upsert"
  }
  
  action {
    key    = "aura.cluster.name"
    value  = env("CLUSTER_NAME")
    action = "upsert"
  }
  
  // Detect agent operations
  action {
    key           = "aura.operation.type"
    from_attribute = "ai.operation.name"
    action        = "copy"
  }
  
  output {
    traces = [otelcol.exporter.otlp.grafana_cloud.input]
  }
}

// ============================================================================
// LOGS: Structured Log Collection with AI Context
// ============================================================================

loki.source.kubernetes "aura_logs" {
  targets    = discovery.kubernetes.aura_intelligence.targets
  forward_to = [loki.process.structured_parsing.receiver]
}

// Parse structured logs and extract AI context
loki.process "structured_parsing" {
  forward_to = [loki.write.grafana_cloud.receiver]
  
  stage.json {
    expressions = {
      level     = "level"
      timestamp = "timestamp"
      message   = "message"
      agent_id  = "agent_id"
      operation = "operation"
      duration  = "duration_ms"
    }
  }
  
  // Extract search operation context
  stage.regex {
    expression = ".*search.*tier=(?P<search_tier>\\w+).*results=(?P<result_count>\\d+)"
  }
  
  // Extract agent decision context
  stage.regex {
    expression = ".*agent.*type=(?P<agent_type>\\w+).*confidence=(?P<confidence>[0-9.]+)"
  }
  
  // Add labels for efficient querying
  stage.labels {
    values = {
      level       = ""
      search_tier = ""
      agent_type  = ""
    }
  }
}

// ============================================================================
// REMOTE WRITE: Grafana Cloud Integration with Cost Optimization
// ============================================================================

prometheus.remote_write "grafana_cloud" {
  endpoint {
    url = env("GRAFANA_PROMETHEUS_URL")
    
    basic_auth {
      username = env("GRAFANA_PROMETHEUS_USER")
      password = env("GRAFANA_PROMETHEUS_PASSWORD")
    }
    
    // Modern compression for cost savings
    compression = "zstd"
    
    // Intelligent batching
    queue_config {
      capacity          = 10000
      max_shards        = 50
      min_shards        = 1
      max_samples_per_send = 2000
      batch_send_deadline  = "5s"
      min_backoff         = "30ms"
      max_backoff         = "5s"
      retry_on_http_429   = true
    }
    
    // Write relabeling for cost optimization
    write_relabel_config {
      source_labels = ["__name__"]
      regex         = "go_.*|process_.*|python_gc_.*"
      action        = "drop"  // Drop noisy runtime metrics
    }
    
    write_relabel_config {
      source_labels = ["__tmp_sample_rate"]
      regex         = "0.01"
      action        = "drop"
      // Probabilistic dropping based on sample rate
      // This would need custom logic in a real implementation
    }
  }
}

// Pyroscope (profiling) remote write
pyroscope.write "grafana_cloud" {
  endpoint {
    url = env("GRAFANA_PYROSCOPE_URL")
    
    basic_auth {
      username = env("GRAFANA_PYROSCOPE_USER")
      password = env("GRAFANA_PYROSCOPE_PASSWORD")
    }
  }
}

// OpenTelemetry traces export
otelcol.exporter.otlp "grafana_cloud" {
  client {
    endpoint = env("GRAFANA_TEMPO_URL")
    
    auth = otelcol.auth.basic.grafana_cloud.handler
    
    compression = "gzip"
    
    // Retry configuration
    retry_on_failure {
      enabled         = true
      initial_interval = "5s"
      max_interval    = "30s"
      max_elapsed_time = "300s"
    }
    
    sending_queue {
      enabled      = true
      num_consumers = 10
      queue_size   = 5000
    }
  }
}

otelcol.auth.basic "grafana_cloud" {
  username = env("GRAFANA_TEMPO_USER")
  password = env("GRAFANA_TEMPO_PASSWORD")
}

// Loki (logs) remote write
loki.write "grafana_cloud" {
  endpoint {
    url = env("GRAFANA_LOKI_URL")
    
    basic_auth {
      username = env("GRAFANA_LOKI_USER")
      password = env("GRAFANA_LOKI_PASSWORD")
    }
    
    // Efficient batching for logs
    batch_config {
      max_wait = "5s"
      max_size = "1MB"
    }
  }
}

// ============================================================================
// HEALTH CHECK: Agent Health Monitoring
// ============================================================================

prometheus.exporter.self "alloy_metrics" {
  // Export Alloy's own metrics for monitoring the monitoring
}

prometheus.scrape "alloy_self" {
  targets    = prometheus.exporter.self.alloy_metrics.targets
  forward_to = [prometheus.remote_write.grafana_cloud.receiver]
  
  scrape_interval = "30s"
  job_name        = "alloy-self-monitoring"
}
