"""
🧪 AURA Intelligence System Integration Tests
Comprehensive end-to-end testing of the complete system
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any
import json

from fastapi.testclient import TestClient
from httpx import AsyncClient

from src.main import app
from src.services.knowledge_graph import KnowledgeGraphService
from src.services.topology_analyzer import TopologyAnalyzer
from src.services.agent_orchestrator import AgentOrchestrator
from src.services.prediction_engine import PredictionEngine
from src.models.topology import TopologySnapshot, ServiceSnapshot, MetricSnapshot
from src.models.knowledge_graph import ServiceNode, IncidentNode


class TestSystemIntegration:
    """Integration tests for the complete AURA Intelligence system"""
    
    @pytest.fixture
    def client(self):
        """Test client fixture"""
        return TestClient(app)
    
    @pytest.fixture
    async def async_client(self):
        """Async test client fixture"""
        async with AsyncClient(app=app, base_url="http://test") as ac:
            yield ac
    
    @pytest.fixture
    def sample_topology_snapshot(self):
        """Sample topology snapshot for testing"""
        return TopologySnapshot(
            id="test_snapshot_001",
            timestamp=datetime.utcnow(),
            topology_type="system",
            environment="test",
            cluster="test-cluster",
            region="us-west-2",
            services=[
                ServiceSnapshot(
                    id="service_001",
                    name="api-gateway",
                    version="1.0.0",
                    cpu_usage=75.5,
                    memory_usage=60.2,
                    request_rate=1500.0,
                    error_rate=2.1,
                    response_time=150.0,
                    health_score=0.85
                ),
                ServiceSnapshot(
                    id="service_002",
                    name="user-service",
                    version="2.1.0",
                    cpu_usage=45.3,
                    memory_usage=55.8,
                    request_rate=800.0,
                    error_rate=0.5,
                    response_time=95.0,
                    health_score=0.95
                ),
                ServiceSnapshot(
                    id="service_003",
                    name="payment-service",
                    version="1.5.2",
                    cpu_usage=85.7,
                    memory_usage=78.9,
                    request_rate=500.0,
                    error_rate=5.2,
                    response_time=250.0,
                    health_score=0.65
                )
            ],
            metrics=[
                MetricSnapshot(
                    id="metric_001",
                    name="system_cpu_usage",
                    value=68.5,
                    unit="percent",
                    source="system_monitor",
                    timestamp=datetime.utcnow(),
                    warning_threshold=70.0,
                    critical_threshold=90.0
                ),
                MetricSnapshot(
                    id="metric_002",
                    name="database_connections",
                    value=85.0,
                    unit="count",
                    source="database_monitor",
                    timestamp=datetime.utcnow(),
                    warning_threshold=80.0,
                    critical_threshold=100.0
                )
            ]
        )
    
    @pytest.fixture
    def sample_incident_data(self):
        """Sample incident data for testing"""
        return {
            "id": "incident_001",
            "title": "High Error Rate in Payment Service",
            "description": "Payment service showing elevated error rates and response times",
            "severity": "high",
            "type": "performance_degradation",
            "affected_services": ["payment-service", "api-gateway"],
            "tags": ["performance", "payment", "critical"],
            "metadata": {
                "detected_at": datetime.utcnow().isoformat(),
                "detection_method": "automated_monitoring"
            }
        }
    
    # === HEALTH CHECK TESTS ===
    
    def test_basic_health_check(self, client):
        """Test basic health check endpoint"""
        response = client.get("/api/v1/health")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert data["service"] == "AURA Intelligence API"
    
    @pytest.mark.asyncio
    async def test_detailed_health_check(self, async_client):
        """Test detailed health check with all services"""
        response = await async_client.get("/api/v1/health/detailed")
        assert response.status_code == 200
        
        data = response.json()
        assert "overall_status" in data
        assert "services" in data
        assert "services_healthy" in data
        assert "total_services" in data
    
    # === TOPOLOGY ANALYSIS TESTS ===
    
    @pytest.mark.asyncio
    async def test_topology_analysis_workflow(self, async_client, sample_topology_snapshot):
        """Test complete topology analysis workflow"""
        
        # 1. Submit topology for analysis
        response = await async_client.post(
            "/api/v1/topology/analyze",
            json={"snapshot": sample_topology_snapshot.dict()}
        )
        assert response.status_code == 200
        
        analysis_data = response.json()
        assert "analysis_id" in analysis_data
        assert "features" in analysis_data
        assert "anomaly_detection" in analysis_data
        
        # Verify topology features
        features = analysis_data["features"]
        assert "betti_numbers" in features
        assert "complexity_score" in features
        assert features["point_cloud_size"] > 0
        
        # Verify anomaly detection
        anomaly = analysis_data["anomaly_detection"]
        assert "is_anomaly" in anomaly
        assert "anomaly_score" in anomaly
        assert "confidence" in anomaly
        
        # 2. Get cached features
        snapshot_id = sample_topology_snapshot.id
        response = await async_client.get(f"/api/v1/topology/features/{snapshot_id}")
        
        if response.status_code == 200:
            cached_data = response.json()
            assert cached_data["snapshot_id"] == snapshot_id
            assert cached_data["cached"] == True
    
    @pytest.mark.asyncio
    async def test_topology_statistics(self, async_client):
        """Test topology statistics endpoint"""
        response = await async_client.get("/api/v1/topology/statistics")
        assert response.status_code == 200
        
        data = response.json()
        # Should handle case where no historical data exists
        assert "timestamp" in data
    
    # === KNOWLEDGE GRAPH TESTS ===
    
    @pytest.mark.asyncio
    async def test_knowledge_graph_workflow(self, async_client):
        """Test knowledge graph operations workflow"""
        
        # 1. Add a service
        service_data = {
            "name": "test-service",
            "type": "microservice",
            "version": "1.0.0",
            "status": "active",
            "metadata": {"environment": "test"}
        }
        
        response = await async_client.post(
            "/api/v1/knowledge-graph/services",
            json=service_data
        )
        assert response.status_code == 200
        
        service_response = response.json()
        assert service_response["success"] == True
        assert "service_id" in service_response["data"]
        service_id = service_response["data"]["service_id"]
        
        # 2. Add an incident
        incident_data = {
            "title": "Test Incident",
            "description": "Test incident for integration testing",
            "severity": "medium",
            "status": "investigating",
            "tags": ["test", "integration"],
            "affected_services": [service_id],
            "metadata": {"test": True}
        }
        
        response = await async_client.post(
            "/api/v1/knowledge-graph/incidents",
            json=incident_data
        )
        assert response.status_code == 200
        
        incident_response = response.json()
        assert incident_response["success"] == True
        assert "incident_id" in incident_response["data"]
        incident_id = incident_response["data"]["incident_id"]
        
        # 3. Test similar incidents search
        response = await async_client.get(
            f"/api/v1/knowledge-graph/incidents/{incident_id}/similar?limit=5"
        )
        assert response.status_code == 200
        
        similar_data = response.json()
        assert "incident_id" in similar_data
        assert "similar_incidents" in similar_data
        assert "total_found" in similar_data
    
    # === AGENT ORCHESTRATION TESTS ===
    
    @pytest.mark.asyncio
    async def test_agent_orchestration_workflow(self, async_client, sample_incident_data):
        """Test multi-agent incident analysis workflow"""
        
        # 1. Analyze incident using agents
        analysis_request = {
            "incident_data": sample_incident_data,
            "priority": "high",
            "timeout": 60
        }
        
        response = await async_client.post(
            "/api/v1/agents/analyze-incident",
            json=analysis_request
        )
        assert response.status_code == 200
        
        analysis_response = response.json()
        assert "analysis" in analysis_response
        assert "execution_time" in analysis_response
        assert "agents_used" in analysis_response
        
        # Verify analysis structure
        analysis = analysis_response["analysis"]
        assert "analysis_id" in analysis
        assert "incident_id" in analysis
        assert "confidence" in analysis
        assert "recommended_actions" in analysis
        
        # 2. Get agent status
        response = await async_client.get("/api/v1/agents/status")
        assert response.status_code == 200
        
        status_data = response.json()
        assert "agents" in status_data
        assert "total_agents" in status_data
        
        # 3. Get orchestrator metrics
        response = await async_client.get("/api/v1/agents/metrics")
        assert response.status_code == 200
        
        metrics_data = response.json()
        assert "overall" in metrics_data
        assert "agents" in metrics_data
    
    # === PREDICTION ENGINE TESTS ===
    
    @pytest.mark.asyncio
    async def test_prediction_workflow(self, async_client, sample_topology_snapshot):
        """Test failure prediction workflow"""
        
        # First, we need to analyze topology to get features
        topology_response = await async_client.post(
            "/api/v1/topology/analyze",
            json={"snapshot": sample_topology_snapshot.dict()}
        )
        assert topology_response.status_code == 200
        
        topology_data = topology_response.json()
        features = topology_data["features"]
        
        # 1. Predict failure
        prediction_request = {
            "topology_features": features,
            "prediction_horizon": 300
        }
        
        response = await async_client.post(
            "/api/v1/predictions/predict-failure",
            json=prediction_request
        )
        assert response.status_code == 200
        
        prediction_response = response.json()
        assert "prediction" in prediction_response
        assert "model_confidence" in prediction_response
        assert "prediction_time" in prediction_response
        
        # Verify prediction structure
        prediction = prediction_response["prediction"]
        assert "prediction_id" in prediction
        assert "failure_probability" in prediction
        assert "confidence" in prediction
        assert 0 <= prediction["failure_probability"] <= 1
        assert 0 <= prediction["confidence"] <= 1
        
        # 2. Get recent predictions
        response = await async_client.get("/api/v1/predictions/predictions/recent?limit=5")
        assert response.status_code == 200
        
        recent_data = response.json()
        assert "predictions" in recent_data
        assert "summary" in recent_data
    
    # === END-TO-END INTEGRATION TESTS ===
    
    @pytest.mark.asyncio
    async def test_complete_incident_analysis_pipeline(self, async_client, sample_topology_snapshot, sample_incident_data):
        """Test complete incident analysis pipeline from topology to prediction"""
        
        # 1. Analyze system topology
        topology_response = await async_client.post(
            "/api/v1/topology/analyze",
            json={"snapshot": sample_topology_snapshot.dict()}
        )
        assert topology_response.status_code == 200
        topology_data = topology_response.json()
        
        # 2. Add incident to knowledge graph
        incident_response = await async_client.post(
            "/api/v1/knowledge-graph/incidents",
            json={
                "title": sample_incident_data["title"],
                "description": sample_incident_data["description"],
                "severity": sample_incident_data["severity"],
                "tags": sample_incident_data["tags"],
                "affected_services": sample_incident_data["affected_services"]
            }
        )
        assert incident_response.status_code == 200
        
        # 3. Analyze incident with agents
        agent_response = await async_client.post(
            "/api/v1/agents/analyze-incident",
            json={"incident_data": sample_incident_data}
        )
        assert agent_response.status_code == 200
        agent_data = agent_response.json()
        
        # 4. Predict failure based on topology
        prediction_response = await async_client.post(
            "/api/v1/predictions/predict-failure",
            json={"topology_features": topology_data["features"]}
        )
        assert prediction_response.status_code == 200
        prediction_data = prediction_response.json()
        
        # 5. Verify complete pipeline results
        assert topology_data["anomaly_detection"]["is_anomaly"] is not None
        assert agent_data["analysis"]["confidence"] > 0
        assert prediction_data["prediction"]["failure_probability"] >= 0
        
        # Log pipeline results for verification
        print(f"\n🔍 Pipeline Results:")
        print(f"   Topology Anomaly: {topology_data['anomaly_detection']['is_anomaly']}")
        print(f"   Agent Confidence: {agent_data['analysis']['confidence']:.2f}")
        print(f"   Failure Probability: {prediction_data['prediction']['failure_probability']:.2f}")
    
    # === PERFORMANCE TESTS ===
    
    @pytest.mark.asyncio
    async def test_system_performance_under_load(self, async_client, sample_topology_snapshot):
        """Test system performance under concurrent load"""
        
        async def analyze_topology():
            response = await async_client.post(
                "/api/v1/topology/analyze",
                json={"snapshot": sample_topology_snapshot.dict()}
            )
            return response.status_code == 200
        
        # Run 10 concurrent topology analyses
        tasks = [analyze_topology() for _ in range(10)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Verify all requests succeeded
        successful_requests = sum(1 for result in results if result is True)
        assert successful_requests >= 8  # Allow for some failures under load
    
    # === ERROR HANDLING TESTS ===
    
    @pytest.mark.asyncio
    async def test_error_handling(self, async_client):
        """Test system error handling"""
        
        # Test invalid topology data
        response = await async_client.post(
            "/api/v1/topology/analyze",
            json={"invalid": "data"}
        )
        assert response.status_code == 422  # Validation error
        
        # Test non-existent resource
        response = await async_client.get("/api/v1/topology/features/non-existent-id")
        assert response.status_code == 404
        
        # Test invalid agent type
        response = await async_client.post(
            "/api/v1/agents/test-agent/invalid-agent-type",
            json={}
        )
        assert response.status_code == 400
    
    # === MONITORING AND METRICS TESTS ===
    
    def test_prometheus_metrics_endpoint(self, client):
        """Test Prometheus metrics endpoint"""
        response = client.get("/metrics")
        assert response.status_code == 200
        
        # Verify metrics format
        metrics_text = response.text
        assert "aura_http_requests_total" in metrics_text
        assert "aura_http_request_duration_seconds" in metrics_text
    
    @pytest.mark.asyncio
    async def test_system_metrics(self, async_client):
        """Test system metrics endpoint"""
        response = await async_client.get("/api/v1/health/metrics/system")
        assert response.status_code == 200
        
        data = response.json()
        assert "cpu" in data
        assert "memory" in data
        assert "process" in data
    
    @pytest.mark.asyncio
    async def test_application_metrics(self, async_client):
        """Test application metrics endpoint"""
        response = await async_client.get("/api/v1/health/metrics/application")
        assert response.status_code == 200
        
        data = response.json()
        assert "knowledge_graph" in data
        assert "topology_analyzer" in data
        assert "agent_orchestrator" in data
