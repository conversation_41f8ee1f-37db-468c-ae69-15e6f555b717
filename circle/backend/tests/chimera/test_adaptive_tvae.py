"""
Test suite for AdaptiveTVAE model

Tests the complete functionality of the adaptive topological VAE including:
- Model initialization and forward pass
- Topological feature extraction
- Adaptive architecture search
- Anomaly detection
- Model persistence
"""

import pytest
import torch
import numpy as np
from pathlib import Path
import tempfile
import json

from src.chimera.models.adaptive_tvae import AdaptiveTVAE
from src.chimera.utils.config import ChimeraConfig
from src.chimera.models.topological_features import TopologicalFeatures


class TestAdaptiveTVAE:
    """Test suite for AdaptiveTVAE model."""
    
    @pytest.fixture
    def config(self):
        """Create test configuration."""
        return ChimeraConfig()
    
    @pytest.fixture
    def model(self, config):
        """Create test model."""
        return AdaptiveTVAE(config)
    
    @pytest.fixture
    def sample_data(self):
        """Create sample test data."""
        batch_size = 32
        input_dim = 100
        return torch.randn(batch_size, input_dim)
    
    def test_model_initialization(self, model, config):
        """Test model initialization."""
        assert model is not None
        assert model.config == config
        assert hasattr(model, 'topological_extractor')
        assert hasattr(model, 'encoder')
        assert hasattr(model, 'decoder')
        assert hasattr(model, 'variational_bottleneck')
        assert hasattr(model, 'anomaly_scorer')
    
    def test_forward_pass(self, model, sample_data):
        """Test forward pass through the model."""
        outputs = model(sample_data)
        
        # Check output structure
        assert isinstance(outputs, dict)
        assert 'reconstruction' in outputs
        assert 'z_mean' in outputs
        assert 'z_log_var' in outputs
        assert 'loss' in outputs
        assert 'anomaly_scores' in outputs
        
        # Check shapes
        batch_size = sample_data.shape[0]
        assert outputs['reconstruction'].shape == sample_data.shape
        assert outputs['z_mean'].shape[0] == batch_size
        assert outputs['z_log_var'].shape[0] == batch_size
        assert outputs['anomaly_scores']['anomaly_score'].shape == (batch_size, 1)
    
    def test_loss_computation(self, model, sample_data):
        """Test loss computation."""
        outputs = model(sample_data)
        loss = outputs['loss']
        
        assert isinstance(loss, torch.Tensor)
        assert loss.requires_grad
        assert loss.item() > 0
        assert not torch.isnan(loss)
        assert not torch.isinf(loss)
    
    def test_anomaly_detection(self, model, sample_data):
        """Test anomaly detection functionality."""
        # Normal data
        normal_outputs = model(sample_data)
        normal_scores = normal_outputs['anomaly_scores']['anomaly_score']
        
        # Anomalous data (out of distribution)
        anomaly_data = torch.randn_like(sample_data) * 5 + 10
        anomaly_outputs = model(anomaly_data)
        anomaly_scores = anomaly_outputs['anomaly_scores']['anomaly_score']
        
        # Anomaly scores should be higher for anomalous data
        assert anomaly_scores.mean() > normal_scores.mean()
    
    def test_architecture_adaptation(self, model, sample_data):
        """Test neural architecture adaptation."""
        # Get initial architecture
        initial_config = model.encoder.get_current_architecture()
        initial_layers = len(initial_config['layers'])
        
        # Trigger architecture search
        model.encoder.search_budget = 10  # Small budget for testing
        model.encoder.enable_search = True
        
        # Run multiple forward passes
        for _ in range(5):
            outputs = model(sample_data)
            loss = outputs['loss']
            loss.backward()
        
        # Check if architecture has been explored
        search_history = model.encoder.architecture_history
        assert len(search_history) > 0
    
    def test_topological_features(self, model, sample_data):
        """Test topological feature extraction."""
        # Extract topological features
        topo_features = model.topological_extractor(sample_data)
        
        assert isinstance(topo_features, TopologicalFeatures)
        assert hasattr(topo_features, 'persistence_images')
        assert hasattr(topo_features, 'persistence_landscapes')
        assert hasattr(topo_features, 'combined_features')
        
        # Check feature dimensions
        batch_size = sample_data.shape[0]
        assert topo_features.combined_features.shape[0] == batch_size
        assert topo_features.combined_features.shape[1] == model.config.topological_config.embedding_dim
    
    def test_model_save_load(self, model, sample_data):
        """Test model saving and loading."""
        with tempfile.TemporaryDirectory() as tmpdir:
            # Save model
            save_path = Path(tmpdir) / "model.pt"
            model.save(save_path)
            
            assert save_path.exists()
            
            # Load model
            loaded_model = AdaptiveTVAE.load(save_path)
            
            # Compare outputs
            model.eval()
            loaded_model.eval()
            
            with torch.no_grad():
                original_outputs = model(sample_data)
                loaded_outputs = loaded_model(sample_data)
            
            # Check reconstruction similarity
            torch.testing.assert_close(
                original_outputs['reconstruction'],
                loaded_outputs['reconstruction'],
                rtol=1e-5,
                atol=1e-5
            )
    
    def test_gradient_flow(self, model, sample_data):
        """Test gradient flow through the model."""
        model.train()
        
        # Forward pass
        outputs = model(sample_data)
        loss = outputs['loss']
        
        # Backward pass
        loss.backward()
        
        # Check gradients
        for name, param in model.named_parameters():
            if param.requires_grad:
                assert param.grad is not None, f"No gradient for {name}"
                assert not torch.isnan(param.grad).any(), f"NaN gradient in {name}"
                assert not torch.isinf(param.grad).any(), f"Inf gradient in {name}"
    
    def test_batch_independence(self, model):
        """Test that batch processing is independent."""
        # Create two different batches
        batch1 = torch.randn(16, 100)
        batch2 = torch.randn(16, 100)
        combined = torch.cat([batch1, batch2], dim=0)
        
        model.eval()
        with torch.no_grad():
            # Process separately
            out1 = model(batch1)
            out2 = model(batch2)
            
            # Process together
            out_combined = model(combined)
        
        # Check that results match
        torch.testing.assert_close(
            torch.cat([out1['reconstruction'], out2['reconstruction']], dim=0),
            out_combined['reconstruction'],
            rtol=1e-5,
            atol=1e-5
        )
    
    def test_device_compatibility(self, config, sample_data):
        """Test model on different devices."""
        devices = ['cpu']
        if torch.cuda.is_available():
            devices.append('cuda')
        
        for device_str in devices:
            device = torch.device(device_str)
            model = AdaptiveTVAE(config).to(device)
            data = sample_data.to(device)
            
            outputs = model(data)
            
            # Check all outputs are on correct device
            assert outputs['reconstruction'].device == device
            assert outputs['loss'].device == device
    
    @pytest.mark.parametrize("batch_size", [1, 16, 32, 64])
    def test_variable_batch_size(self, model, batch_size):
        """Test model with different batch sizes."""
        data = torch.randn(batch_size, 100)
        outputs = model(data)
        
        assert outputs['reconstruction'].shape[0] == batch_size
        assert outputs['anomaly_scores']['anomaly_score'].shape[0] == batch_size
    
    def test_model_metrics(self, model, sample_data):
        """Test that model metrics are being tracked."""
        # Reset metrics
        model.metrics.reset()
        
        # Run forward passes
        for _ in range(10):
            outputs = model(sample_data)
        
        # Check metrics were updated
        # Note: In a real test, we'd check actual metric values
        assert hasattr(model.metrics, 'forward_pass_counter')
        assert hasattr(model.metrics, 'loss_histogram')


class TestModelIntegration:
    """Integration tests for the complete system."""
    
    @pytest.fixture
    def pipeline_config(self):
        """Create pipeline configuration."""
        from src.chimera.mlops.training_pipeline import TrainingConfig
        return TrainingConfig()
    
    def test_end_to_end_training(self, config, pipeline_config):
        """Test end-to-end training pipeline."""
        from src.chimera.mlops.training_pipeline import ChimeraTrainingPipeline, ChimeraDataset
        from torch.utils.data import DataLoader
        
        # Create pipeline
        pipeline = ChimeraTrainingPipeline(config, pipeline_config)
        
        # Create synthetic dataset
        data = np.random.randn(1000, 100)
        dataset = ChimeraDataset(data)
        loader = DataLoader(dataset, batch_size=32)
        
        # Train model (simplified)
        model = AdaptiveTVAE(config)
        optimizer = torch.optim.Adam(model.parameters())
        
        model.train()
        for epoch in range(2):  # Just 2 epochs for testing
            for batch in loader:
                optimizer.zero_grad()
                outputs = model(batch)
                loss = outputs['loss']
                loss.backward()
                optimizer.step()
        
        # Verify model trained
        assert model is not None
    
    def test_anomaly_detection_pipeline(self, model):
        """Test complete anomaly detection pipeline."""
        # Create test data with known anomalies
        normal_data = torch.randn(90, 100)
        anomaly_data = torch.randn(10, 100) * 3 + 5
        
        all_data = torch.cat([normal_data, anomaly_data], dim=0)
        true_labels = torch.cat([
            torch.zeros(90),
            torch.ones(10)
        ])
        
        # Get predictions
        model.eval()
        with torch.no_grad():
            outputs = model(all_data)
            scores = outputs['anomaly_scores']['anomaly_score'].squeeze()
        
        # Calculate AUC (simplified)
        from sklearn.metrics import roc_auc_score
        auc = roc_auc_score(true_labels.numpy(), scores.numpy())
        
        # Should detect anomalies better than random
        assert auc > 0.6  # Conservative threshold for test


if __name__ == "__main__":
    pytest.main([__file__, "-v"])