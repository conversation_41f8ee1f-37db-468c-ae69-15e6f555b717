#!/bin/bash

# 🚀 AURA Intelligence Backend Startup Script
# Professional development and production startup

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
ENV_FILE="$PROJECT_DIR/.env"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_dependencies() {
    log_info "Checking dependencies..."
    
    # Check Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3 is not installed"
        exit 1
    fi
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed"
        exit 1
    fi
    
    log_success "All dependencies are available"
}

setup_environment() {
    log_info "Setting up environment..."
    
    # Create .env file if it doesn't exist
    if [ ! -f "$ENV_FILE" ]; then
        log_info "Creating .env file from template..."
        cp "$PROJECT_DIR/.env.example" "$ENV_FILE"
        log_warning "Please update $ENV_FILE with your actual configuration"
    fi
    
    # Create necessary directories
    mkdir -p "$PROJECT_DIR/models"
    mkdir -p "$PROJECT_DIR/logs"
    mkdir -p "$PROJECT_DIR/data"
    
    log_success "Environment setup completed"
}

install_python_dependencies() {
    log_info "Installing Python dependencies..."
    
    cd "$PROJECT_DIR"
    
    # Create virtual environment if it doesn't exist
    if [ ! -d "venv" ]; then
        log_info "Creating virtual environment..."
        python3 -m venv venv
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Upgrade pip
    pip install --upgrade pip
    
    # Install dependencies
    pip install -r requirements.txt
    
    log_success "Python dependencies installed"
}

start_infrastructure() {
    log_info "Starting infrastructure services..."
    
    cd "$PROJECT_DIR"
    
    # Start infrastructure services
    docker-compose up -d postgres neo4j redis kafka zookeeper
    
    # Wait for services to be ready
    log_info "Waiting for services to be ready..."
    sleep 10
    
    # Check PostgreSQL
    log_info "Checking PostgreSQL connection..."
    until docker-compose exec -T postgres pg_isready -U aura -d aura_intelligence; do
        log_info "Waiting for PostgreSQL..."
        sleep 2
    done
    
    # Check Neo4j
    log_info "Checking Neo4j connection..."
    until curl -f http://localhost:7474 > /dev/null 2>&1; do
        log_info "Waiting for Neo4j..."
        sleep 2
    done
    
    # Check Redis
    log_info "Checking Redis connection..."
    until docker-compose exec -T redis redis-cli ping > /dev/null 2>&1; do
        log_info "Waiting for Redis..."
        sleep 2
    done
    
    log_success "Infrastructure services are ready"
}

run_database_migrations() {
    log_info "Running database migrations..."
    
    cd "$PROJECT_DIR"
    source venv/bin/activate
    
    # This would run Alembic migrations in production
    # For now, just log that migrations would run here
    log_info "Database migrations would run here (implement with Alembic)"
    
    log_success "Database migrations completed"
}

start_application() {
    log_info "Starting AURA Intelligence API..."
    
    cd "$PROJECT_DIR"
    source venv/bin/activate
    
    # Set environment variables
    export PYTHONPATH="$PROJECT_DIR:$PYTHONPATH"
    
    # Start the application
    if [ "$1" = "dev" ]; then
        log_info "Starting in development mode with hot reload..."
        uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload --log-level debug
    else
        log_info "Starting in production mode..."
        gunicorn src.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
    fi
}

run_tests() {
    log_info "Running tests..."
    
    cd "$PROJECT_DIR"
    source venv/bin/activate
    
    # Set test environment
    export TESTING=true
    export PYTHONPATH="$PROJECT_DIR:$PYTHONPATH"
    
    # Run tests
    pytest tests/ -v --cov=src --cov-report=html --cov-report=term
    
    log_success "Tests completed"
}

show_help() {
    echo "🚀 AURA Intelligence Backend Startup Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  setup     - Setup environment and install dependencies"
    echo "  start     - Start infrastructure and application (production mode)"
    echo "  dev       - Start in development mode with hot reload"
    echo "  test      - Run tests"
    echo "  infra     - Start only infrastructure services"
    echo "  stop      - Stop all services"
    echo "  clean     - Clean up containers and volumes"
    echo "  logs      - Show application logs"
    echo "  help      - Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 setup     # First time setup"
    echo "  $0 dev       # Development with hot reload"
    echo "  $0 start     # Production mode"
    echo "  $0 test      # Run tests"
}

# Main execution
case "${1:-help}" in
    setup)
        log_info "🚀 Setting up AURA Intelligence Backend..."
        check_dependencies
        setup_environment
        install_python_dependencies
        start_infrastructure
        run_database_migrations
        log_success "✅ Setup completed! Run '$0 dev' to start in development mode."
        ;;
    
    start)
        log_info "🚀 Starting AURA Intelligence Backend (Production)..."
        check_dependencies
        start_infrastructure
        start_application prod
        ;;
    
    dev)
        log_info "🚀 Starting AURA Intelligence Backend (Development)..."
        check_dependencies
        start_infrastructure
        start_application dev
        ;;
    
    test)
        log_info "🧪 Running AURA Intelligence Tests..."
        check_dependencies
        start_infrastructure
        run_tests
        ;;
    
    infra)
        log_info "🏗️ Starting Infrastructure Services..."
        check_dependencies
        start_infrastructure
        log_success "✅ Infrastructure services started"
        ;;
    
    stop)
        log_info "🛑 Stopping all services..."
        cd "$PROJECT_DIR"
        docker-compose down
        log_success "✅ All services stopped"
        ;;
    
    clean)
        log_info "🧹 Cleaning up containers and volumes..."
        cd "$PROJECT_DIR"
        docker-compose down -v --remove-orphans
        docker system prune -f
        log_success "✅ Cleanup completed"
        ;;
    
    logs)
        log_info "📋 Showing application logs..."
        cd "$PROJECT_DIR"
        docker-compose logs -f aura-api
        ;;
    
    help|*)
        show_help
        ;;
esac
