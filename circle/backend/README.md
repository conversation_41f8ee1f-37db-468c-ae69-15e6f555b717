# 🚀 AURA Intelligence Backend

**Advanced AIOps Platform with Knowledge Graph and Topological Analysis**

A production-grade backend system that combines GPU-accelerated topological data analysis, knowledge graphs, and multi-agent reasoning for intelligent system failure prediction and automated remediation.

## 🎯 **SYSTEM OVERVIEW**

AURA Intelligence transforms raw system telemetry into actionable insights using:

- **🧠 Knowledge Graph**: Neo4j-based system knowledge with causal reasoning
- **🔍 Topological Analysis**: GPU-accelerated persistent homology for anomaly detection  
- **🤖 Multi-Agent System**: 7 specialized agents for intelligent incident analysis
- **🧠 ML Prediction Engine**: Advanced failure prediction with confidence estimation
- **⚡ Real-time Processing**: Sub-100ms topology analysis with streaming updates

## 🏗️ **ARCHITECTURE**

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   API Gateway   │    │   Agent         │
│   Dashboard     │◄──►│   (FastAPI)     │◄──►│   Orchestrator  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Knowledge     │    │   Topology      │    │   Prediction    │
│   Graph (Neo4j) │◄──►│   Analyzer      │◄──►│   Engine (ML)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                    ┌─────────────────┐
                    │   Data Pipeline │
                    │   (Kafka/Redis) │
                    └─────────────────┘
```

## 🚀 **QUICK START**

### Prerequisites
- Python 3.11+
- Docker & Docker Compose
- 8GB+ RAM (16GB recommended)
- NVIDIA GPU (optional, for acceleration)

### 1. <PERSON><PERSON> and Setup
```bash
git clone <repository>
cd aura-intelligence/backend

# Setup environment and dependencies
./scripts/start.sh setup
```

### 2. Start Development Environment
```bash
# Start all services in development mode
./scripts/start.sh dev
```

### 3. Verify Installation
```bash
# Check system health
curl http://localhost:8000/api/v1/health

# View API documentation
open http://localhost:8000/docs
```

## 📊 **CORE SERVICES**

### Knowledge Graph Service
- **Neo4j Integration**: Hyper-relational incident storage
- **Similarity Search**: Vector-based incident matching
- **Causal Reasoning**: Root cause analysis with confidence scoring
- **Real-time Updates**: Streaming knowledge graph updates

### Topology Analyzer  
- **Persistent Homology**: GPU-accelerated TDA computation
- **Anomaly Detection**: Topological signature-based anomaly detection
- **Multi-scale Analysis**: Betti numbers across multiple dimensions
- **Pattern Recognition**: Known vulnerability signature matching

### Agent Orchestrator
- **7 Specialized Agents**: Intent, Topology, Temporal, Causal, Remediation, Integration, Synthesis
- **Parallel Execution**: Optimized agent orchestration with dependency management
- **Context Sharing**: Inter-agent knowledge sharing and conflict resolution
- **Performance Tracking**: Agent-level metrics and success rates

### Prediction Engine
- **ML-based Prediction**: Random Forest + Gradient Boosting models
- **Multi-horizon Forecasting**: 10s to 24h prediction horizons
- **Confidence Estimation**: Uncertainty quantification for predictions
- **Continuous Learning**: Online model updates from new incidents

## 🔧 **CONFIGURATION**

### Environment Variables
```bash
# Core Configuration
DEBUG=true
SECRET_KEY=your-secret-key-32-chars-minimum
HOST=0.0.0.0
PORT=8000

# Database Connections
NEO4J_URI=bolt://localhost:7687
NEO4J_PASSWORD=your-neo4j-password
POSTGRES_HOST=localhost
POSTGRES_PASSWORD=your-postgres-password
REDIS_HOST=localhost

# ML Configuration
GPU_ENABLED=true
MODEL_PATH=./models
PREDICTION_HORIZON_SECONDS=300

# TDA Configuration
TDA_MAX_DIMENSION=2
PERSISTENCE_THRESHOLD=0.1
```

### Docker Compose Services
- **aura-api**: Main FastAPI application
- **postgres**: Primary database
- **neo4j**: Knowledge graph database
- **redis**: Caching and session storage
- **kafka**: Message streaming
- **prometheus**: Metrics collection
- **grafana**: Monitoring dashboards

## 📡 **API ENDPOINTS**

### Health & Status
```bash
GET  /api/v1/health                    # Basic health check
GET  /api/v1/health/detailed           # Detailed service health
GET  /api/v1/health/readiness          # Kubernetes readiness probe
GET  /metrics                          # Prometheus metrics
```

### Knowledge Graph
```bash
POST /api/v1/knowledge-graph/services          # Add service
POST /api/v1/knowledge-graph/incidents         # Add incident
GET  /api/v1/knowledge-graph/incidents/{id}/similar    # Find similar incidents
GET  /api/v1/knowledge-graph/incidents/{id}/root-cause # Root cause analysis
POST /api/v1/knowledge-graph/relationships/causal      # Add causal relationship
```

### Topology Analysis
```bash
POST /api/v1/topology/analyze          # Analyze topology snapshot
GET  /api/v1/topology/features/{id}    # Get cached features
GET  /api/v1/topology/anomalies        # Recent anomalies
GET  /api/v1/topology/statistics       # Analysis statistics
GET  /api/v1/topology/trends           # Topology trends
```

### Agent Orchestration
```bash
POST /api/v1/agents/analyze-incident   # Multi-agent incident analysis
GET  /api/v1/agents/status             # Agent status
GET  /api/v1/agents/metrics            # Orchestrator metrics
GET  /api/v1/agents/execution-history  # Execution history
POST /api/v1/agents/test-agent/{type}  # Test individual agent
```

### Predictions
```bash
POST /api/v1/predictions/predict-failure       # Predict system failure
GET  /api/v1/predictions/predictions/recent    # Recent predictions
POST /api/v1/predictions/train-model           # Train ML model
GET  /api/v1/predictions/model/metrics         # Model performance
GET  /api/v1/predictions/analytics/trends      # Prediction trends
```

## 🧪 **TESTING**

### Run Test Suite
```bash
# Run all tests
./scripts/start.sh test

# Run specific test categories
pytest tests/test_system_integration.py -v
pytest tests/test_topology_analyzer.py -v
pytest tests/test_knowledge_graph.py -v
```

### Test Coverage
- **Integration Tests**: End-to-end system workflows
- **Unit Tests**: Individual service testing
- **Performance Tests**: Load and stress testing
- **API Tests**: Comprehensive endpoint testing

## 📈 **MONITORING**

### Metrics Collection
- **Prometheus**: System and application metrics
- **Grafana**: Real-time dashboards
- **Jaeger**: Distributed tracing
- **Structured Logging**: JSON-formatted logs

### Key Metrics
- Request latency and throughput
- Topology analysis performance
- Agent execution success rates
- Prediction model accuracy
- Knowledge graph query performance

## 🔒 **SECURITY**

### Authentication
- JWT-based authentication
- Role-based access control
- API key management
- Session management

### Data Protection
- Encrypted data at rest
- TLS encryption in transit
- Input validation and sanitization
- SQL injection prevention

## 🚀 **DEPLOYMENT**

### Development
```bash
./scripts/start.sh dev
```

### Production
```bash
# Build production image
docker build -t aura-intelligence:latest .

# Deploy with Docker Compose
docker-compose -f docker-compose.prod.yml up -d
```

### Kubernetes
```bash
# Apply Kubernetes manifests
kubectl apply -f k8s/
```

## 📚 **DOCUMENTATION**

- **API Documentation**: http://localhost:8000/docs
- **Architecture Guide**: `docs/architecture.md`
- **Deployment Guide**: `docs/deployment.md`
- **Development Guide**: `docs/development.md`

## 🤝 **CONTRIBUTING**

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 **LICENSE**

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 **SUPPORT**

- **Issues**: GitHub Issues
- **Documentation**: `/docs` directory
- **API Reference**: http://localhost:8000/docs

---

**Built with ❤️ for intelligent system operations**
