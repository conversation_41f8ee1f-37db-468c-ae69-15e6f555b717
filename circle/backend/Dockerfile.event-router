# Event Router Dockerfile
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Set work directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.minimal.txt .
RUN pip install --no-cache-dir -r requirements.minimal.txt

# Copy source code
COPY src/ ./src/

# Create entry point for Event Router
RUN echo '#!/usr/bin/env python3' > /app/event_router.py && \
    echo 'import sys' >> /app/event_router.py && \
    echo 'sys.path.append("/app")' >> /app/event_router.py && \
    echo 'from src.chimera.integration.event_router import main' >> /app/event_router.py && \
    echo 'if __name__ == "__main__":' >> /app/event_router.py && \
    echo '    main()' >> /app/event_router.py && \
    chmod +x /app/event_router.py

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Run Event Router
CMD ["python", "/app/event_router.py"]
