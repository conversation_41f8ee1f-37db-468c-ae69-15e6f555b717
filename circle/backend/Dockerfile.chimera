# Chimera Shadow Mode Dockerfile
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Set work directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.minimal.txt .
RUN pip install --no-cache-dir -r requirements.minimal.txt

# Copy source code
COPY src/ ./src/

# Create entry point for Chimera shadow mode
RUN echo '#!/usr/bin/env python3' > /app/chimera_shadow.py && \
    echo 'import sys' >> /app/chimera_shadow.py && \
    echo 'sys.path.append("/app")' >> /app/chimera_shadow.py && \
    echo 'from src.chimera.examples.demo_chimera import main' >> /app/chimera_shadow.py && \
    echo 'if __name__ == "__main__":' >> /app/chimera_shadow.py && \
    echo '    main()' >> /app/chimera_shadow.py && \
    chmod +x /app/chimera_shadow.py

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Run Chimera shadow mode
CMD ["python", "/app/chimera_shadow.py"]
