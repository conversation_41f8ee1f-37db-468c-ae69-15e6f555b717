# 🚀 AURA Intelligence Backend Dependencies
# Production-grade MLOps + Knowledge Graph + Real-time Processing

# === CORE FRAMEWORK ===
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# === KNOWLEDGE GRAPH ===
neo4j==5.15.0
py2neo==2021.2.4
networkx==3.2.1
# graph-tool==2.57  # Commented out - not available in slim image  # High-performance graph analysis

# === MACHINE LEARNING ===
torch==2.1.1
torch-geometric==2.4.0
scikit-learn==1.3.2
xgboost==2.0.2
lightgbm==4.1.0

# === TOPOLOGICAL DATA ANALYSIS ===
gudhi==3.8.0
ripser==0.6.4
scikit-tda==1.0.0
giotto-tda==0.6.0
persim==0.3.2

# === REAL-TIME PROCESSING ===
kafka-python==2.0.2
redis==5.0.1
celery==5.3.4
apache-airflow==2.7.3

# === DATA PROCESSING ===
pandas==2.1.4
numpy==1.25.2
polars==0.20.2  # High-performance DataFrame
pyarrow==14.0.1

# === DATABASE ===
sqlalchemy==2.0.23
alembic==1.13.1
asyncpg==0.29.0  # PostgreSQL async
motor==3.3.2     # MongoDB async

# === MONITORING & OBSERVABILITY ===
prometheus-client==0.19.0
structlog==23.2.0
opentelemetry-api==1.21.0
opentelemetry-sdk==1.21.0
opentelemetry-instrumentation-fastapi==0.42b0

# === SECURITY ===
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# === TESTING ===
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2

# === DEVELOPMENT ===
black==23.11.0
isort==5.12.0
mypy==1.7.1
pre-commit==3.6.0

# === DEPLOYMENT ===
docker==6.1.3
kubernetes==28.1.0
gunicorn==21.2.0

# === SPECIALIZED LIBRARIES ===
# Causal Inference
causal-learn==*******
dowhy==0.10.1

# Graph Neural Networks
dgl==1.1.3
torch-scatter==2.1.2
torch-sparse==0.6.18

# Time Series
prophet==1.1.5
statsmodels==0.14.0

# Distributed Computing
ray[default]==2.8.0
dask[complete]==2023.11.0

# Vector Database
chromadb==0.4.18
weaviate-client==3.25.3

# API Documentation
sphinx==7.2.6
sphinx-rtd-theme==1.3.0
