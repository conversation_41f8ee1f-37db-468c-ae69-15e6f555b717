# Simplified requirements for Chimera Shadow Mode
# Core dependencies with PyTorch

# === CORE FRAMEWORK ===
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# === MACHINE LEARNING ===
torch>=2.0.0
scikit-learn==1.3.2
numpy>=1.21.0
pandas>=1.3.0

# === NETWORKING ===
aiohttp>=3.8.0
nats-py>=2.0.0
redis>=4.0.0

# === MONITORING ===
prometheus-client>=0.17.0
structlog>=23.0.0

# === UTILITIES ===
colorama>=0.4.0
tenacity>=8.0.0
