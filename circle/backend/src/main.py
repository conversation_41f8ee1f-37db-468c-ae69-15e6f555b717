"""
🚀 AURA Intelligence Backend - Main Application
Production-grade FastAPI server with Knowledge Graph + MLOps integration
"""

import asyncio
import logging
from contextlib import asynccontextmanager
from typing import Dict, Any

import structlog
from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from prometheus_client import make_asgi_app
import uvicorn

from src.core.config import get_settings
from src.core.logging import setup_logging
from src.api.routes import health, knowledge_graph, topology, agents, predictions, websocket
from src.services.knowledge_graph import KnowledgeGraphService
from src.services.topology_analyzer import TopologyAnalyzer
from src.services.agent_orchestrator import AgentOrchestrator
from src.services.prediction_engine import PredictionEngine
from src.middleware.auth import AuthMiddleware
from src.middleware.monitoring import MonitoringMiddleware

# Global services
services: Dict[str, Any] = {}

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management"""
    logger = structlog.get_logger("aura.startup")
    
    try:
        # Initialize logging
        setup_logging()
        logger.info("🚀 Starting AURA Intelligence Backend")
        
        # Initialize services
        settings = get_settings()
        
        # Knowledge Graph Service
        logger.info("📊 Initializing Knowledge Graph Service")
        kg_service = KnowledgeGraphService(
            neo4j_uri=settings.neo4j_uri,
            neo4j_user=settings.neo4j_user,
            neo4j_password=settings.neo4j_password
        )
        await kg_service.initialize()
        services["knowledge_graph"] = kg_service
        
        # Topology Analyzer
        logger.info("🔍 Initializing Topology Analyzer")
        topology_analyzer = TopologyAnalyzer(
            gpu_enabled=settings.gpu_enabled,
            batch_size=settings.topology_batch_size
        )
        await topology_analyzer.initialize()
        services["topology_analyzer"] = topology_analyzer
        
        # Agent Orchestrator
        logger.info("🤖 Initializing Agent Orchestrator")
        agent_orchestrator = AgentOrchestrator(
            knowledge_graph=kg_service,
            topology_analyzer=topology_analyzer
        )
        await agent_orchestrator.initialize()
        services["agent_orchestrator"] = agent_orchestrator
        
        # Prediction Engine
        logger.info("🧠 Initializing Prediction Engine")
        prediction_engine = PredictionEngine(
            knowledge_graph=kg_service,
            topology_analyzer=topology_analyzer,
            model_path=settings.model_path
        )
        await prediction_engine.initialize()
        services["prediction_engine"] = prediction_engine
        
        logger.info("✅ All services initialized successfully")
        
        yield
        
    except Exception as e:
        logger.error("❌ Failed to initialize services", error=str(e))
        raise
    finally:
        # Cleanup
        logger.info("🧹 Shutting down services")
        for service_name, service in services.items():
            try:
                if hasattr(service, 'cleanup'):
                    await service.cleanup()
                logger.info(f"✅ {service_name} cleaned up")
            except Exception as e:
                logger.error(f"❌ Error cleaning up {service_name}", error=str(e))

# Create FastAPI application
app = FastAPI(
    title="AURA Intelligence API",
    description="Advanced AIOps platform with Knowledge Graph and Topological Analysis",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(GZipMiddleware, minimum_size=1000)
app.add_middleware(AuthMiddleware)
app.add_middleware(MonitoringMiddleware)

# Add Prometheus metrics endpoint
metrics_app = make_asgi_app()
app.mount("/metrics", metrics_app)

# Include routers
app.include_router(health.router, prefix="/api/v1", tags=["health"])
app.include_router(knowledge_graph.router, prefix="/api/v1", tags=["knowledge-graph"])
app.include_router(topology.router, prefix="/api/v1", tags=["topology"])
app.include_router(agents.router, prefix="/api/v1", tags=["agents"])
app.include_router(predictions.router, prefix="/api/v1", tags=["predictions"])
app.include_router(websocket.router, tags=["websocket"])

# Dependency injection
def get_knowledge_graph_service() -> KnowledgeGraphService:
    """Get Knowledge Graph service instance"""
    if "knowledge_graph" not in services:
        raise HTTPException(status_code=503, detail="Knowledge Graph service not available")
    return services["knowledge_graph"]

def get_topology_analyzer() -> TopologyAnalyzer:
    """Get Topology Analyzer service instance"""
    if "topology_analyzer" not in services:
        raise HTTPException(status_code=503, detail="Topology Analyzer service not available")
    return services["topology_analyzer"]

def get_agent_orchestrator() -> AgentOrchestrator:
    """Get Agent Orchestrator service instance"""
    if "agent_orchestrator" not in services:
        raise HTTPException(status_code=503, detail="Agent Orchestrator service not available")
    return services["agent_orchestrator"]

def get_prediction_engine() -> PredictionEngine:
    """Get Prediction Engine service instance"""
    if "prediction_engine" not in services:
        raise HTTPException(status_code=503, detail="Prediction Engine service not available")
    return services["prediction_engine"]

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "🚀 AURA Intelligence API",
        "version": "1.0.0",
        "status": "operational",
        "services": list(services.keys())
    }

@app.get("/api/v1/status")
async def get_status():
    """Get system status"""
    logger = structlog.get_logger("aura.status")
    
    status = {
        "status": "healthy",
        "services": {},
        "timestamp": asyncio.get_event_loop().time()
    }
    
    for service_name, service in services.items():
        try:
            if hasattr(service, 'health_check'):
                health = await service.health_check()
                status["services"][service_name] = health
            else:
                status["services"][service_name] = {"status": "running"}
        except Exception as e:
            logger.error(f"Health check failed for {service_name}", error=str(e))
            status["services"][service_name] = {"status": "error", "error": str(e)}
    
    return status

if __name__ == "__main__":
    settings = get_settings()
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level="info"
    )
