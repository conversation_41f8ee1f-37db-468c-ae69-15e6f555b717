"""
🧠 AURA Intelligence Prediction Engine
Advanced ML-based failure prediction with topological features
"""

import asyncio
from typing import Dict, List, Optional, Any, Tu<PERSON>
from datetime import datetime, timedelta
import numpy as np
import pickle
import os

import structlog
from sklearn.ensemble import RandomForestClassifier, GradientBoostingRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, precision_score, recall_score

from src.core.config import get_settings
from src.services.knowledge_graph import KnowledgeGraphService
from src.services.topology_analyzer import TopologyAnalyzer
from src.models.topology import TopologyFeatures, TopologyPrediction


class PredictionEngine:
    """
    Advanced Prediction Engine for AURA Intelligence
    
    Features:
    - ML-based failure prediction using topological features
    - Multi-horizon prediction (seconds to hours)
    - Confidence estimation and uncertainty quantification
    - Continuous learning from new incidents
    - Integration with knowledge graph for context
    """
    
    def __init__(self, knowledge_graph: KnowledgeGraphService,
                 topology_analyzer: TopologyAnalyzer,
                 model_path: str = "./models"):
        self.logger = structlog.get_logger("aura.prediction")
        self.settings = get_settings()
        self.knowledge_graph = knowledge_graph
        self.topology_analyzer = topology_analyzer
        self.model_path = model_path
        
        # ML Models
        self.failure_classifier = None
        self.time_to_failure_regressor = None
        self.impact_predictor = None
        self.scaler = StandardScaler()
        
        # Training data
        self.training_features: List[np.ndarray] = []
        self.training_labels: List[int] = []  # 0 = no failure, 1 = failure
        self.training_times: List[float] = []  # time to failure
        
        # Model performance tracking
        self.model_metrics = {
            "accuracy": 0.0,
            "precision": 0.0,
            "recall": 0.0,
            "last_trained": None,
            "training_samples": 0
        }
        
        # Prediction cache
        self.prediction_cache: Dict[str, TopologyPrediction] = {}
        
    async def initialize(self) -> None:
        """Initialize prediction engine"""
        try:
            self.logger.info("🧠 Initializing Prediction Engine")
            
            # Create model directory
            os.makedirs(self.model_path, exist_ok=True)
            
            # Load existing models if available
            await self._load_models()
            
            # Initialize models if not loaded
            if self.failure_classifier is None:
                await self._initialize_models()
            
            self.logger.info("✅ Prediction Engine initialized successfully")
            
        except Exception as e:
            self.logger.error("❌ Failed to initialize Prediction Engine", error=str(e))
            raise
    
    async def _initialize_models(self) -> None:
        """Initialize ML models with default parameters"""
        
        # Failure classification model
        self.failure_classifier = RandomForestClassifier(
            n_estimators=100,
            max_depth=10,
            random_state=42,
            class_weight='balanced'
        )
        
        # Time to failure regression model
        self.time_to_failure_regressor = GradientBoostingRegressor(
            n_estimators=100,
            max_depth=6,
            learning_rate=0.1,
            random_state=42
        )
        
        # Impact prediction model
        self.impact_predictor = RandomForestClassifier(
            n_estimators=50,
            max_depth=8,
            random_state=42
        )
        
        self.logger.info("ML models initialized with default parameters")
    
    async def predict_failure(self, topology_features: TopologyFeatures) -> TopologyPrediction:
        """
        Predict system failure based on topological features
        """
        prediction_id = f"pred_{datetime.utcnow().strftime('%Y%m%d_%H%M%S_%f')}"
        
        try:
            # Extract feature vector
            feature_vector = self._extract_feature_vector(topology_features)
            
            # Normalize features
            if hasattr(self.scaler, 'mean_'):
                normalized_features = self.scaler.transform([feature_vector])[0]
            else:
                normalized_features = feature_vector
            
            # Predict failure probability
            if self.failure_classifier is not None and hasattr(self.failure_classifier, 'predict_proba'):
                failure_proba = self.failure_classifier.predict_proba([normalized_features])[0]
                failure_probability = failure_proba[1] if len(failure_proba) > 1 else 0.5
            else:
                # Fallback: use topological complexity as proxy
                failure_probability = min(topology_features.complexity_score / 10.0, 1.0)
            
            # Predict time to failure
            time_to_failure = None
            if (failure_probability > 0.5 and 
                self.time_to_failure_regressor is not None and 
                hasattr(self.time_to_failure_regressor, 'predict')):
                try:
                    predicted_time = self.time_to_failure_regressor.predict([normalized_features])[0]
                    time_to_failure = max(predicted_time, 10.0)  # At least 10 seconds
                except:
                    time_to_failure = 300.0  # Default 5 minutes
            
            # Determine failure type based on topological signature
            predicted_failure_type = self._classify_failure_type(topology_features)
            
            # Identify affected services (simplified)
            affected_services = self._predict_affected_services(topology_features)
            
            # Calculate impact score
            impact_score = self._calculate_impact_score(topology_features, failure_probability)
            
            # Generate warning indicators
            warning_indicators = self._generate_warning_indicators(topology_features)
            
            # Calculate confidence based on model performance and feature quality
            confidence = self._calculate_prediction_confidence(
                topology_features, failure_probability
            )
            
            prediction = TopologyPrediction(
                prediction_id=prediction_id,
                snapshot_id=topology_features.snapshot_id,
                failure_probability=failure_probability,
                time_to_failure=time_to_failure,
                confidence=confidence,
                predicted_failure_type=predicted_failure_type,
                affected_services=affected_services,
                impact_score=impact_score,
                warning_indicators=warning_indicators,
                critical_features=self._identify_critical_features(topology_features),
                prediction_timestamp=datetime.utcnow(),
                prediction_horizon=self.settings.prediction_horizon_seconds
            )
            
            # Cache prediction
            self.prediction_cache[prediction_id] = prediction
            
            self.logger.info("Failure prediction completed", 
                           prediction_id=prediction_id,
                           failure_probability=failure_probability,
                           confidence=confidence)
            
            return prediction
            
        except Exception as e:
            self.logger.error("Failed to predict failure", 
                            prediction_id=prediction_id, error=str(e))
            raise
    
    def _extract_feature_vector(self, topology_features: TopologyFeatures) -> np.ndarray:
        """Extract numerical feature vector from topology features"""
        return np.array([
            # Betti numbers
            topology_features.betti_numbers.beta_0,
            topology_features.betti_numbers.beta_1,
            topology_features.betti_numbers.beta_2,
            
            # Persistence statistics
            topology_features.mean_persistence_0,
            topology_features.mean_persistence_1,
            topology_features.mean_persistence_2,
            topology_features.max_persistence_0,
            topology_features.max_persistence_1,
            topology_features.max_persistence_2,
            topology_features.total_persistence_0,
            topology_features.total_persistence_1,
            topology_features.total_persistence_2,
            
            # Complexity measures
            topology_features.complexity_score,
            topology_features.total_features,
            topology_features.point_cloud_size,
            
            # Derived features
            topology_features.betti_numbers.euler_characteristic,
            topology_features.persistence_diagram.persistence_entropy,
            topology_features.stability_score,
            topology_features.noise_level
        ])
    
    def _classify_failure_type(self, topology_features: TopologyFeatures) -> Optional[str]:
        """Classify failure type based on topological signature"""
        
        # Simple rule-based classification (would use ML in production)
        if topology_features.betti_numbers.beta_1 > 5:
            return "cascade_failure"
        elif topology_features.complexity_score > 3.0:
            return "resource_exhaustion"
        elif topology_features.mean_persistence_1 > 0.7:
            return "service_isolation"
        elif topology_features.total_persistence_2 > 1.0:
            return "dependency_failure"
        else:
            return "unknown"
    
    def _predict_affected_services(self, topology_features: TopologyFeatures) -> List[str]:
        """Predict which services might be affected"""
        
        # Simplified prediction based on topology complexity
        affected_services = []
        
        if topology_features.complexity_score > 2.0:
            affected_services.extend(["payment-service", "user-service"])
        
        if topology_features.betti_numbers.beta_1 > 3:
            affected_services.extend(["database-service", "cache-service"])
        
        if topology_features.mean_persistence_1 > 0.5:
            affected_services.append("api-gateway")
        
        return list(set(affected_services))  # Remove duplicates
    
    def _calculate_impact_score(self, topology_features: TopologyFeatures, 
                               failure_probability: float) -> float:
        """Calculate predicted impact score"""
        
        # Combine failure probability with topological complexity
        base_impact = failure_probability * 0.7
        complexity_impact = min(topology_features.complexity_score / 10.0, 0.3)
        
        return min(base_impact + complexity_impact, 1.0)
    
    def _generate_warning_indicators(self, topology_features: TopologyFeatures) -> List[str]:
        """Generate warning indicators based on topological features"""
        
        indicators = []
        
        if topology_features.betti_numbers.beta_0 < 2:
            indicators.append("system_fragmentation")
        
        if topology_features.betti_numbers.beta_1 > 4:
            indicators.append("excessive_cycles")
        
        if topology_features.complexity_score > 2.5:
            indicators.append("high_complexity")
        
        if topology_features.mean_persistence_1 > 0.6:
            indicators.append("persistent_anomalies")
        
        if topology_features.stability_score < 0.7:
            indicators.append("topological_instability")
        
        return indicators
    
    def _identify_critical_features(self, topology_features: TopologyFeatures) -> List[str]:
        """Identify critical topological features"""
        
        critical_features = []
        
        if topology_features.betti_numbers.beta_1 > 3:
            critical_features.append("beta_1_loops")
        
        if topology_features.complexity_score > 2.0:
            critical_features.append("complexity_score")
        
        if topology_features.max_persistence_1 > 0.8:
            critical_features.append("max_persistence_1")
        
        return critical_features
    
    def _calculate_prediction_confidence(self, topology_features: TopologyFeatures,
                                       failure_probability: float) -> float:
        """Calculate prediction confidence"""
        
        # Base confidence from model performance
        base_confidence = self.model_metrics.get("accuracy", 0.5)
        
        # Adjust based on feature quality
        feature_quality = topology_features.stability_score
        
        # Adjust based on prediction certainty
        certainty_bonus = abs(failure_probability - 0.5) * 0.4
        
        confidence = min(base_confidence * feature_quality + certainty_bonus, 1.0)
        
        return max(confidence, 0.1)  # Minimum confidence
    
    async def train_model(self, training_data: List[Dict[str, Any]]) -> Dict[str, float]:
        """Train prediction models with new data"""
        
        try:
            if len(training_data) < 10:
                self.logger.warning("Insufficient training data", samples=len(training_data))
                return self.model_metrics
            
            # Prepare training data
            features = []
            labels = []
            times = []
            
            for sample in training_data:
                if 'topology_features' in sample and 'failure_occurred' in sample:
                    feature_vector = self._extract_feature_vector(sample['topology_features'])
                    features.append(feature_vector)
                    labels.append(1 if sample['failure_occurred'] else 0)
                    times.append(sample.get('time_to_failure', 0.0))
            
            if len(features) < 10:
                self.logger.warning("Insufficient valid training samples", samples=len(features))
                return self.model_metrics
            
            X = np.array(features)
            y = np.array(labels)
            times = np.array(times)
            
            # Normalize features
            X_scaled = self.scaler.fit_transform(X)
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X_scaled, y, test_size=0.2, random_state=42, stratify=y
            )
            
            # Train failure classifier
            self.failure_classifier.fit(X_train, y_train)
            
            # Train time-to-failure regressor (only on failure samples)
            failure_indices = y == 1
            if np.sum(failure_indices) > 5:
                X_failure = X_scaled[failure_indices]
                times_failure = times[failure_indices]
                self.time_to_failure_regressor.fit(X_failure, times_failure)
            
            # Evaluate model
            y_pred = self.failure_classifier.predict(X_test)
            
            self.model_metrics = {
                "accuracy": accuracy_score(y_test, y_pred),
                "precision": precision_score(y_test, y_pred, zero_division=0),
                "recall": recall_score(y_test, y_pred, zero_division=0),
                "last_trained": datetime.utcnow().isoformat(),
                "training_samples": len(features)
            }
            
            # Save models
            await self._save_models()
            
            self.logger.info("Model training completed", 
                           accuracy=self.model_metrics["accuracy"],
                           samples=len(features))
            
            return self.model_metrics
            
        except Exception as e:
            self.logger.error("Failed to train model", error=str(e))
            raise
    
    async def _save_models(self) -> None:
        """Save trained models to disk"""
        try:
            model_files = {
                "failure_classifier.pkl": self.failure_classifier,
                "time_regressor.pkl": self.time_to_failure_regressor,
                "impact_predictor.pkl": self.impact_predictor,
                "scaler.pkl": self.scaler,
                "metrics.pkl": self.model_metrics
            }
            
            for filename, model in model_files.items():
                if model is not None:
                    filepath = os.path.join(self.model_path, filename)
                    with open(filepath, 'wb') as f:
                        pickle.dump(model, f)
            
            self.logger.info("Models saved successfully", path=self.model_path)
            
        except Exception as e:
            self.logger.error("Failed to save models", error=str(e))
    
    async def _load_models(self) -> None:
        """Load trained models from disk"""
        try:
            model_files = {
                "failure_classifier.pkl": "failure_classifier",
                "time_regressor.pkl": "time_to_failure_regressor", 
                "impact_predictor.pkl": "impact_predictor",
                "scaler.pkl": "scaler",
                "metrics.pkl": "model_metrics"
            }
            
            for filename, attr_name in model_files.items():
                filepath = os.path.join(self.model_path, filename)
                if os.path.exists(filepath):
                    with open(filepath, 'rb') as f:
                        model = pickle.load(f)
                        setattr(self, attr_name, model)
            
            if hasattr(self, 'failure_classifier') and self.failure_classifier is not None:
                self.logger.info("Models loaded successfully", path=self.model_path)
            
        except Exception as e:
            self.logger.warning("Failed to load models", error=str(e))
    
    async def health_check(self) -> Dict[str, Any]:
        """Health check for the prediction engine"""
        return {
            "status": "healthy",
            "models_loaded": {
                "failure_classifier": self.failure_classifier is not None,
                "time_regressor": self.time_to_failure_regressor is not None,
                "impact_predictor": self.impact_predictor is not None
            },
            "model_metrics": self.model_metrics,
            "prediction_cache_size": len(self.prediction_cache),
            "timestamp": datetime.utcnow().isoformat()
        }
    
    async def cleanup(self) -> None:
        """Cleanup resources"""
        # Save models before cleanup
        if self.failure_classifier is not None:
            await self._save_models()
        
        self.logger.info("Prediction Engine cleaned up")
