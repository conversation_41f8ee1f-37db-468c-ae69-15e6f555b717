"""
🧠 AURA Intelligence Knowledge Graph Service
Advanced Neo4j-based system knowledge representation with causal reasoning
"""

import asyncio
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import json

import structlog
from neo4j import AsyncGraphDatabase, AsyncDriver
from neo4j.exceptions import ServiceUnavailable, TransientError
import networkx as nx
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity

from src.core.config import get_settings
from src.models.knowledge_graph import (
    SystemNode, IncidentNode, ServiceNode, DependencyEdge,
    CausalEdge, TemporalEdge, KnowledgeGraphQuery, SimilarityResult
)


class KnowledgeGraphService:
    """
    Advanced Knowledge Graph Service for AURA Intelligence
    
    Features:
    - Hyper-relational schema with temporal edges
    - Causal reasoning and root cause analysis
    - Incident similarity search with embeddings
    - Real-time graph updates and pattern detection
    """
    
    def __init__(self, neo4j_uri: str, neo4j_user: str, neo4j_password: str):
        self.logger = structlog.get_logger("aura.knowledge_graph")
        self.neo4j_uri = neo4j_uri
        self.neo4j_user = neo4j_user
        self.neo4j_password = neo4j_password
        self.driver: Optional[AsyncDriver] = None
        self.settings = get_settings()
        
        # Graph analytics
        self.nx_graph = nx.DiGraph()
        self.embeddings_cache: Dict[str, np.ndarray] = {}
        
    async def initialize(self) -> None:
        """Initialize Neo4j connection and create schema"""
        try:
            self.driver = AsyncGraphDatabase.driver(
                self.neo4j_uri,
                auth=(self.neo4j_user, self.neo4j_password),
                max_connection_lifetime=3600,
                max_connection_pool_size=50,
                connection_acquisition_timeout=60
            )
            
            # Verify connection
            await self.driver.verify_connectivity()
            self.logger.info("✅ Connected to Neo4j", uri=self.neo4j_uri)
            
            # Create schema
            await self._create_schema()
            await self._create_indexes()
            
            self.logger.info("🚀 Knowledge Graph Service initialized")
            
        except Exception as e:
            self.logger.error("❌ Failed to initialize Knowledge Graph", error=str(e))
            raise
    
    async def _create_schema(self) -> None:
        """Create knowledge graph schema"""
        schema_queries = [
            # Node constraints
            "CREATE CONSTRAINT service_id IF NOT EXISTS FOR (s:Service) REQUIRE s.id IS UNIQUE",
            "CREATE CONSTRAINT incident_id IF NOT EXISTS FOR (i:Incident) REQUIRE i.id IS UNIQUE",
            "CREATE CONSTRAINT system_id IF NOT EXISTS FOR (sys:System) REQUIRE sys.id IS UNIQUE",
            "CREATE CONSTRAINT metric_id IF NOT EXISTS FOR (m:Metric) REQUIRE m.id IS UNIQUE",
            
            # Indexes for performance
            "CREATE INDEX service_name IF NOT EXISTS FOR (s:Service) ON (s.name)",
            "CREATE INDEX incident_timestamp IF NOT EXISTS FOR (i:Incident) ON (i.timestamp)",
            "CREATE INDEX incident_severity IF NOT EXISTS FOR (i:Incident) ON (i.severity)",
            "CREATE INDEX metric_timestamp IF NOT EXISTS FOR (m:Metric) ON (m.timestamp)",
        ]
        
        async with self.driver.session() as session:
            for query in schema_queries:
                try:
                    await session.run(query)
                    self.logger.debug("Schema query executed", query=query)
                except Exception as e:
                    self.logger.warning("Schema query failed", query=query, error=str(e))
    
    async def _create_indexes(self) -> None:
        """Create additional indexes for performance"""
        index_queries = [
            # Full-text search indexes
            "CALL db.index.fulltext.createNodeIndex('serviceSearch', ['Service'], ['name', 'description'])",
            "CALL db.index.fulltext.createNodeIndex('incidentSearch', ['Incident'], ['title', 'description', 'tags'])",
            
            # Vector indexes for similarity search (Neo4j 5.x)
            "CALL db.index.vector.createNodeIndex('incidentEmbeddings', 'Incident', 'embedding', 384, 'cosine')",
        ]
        
        async with self.driver.session() as session:
            for query in index_queries:
                try:
                    await session.run(query)
                    self.logger.debug("Index created", query=query)
                except Exception as e:
                    self.logger.warning("Index creation failed", query=query, error=str(e))
    
    async def add_service(self, service: ServiceNode) -> str:
        """Add or update a service node"""
        query = """
        MERGE (s:Service {id: $id})
        SET s.name = $name,
            s.type = $type,
            s.version = $version,
            s.status = $status,
            s.metadata = $metadata,
            s.updated_at = datetime()
        RETURN s.id as id
        """
        
        async with self.driver.session() as session:
            result = await session.run(query, {
                "id": service.id,
                "name": service.name,
                "type": service.type,
                "version": service.version,
                "status": service.status,
                "metadata": json.dumps(service.metadata)
            })
            
            record = await result.single()
            self.logger.info("Service added/updated", service_id=record["id"])
            return record["id"]
    
    async def add_incident(self, incident: IncidentNode) -> str:
        """Add incident with embedding for similarity search"""
        # Generate embedding for incident
        embedding = await self._generate_incident_embedding(incident)
        
        query = """
        CREATE (i:Incident {
            id: $id,
            title: $title,
            description: $description,
            severity: $severity,
            status: $status,
            timestamp: datetime($timestamp),
            resolved_at: CASE WHEN $resolved_at IS NOT NULL THEN datetime($resolved_at) ELSE NULL END,
            tags: $tags,
            metadata: $metadata,
            embedding: $embedding
        })
        RETURN i.id as id
        """
        
        async with self.driver.session() as session:
            result = await session.run(query, {
                "id": incident.id,
                "title": incident.title,
                "description": incident.description,
                "severity": incident.severity,
                "status": incident.status,
                "timestamp": incident.timestamp.isoformat(),
                "resolved_at": incident.resolved_at.isoformat() if incident.resolved_at else None,
                "tags": incident.tags,
                "metadata": json.dumps(incident.metadata),
                "embedding": embedding.tolist()
            })
            
            record = await result.single()
            self.logger.info("Incident added", incident_id=record["id"])
            return record["id"]
    
    async def add_causal_relationship(self, cause_id: str, effect_id: str, 
                                   confidence: float, evidence: Dict[str, Any]) -> None:
        """Add causal relationship between incidents/services"""
        query = """
        MATCH (cause), (effect)
        WHERE cause.id = $cause_id AND effect.id = $effect_id
        CREATE (cause)-[r:CAUSES {
            confidence: $confidence,
            evidence: $evidence,
            created_at: datetime()
        }]->(effect)
        """
        
        async with self.driver.session() as session:
            await session.run(query, {
                "cause_id": cause_id,
                "effect_id": effect_id,
                "confidence": confidence,
                "evidence": json.dumps(evidence)
            })
            
            self.logger.info("Causal relationship added", 
                           cause=cause_id, effect=effect_id, confidence=confidence)
    
    async def find_similar_incidents(self, incident_id: str, limit: int = 5) -> List[SimilarityResult]:
        """Find similar incidents using vector similarity"""
        # Get incident embedding
        query_embedding = """
        MATCH (i:Incident {id: $incident_id})
        RETURN i.embedding as embedding, i.title as title
        """
        
        async with self.driver.session() as session:
            result = await session.run(query_embedding, {"incident_id": incident_id})
            record = await result.single()
            
            if not record:
                return []
            
            target_embedding = np.array(record["embedding"])
            
            # Find similar incidents using vector index
            similarity_query = """
            CALL db.index.vector.queryNodes('incidentEmbeddings', $limit, $embedding)
            YIELD node, score
            WHERE node.id <> $incident_id
            RETURN node.id as id, node.title as title, node.description as description,
                   node.severity as severity, node.timestamp as timestamp, score
            ORDER BY score DESC
            """
            
            result = await session.run(similarity_query, {
                "embedding": target_embedding.tolist(),
                "limit": limit,
                "incident_id": incident_id
            })
            
            similar_incidents = []
            async for record in result:
                similar_incidents.append(SimilarityResult(
                    id=record["id"],
                    title=record["title"],
                    description=record["description"],
                    severity=record["severity"],
                    timestamp=record["timestamp"],
                    similarity_score=record["score"]
                ))
            
            return similar_incidents
    
    async def trace_root_cause(self, incident_id: str, max_depth: int = 5) -> List[Dict[str, Any]]:
        """Trace root cause using causal relationships"""
        query = """
        MATCH path = (root)-[:CAUSES*1..$max_depth]->(incident:Incident {id: $incident_id})
        WHERE NOT (()-[:CAUSES]->(root))
        RETURN path, 
               [rel in relationships(path) | rel.confidence] as confidences,
               length(path) as depth
        ORDER BY reduce(conf = 1.0, c in confidences | conf * c) DESC
        LIMIT 10
        """
        
        async with self.driver.session() as session:
            result = await session.run(query, {
                "incident_id": incident_id,
                "max_depth": max_depth
            })
            
            causal_chains = []
            async for record in result:
                path = record["path"]
                confidences = record["confidences"]
                depth = record["depth"]
                
                # Extract path information
                nodes = [dict(node) for node in path.nodes]
                relationships = [dict(rel) for rel in path.relationships]
                
                causal_chains.append({
                    "nodes": nodes,
                    "relationships": relationships,
                    "confidences": confidences,
                    "depth": depth,
                    "overall_confidence": np.prod(confidences) if confidences else 0.0
                })
            
            return causal_chains
    
    async def _generate_incident_embedding(self, incident: IncidentNode) -> np.ndarray:
        """Generate embedding for incident (placeholder - integrate with actual embedding model)"""
        # This would integrate with your embedding model (e.g., sentence-transformers)
        # For now, using a simple hash-based approach
        text = f"{incident.title} {incident.description} {' '.join(incident.tags)}"
        
        # Placeholder: replace with actual embedding generation
        # from sentence_transformers import SentenceTransformer
        # model = SentenceTransformer('all-MiniLM-L6-v2')
        # embedding = model.encode(text)
        
        # Simple hash-based embedding for demo
        import hashlib
        hash_obj = hashlib.md5(text.encode())
        hash_int = int(hash_obj.hexdigest(), 16)
        
        # Generate 384-dimensional embedding
        np.random.seed(hash_int % (2**32))
        embedding = np.random.normal(0, 1, 384)
        embedding = embedding / np.linalg.norm(embedding)  # Normalize
        
        return embedding
    
    async def health_check(self) -> Dict[str, Any]:
        """Health check for the service"""
        try:
            async with self.driver.session() as session:
                result = await session.run("RETURN 1 as health")
                await result.single()
                
                # Get basic stats
                stats_query = """
                MATCH (n) 
                RETURN labels(n)[0] as label, count(n) as count
                """
                result = await session.run(stats_query)
                stats = {record["label"]: record["count"] async for record in result}
                
                return {
                    "status": "healthy",
                    "node_counts": stats,
                    "timestamp": datetime.utcnow().isoformat()
                }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
    
    async def cleanup(self) -> None:
        """Cleanup resources"""
        if self.driver:
            await self.driver.close()
            self.logger.info("Neo4j driver closed")
