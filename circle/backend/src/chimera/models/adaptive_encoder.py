"""
Adaptive Encoder with Neural Architecture Search

Implements a self-modifying encoder inspired by:
- Darwin Gödel Machine concepts for self-improvement
- DANCE (Dynamic Architectures with Neural Continuous Evolution)
- Self-programming AI principles
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import numpy as np
from collections import OrderedDict, deque
import copy

from prometheus_client import Counter, Histogram, Gauge
from opentelemetry import trace

from .topological_features import TopologicalFeatures
from ..monitoring.metrics import ChimeraMetrics
from ..monitoring.tracing import ChimeraTracer


@dataclass
class ArchitectureConfig:
    """Configuration for a specific architecture."""
    layer_sizes: List[int]
    activation_functions: List[str]
    dropout_rates: List[float]
    skip_connections: List[Tuple[int, int]]
    performance_score: float = 0.0
    complexity_score: float = 0.0


class AdaptiveLayer(nn.Module):
    """
    A single adaptive layer that can modify its structure.
    """
    
    def __init__(
        self,
        input_dim: int,
        output_dim: int,
        activation: str = 'relu',
        dropout_rate: float = 0.2
    ):
        super().__init__()
        
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.activation_name = activation
        self.dropout_rate = dropout_rate
        
        # Core transformation
        self.linear = nn.Linear(input_dim, output_dim)
        self.batch_norm = nn.BatchNorm1d(output_dim)
        self.dropout = nn.Dropout(dropout_rate)
        
        # Activation function
        self.activation = self._get_activation(activation)
        
        # Learnable gating for architecture search
        self.gate = nn.Parameter(torch.ones(1))
        
    def _get_activation(self, name: str) -> nn.Module:
        """Get activation function by name."""
        activations = {
            'relu': nn.ReLU(),
            'gelu': nn.GELU(),
            'silu': nn.SiLU(),
            'leaky_relu': nn.LeakyReLU(0.1),
            'elu': nn.ELU(),
            'tanh': nn.Tanh()
        }
        return activations.get(name, nn.ReLU())
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass with gated output."""
        out = self.linear(x)
        out = self.batch_norm(out)
        out = self.activation(out)
        out = self.dropout(out)
        
        # Apply learnable gate
        return out * torch.sigmoid(self.gate)
    
    def mutate(self, mutation_rate: float = 0.1) -> 'AdaptiveLayer':
        """Create a mutated version of this layer."""
        new_layer = copy.deepcopy(self)
        
        if np.random.random() < mutation_rate:
            # Mutate activation function
            activations = ['relu', 'gelu', 'silu', 'leaky_relu', 'elu']
            new_activation = np.random.choice(activations)
            new_layer.activation = self._get_activation(new_activation)
            new_layer.activation_name = new_activation
        
        if np.random.random() < mutation_rate:
            # Mutate dropout rate
            new_layer.dropout_rate = np.clip(
                self.dropout_rate + np.random.normal(0, 0.1),
                0.0, 0.5
            )
            new_layer.dropout = nn.Dropout(new_layer.dropout_rate)
        
        return new_layer


class AdaptiveEncoder(nn.Module):
    """
    Self-modifying encoder with neural architecture search capabilities.
    
    This encoder can:
    1. Modify its architecture based on performance metrics
    2. Explore different activation functions and layer configurations
    3. Implement skip connections dynamically
    4. Adapt to the complexity of input data
    """
    
    def __init__(
        self,
        input_dim: int,
        latent_dim: int = 32,
        initial_hidden_dims: List[int] = [256, 128, 64],
        search_budget: int = 100,
        device: Optional[torch.device] = None
    ):
        super().__init__()
        
        self.input_dim = input_dim
        self.latent_dim = latent_dim
        self.device = device or torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Architecture search parameters
        self.search_budget = search_budget
        self.search_space = {
            'layer_sizes': [32, 64, 128, 256, 512, 1024],
            'activation_functions': ['relu', 'gelu', 'silu', 'leaky_relu', 'elu'],
            'dropout_rates': [0.0, 0.1, 0.2, 0.3, 0.4],
            'max_layers': 6,
            'min_layers': 2
        }
        
        # Initialize metrics
        self.metrics = ChimeraMetrics()
        self.tracer = ChimeraTracer().get_tracer(__name__)
        
        # Metrics for monitoring
        self._adaptation_counter = Counter(
            'chimera_encoder_adaptations_total',
            'Total number of architecture adaptations'
        )
        self._architecture_score = Gauge(
            'chimera_encoder_architecture_score',
            'Current architecture performance score'
        )
        self._complexity_gauge = Gauge(
            'chimera_encoder_complexity',
            'Current architecture complexity'
        )
        
        # Initialize base architecture
        self._init_base_architecture(initial_hidden_dims)
        
        # Architecture history for evolution
        self.architecture_history = deque(maxlen=50)
        self.performance_history = deque(maxlen=100)
        
        # Skip connection registry
        self.skip_connections = nn.ModuleDict()
        self.skip_gates = nn.ParameterDict()
        
    def _init_base_architecture(self, hidden_dims: List[int]):
        """Initialize the base encoder architecture."""
        self.adaptive_layers = nn.ModuleList()
        
        # Build initial layers
        dims = [self.input_dim] + hidden_dims + [self.latent_dim]
        
        for i in range(len(dims) - 1):
            layer = AdaptiveLayer(
                input_dim=dims[i],
                output_dim=dims[i + 1],
                activation='relu' if i < len(dims) - 2 else 'tanh',
                dropout_rate=0.2 if i < len(dims) - 2 else 0.0
            )
            self.adaptive_layers.append(layer)
        
        # Output projection
        self.output_projection = nn.Linear(self.latent_dim, self.latent_dim)
        
    @trace.get_tracer(__name__).start_as_current_span("adaptive_encode")
    def forward(
        self,
        x: torch.Tensor,
        features: Optional[TopologicalFeatures] = None
    ) -> Tuple[torch.Tensor, Dict[str, Any]]:
        """
        Forward pass through adaptive encoder.
        
        Args:
            x: Input tensor
            features: Optional topological features for conditioning
            
        Returns:
            Encoded representation and metadata
        """
        with self.metrics.timer('adaptive_encoding'):
            batch_size = x.shape[0]
            
            # Store intermediate activations for skip connections
            activations = [x]
            
            # Forward through adaptive layers
            current = x
            for i, layer in enumerate(self.adaptive_layers):
                # Apply layer
                current = layer(current)
                activations.append(current)
                
                # Apply skip connections if they exist
                for skip_name, skip_layer in self.skip_connections.items():
                    src_idx, dst_idx = map(int, skip_name.split('_to_'))
                    if dst_idx == i + 1 and src_idx < len(activations):
                        skip_out = skip_layer(activations[src_idx])
                        gate = torch.sigmoid(self.skip_gates[skip_name])
                        current = current + gate * skip_out
            
            # Final projection
            encoded = self.output_projection(current)
            
            # Condition on topological features if provided
            if features is not None:
                topo_embedding = features.topological_embeddings
                if topo_embedding.shape[0] == batch_size:
                    # Combine with topological information
                    encoded = encoded + 0.1 * self._project_topological(topo_embedding)
            
            # Compute architecture metadata
            metadata = {
                'num_layers': len(self.adaptive_layers),
                'skip_connections': len(self.skip_connections),
                'architecture_score': self._compute_architecture_score(),
                'complexity': self._compute_complexity()
            }
            
            self._architecture_score.set(metadata['architecture_score'])
            self._complexity_gauge.set(metadata['complexity'])
            
            return encoded, metadata
    
    def _project_topological(self, topo_embedding: torch.Tensor) -> torch.Tensor:
        """Project topological embeddings to latent dimension."""
        if not hasattr(self, 'topo_projection'):
            self.topo_projection = nn.Linear(
                topo_embedding.shape[-1],
                self.latent_dim
            ).to(self.device)
        
        return self.topo_projection(topo_embedding)
    
    def adapt_architecture(
        self,
        performance_metrics: Dict[str, float],
        data_complexity: Optional[float] = None
    ):
        """
        Modify architecture based on performance metrics.
        Implements self-programming concepts from 2025 research.
        """
        self._adaptation_counter.inc()
        
        with self.tracer.start_as_current_span("architecture_adaptation"):
            # Record performance
            self.performance_history.append(performance_metrics)
            
            # Analyze current performance
            current_score = self._compute_performance_score(performance_metrics)
            
            # Decide on adaptation strategy
            if current_score < 0.5:  # Poor performance
                self._explore_new_architecture()
            elif current_score < 0.8:  # Moderate performance
                self._local_architecture_search()
            else:  # Good performance
                self._fine_tune_architecture()
            
            # Adapt to data complexity if provided
            if data_complexity is not None:
                self._adapt_to_complexity(data_complexity)
    
    def _compute_performance_score(self, metrics: Dict[str, float]) -> float:
        """Compute overall performance score from metrics."""
        # Weighted average of different metrics
        score = 0.0
        weights = {
            'accuracy': 0.4,
            'loss': -0.3,  # Negative weight for loss
            'latency': -0.2,
            'memory': -0.1
        }
        
        for metric, weight in weights.items():
            if metric in metrics:
                value = metrics[metric]
                if metric == 'loss':
                    value = 1.0 / (1.0 + value)  # Convert loss to score
                score += weight * abs(weight) * value
        
        return np.clip(score, 0.0, 1.0)
    
    def _explore_new_architecture(self):
        """Explore radically different architectures."""
        # Generate new architecture configuration
        num_layers = np.random.randint(
            self.search_space['min_layers'],
            self.search_space['max_layers'] + 1
        )
        
        layer_sizes = []
        for i in range(num_layers):
            if i == 0:
                size = np.random.choice([256, 512, 1024])
            else:
                # Ensure decreasing sizes
                max_size = min(layer_sizes[-1], 512)
                size = np.random.choice([s for s in self.search_space['layer_sizes'] if s <= max_size])
            layer_sizes.append(size)
        
        # Create new architecture
        self._rebuild_architecture(layer_sizes)
        
        # Add random skip connections
        if num_layers > 2:
            num_skips = np.random.randint(0, min(3, num_layers - 1))
            for _ in range(num_skips):
                self._add_random_skip_connection()
    
    def _local_architecture_search(self):
        """Perform local search around current architecture."""
        # Mutate existing layers
        for i, layer in enumerate(self.adaptive_layers):
            if np.random.random() < 0.3:  # 30% chance to mutate each layer
                self.adaptive_layers[i] = layer.mutate(mutation_rate=0.2)
        
        # Possibly add or remove a layer
        if np.random.random() < 0.2:
            if len(self.adaptive_layers) < self.search_space['max_layers']:
                self._insert_layer()
            elif len(self.adaptive_layers) > self.search_space['min_layers']:
                self._remove_layer()
    
    def _fine_tune_architecture(self):
        """Fine-tune the current architecture."""
        # Adjust dropout rates based on overfitting indicators
        if len(self.performance_history) > 10:
            recent_metrics = list(self.performance_history)[-10:]
            
            # Check for overfitting
            train_losses = [m.get('train_loss', 0) for m in recent_metrics]
            val_losses = [m.get('val_loss', 0) for m in recent_metrics]
            
            if np.mean(val_losses) > 1.2 * np.mean(train_losses):
                # Increase dropout
                for layer in self.adaptive_layers:
                    layer.dropout_rate = min(0.5, layer.dropout_rate + 0.05)
                    layer.dropout = nn.Dropout(layer.dropout_rate)
    
    def _adapt_to_complexity(self, complexity: float):
        """Adapt architecture based on data complexity."""
        # High complexity data needs deeper networks
        target_depth = int(2 + complexity * 4)  # 2-6 layers based on complexity
        current_depth = len(self.adaptive_layers)
        
        if target_depth > current_depth and current_depth < self.search_space['max_layers']:
            self._insert_layer()
        elif target_depth < current_depth and current_depth > self.search_space['min_layers']:
            self._remove_layer()
    
    def _rebuild_architecture(self, layer_sizes: List[int]):
        """Rebuild the entire architecture with new configuration."""
        self.adaptive_layers = nn.ModuleList()
        
        dims = [self.input_dim] + layer_sizes + [self.latent_dim]
        
        for i in range(len(dims) - 1):
            activation = np.random.choice(self.search_space['activation_functions'])
            dropout = np.random.choice(self.search_space['dropout_rates'])
            
            layer = AdaptiveLayer(
                input_dim=dims[i],
                output_dim=dims[i + 1],
                activation=activation,
                dropout_rate=dropout
            )
            self.adaptive_layers.append(layer)
    
    def _insert_layer(self):
        """Insert a new layer into the architecture."""
        if len(self.adaptive_layers) >= self.search_space['max_layers']:
            return
        
        # Choose insertion point
        insert_idx = np.random.randint(1, len(self.adaptive_layers))
        
        # Determine dimensions
        prev_out = self.adaptive_layers[insert_idx - 1].output_dim
        next_in = self.adaptive_layers[insert_idx].input_dim
        new_dim = int((prev_out + next_in) / 2)
        
        # Create new layer
        new_layer = AdaptiveLayer(
            input_dim=prev_out,
            output_dim=new_dim,
            activation=np.random.choice(self.search_space['activation_functions']),
            dropout_rate=np.random.choice(self.search_space['dropout_rates'])
        )
        
        # Update next layer input dimension
        self.adaptive_layers[insert_idx].linear = nn.Linear(new_dim, next_in)
        
        # Insert layer
        self.adaptive_layers.insert(insert_idx, new_layer)
    
    def _remove_layer(self):
        """Remove a layer from the architecture."""
        if len(self.adaptive_layers) <= self.search_space['min_layers']:
            return
        
        # Choose layer to remove (not first or last)
        remove_idx = np.random.randint(1, len(self.adaptive_layers) - 1)
        
        # Update connections
        prev_out = self.adaptive_layers[remove_idx - 1].output_dim
        next_out = self.adaptive_layers[remove_idx + 1].output_dim
        
        # Update next layer to connect to previous
        self.adaptive_layers[remove_idx + 1].linear = nn.Linear(prev_out, next_out)
        
        # Remove layer
        del self.adaptive_layers[remove_idx]
    
    def _add_random_skip_connection(self):
        """Add a random skip connection."""
        if len(self.adaptive_layers) < 3:
            return
        
        # Choose source and destination
        src_idx = np.random.randint(0, len(self.adaptive_layers) - 2)
        dst_idx = np.random.randint(src_idx + 2, len(self.adaptive_layers))
        
        skip_name = f"{src_idx}_to_{dst_idx}"
        
        if skip_name not in self.skip_connections:
            # Create skip connection
            src_dim = self.adaptive_layers[src_idx].output_dim if src_idx > 0 else self.input_dim
            dst_dim = self.adaptive_layers[dst_idx - 1].output_dim
            
            self.skip_connections[skip_name] = nn.Linear(src_dim, dst_dim)
            self.skip_gates[skip_name] = nn.Parameter(torch.zeros(1))
    
    def _compute_architecture_score(self) -> float:
        """Compute current architecture performance score."""
        if not self.performance_history:
            return 0.5
        
        # Average recent performance
        recent = list(self.performance_history)[-10:]
        scores = [self._compute_performance_score(m) for m in recent]
        
        return np.mean(scores) if scores else 0.5
    
    def _compute_complexity(self) -> float:
        """Compute architecture complexity."""
        # Count parameters
        param_count = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        # Normalize to 0-1 range (assuming max 10M parameters)
        complexity = min(param_count / 10_000_000, 1.0)
        
        # Factor in depth and skip connections
        depth_factor = len(self.adaptive_layers) / self.search_space['max_layers']
        skip_factor = len(self.skip_connections) / max(len(self.adaptive_layers), 1)
        
        return 0.6 * complexity + 0.3 * depth_factor + 0.1 * skip_factor
    
    def get_architecture_config(self) -> ArchitectureConfig:
        """Get current architecture configuration."""
        layer_sizes = [layer.output_dim for layer in self.adaptive_layers[:-1]]
        activations = [layer.activation_name for layer in self.adaptive_layers]
        dropouts = [layer.dropout_rate for layer in self.adaptive_layers]
        
        skip_list = []
        for skip_name in self.skip_connections:
            src, dst = map(int, skip_name.split('_to_'))
            skip_list.append((src, dst))
        
        return ArchitectureConfig(
            layer_sizes=layer_sizes,
            activation_functions=activations,
            dropout_rates=dropouts,
            skip_connections=skip_list,
            performance_score=self._compute_architecture_score(),
            complexity_score=self._compute_complexity()
        )
    
    def load_architecture_config(self, config: ArchitectureConfig):
        """Load a specific architecture configuration."""
        # Rebuild layers
        self._rebuild_architecture(config.layer_sizes)
        
        # Set activations and dropouts
        for i, (activation, dropout) in enumerate(zip(
            config.activation_functions,
            config.dropout_rates
        )):
            if i < len(self.adaptive_layers):
                self.adaptive_layers[i].activation = self.adaptive_layers[i]._get_activation(activation)
                self.adaptive_layers[i].activation_name = activation
                self.adaptive_layers[i].dropout_rate = dropout
                self.adaptive_layers[i].dropout = nn.Dropout(dropout)
        
        # Recreate skip connections
        self.skip_connections.clear()
        self.skip_gates.clear()
        
        for src, dst in config.skip_connections:
            skip_name = f"{src}_to_{dst}"
            src_dim = self.adaptive_layers[src].output_dim if src > 0 else self.input_dim
            dst_dim = self.adaptive_layers[dst - 1].output_dim
            
            self.skip_connections[skip_name] = nn.Linear(src_dim, dst_dim)
            self.skip_gates[skip_name] = nn.Parameter(torch.zeros(1))