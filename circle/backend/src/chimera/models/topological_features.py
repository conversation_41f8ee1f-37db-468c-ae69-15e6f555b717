"""
Topological Feature Extractor for AdaptiveTVAE

Implements state-of-the-art topological data analysis techniques including:
- Persistent homology computation
- Topological autoencoders for feature extraction
- Multi-scale topological features
"""

import numpy as np
import torch
import torch.nn as nn
from typing import List, Tuple, Optional, Dict, Any
from dataclasses import dataclass
from collections import deque

import gudhi
from gudhi.representations import PersistenceImage, PersistenceLandscape
from ripser import ripser
import persim

from prometheus_client import Counter, Histogram, Gauge
from opentelemetry import trace
from opentelemetry.trace import Tracer

from ..utils.persistence import PersistenceDiagram
from ..monitoring.metrics import ChimeraMetrics
from ..monitoring.tracing import ChimeraTracer


@dataclass
class TopologicalFeatures:
    """Container for extracted topological features."""
    persistence_diagrams: List[PersistenceDiagram]
    persistence_images: torch.Tensor
    persistence_landscapes: torch.Tensor
    topological_embeddings: torch.Tensor
    metadata: Dict[str, Any]


class TopologicalFeatureExtractor(nn.Module):
    """
    Extracts topological features from system state using persistent homology.
    
    This implementation incorporates 2025 advances in topological deep learning:
    - Multi-scale persistent homology computation
    - Learnable topological representations
    - Adaptive feature extraction based on data characteristics
    """
    
    def __init__(
        self,
        persistence_dim: List[int] = [0, 1, 2],
        filtration_type: str = "rips",
        max_edge_length: float = 10.0,
        image_resolution: Tuple[int, int] = (50, 50),
        num_landscapes: int = 5,
        embedding_dim: int = 128,
        device: Optional[torch.device] = None
    ):
        super().__init__()
        
        self.persistence_dim = persistence_dim
        self.filtration_type = filtration_type
        self.max_edge_length = max_edge_length
        self.image_resolution = image_resolution
        self.num_landscapes = num_landscapes
        self.embedding_dim = embedding_dim
        self.device = device or torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Initialize metrics
        self.metrics = ChimeraMetrics()
        self.tracer = ChimeraTracer().get_tracer(__name__)
        
        # Metrics for monitoring
        self._extraction_counter = Counter(
            'chimera_topological_extractions_total',
            'Total number of topological feature extractions'
        )
        self._extraction_duration = Histogram(
            'chimera_topological_extraction_duration_seconds',
            'Duration of topological feature extraction'
        )
        self._feature_quality = Gauge(
            'chimera_topological_feature_quality',
            'Quality score of extracted topological features'
        )
        
        # Learnable components for advanced feature extraction
        self._init_learnable_components()
        
    def _init_learnable_components(self):
        """Initialize learnable components for topological feature processing."""
        # Adaptive persistence image parameters
        self.persistence_image_params = nn.ParameterDict({
            'bandwidth': nn.Parameter(torch.tensor(0.1)),
            'weight_function': nn.Parameter(torch.ones(2))  # For adaptive weighting
        })
        
        # Topological autoencoder for feature compression
        self.topo_encoder = nn.Sequential(
            nn.Linear(np.prod(self.image_resolution) * len(self.persistence_dim), 512),
            nn.ReLU(),
            nn.BatchNorm1d(512),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.BatchNorm1d(256),
            nn.Linear(256, self.embedding_dim)
        )
        
        # Multi-scale aggregation network
        self.scale_aggregator = nn.ModuleList([
            nn.Linear(self.embedding_dim, self.embedding_dim)
            for _ in range(3)  # For different scales
        ])
        
        # Attention mechanism for feature importance
        self.feature_attention = nn.MultiheadAttention(
            embed_dim=self.embedding_dim,
            num_heads=4,
            batch_first=True
        )
        
    @trace.get_tracer(__name__).start_as_current_span("extract_features")
    def forward(self, state_vector: torch.Tensor) -> TopologicalFeatures:
        """
        Extract topological features from state vector.
        
        Args:
            state_vector: Input state tensor of shape (batch_size, num_points, dim)
            
        Returns:
            TopologicalFeatures containing various topological representations
        """
        with self.metrics.timer('topological_extraction'):
            self._extraction_counter.inc()
            
            batch_size = state_vector.shape[0]
            all_diagrams = []
            all_images = []
            all_landscapes = []
            
            # Process each sample in batch
            for i in range(batch_size):
                data = state_vector[i].detach().cpu().numpy()
                
                # Compute persistence diagrams
                diagrams = self._compute_persistence_diagrams(data)
                all_diagrams.extend(diagrams)
                
                # Convert to persistence images
                images = self._diagrams_to_images(diagrams)
                all_images.append(images)
                
                # Convert to persistence landscapes
                landscapes = self._diagrams_to_landscapes(diagrams)
                all_landscapes.append(landscapes)
            
            # Stack batch results
            persistence_images = torch.stack(all_images).to(self.device)
            persistence_landscapes = torch.stack(all_landscapes).to(self.device)
            
            # Generate topological embeddings using learned encoder
            embeddings = self._generate_embeddings(persistence_images, persistence_landscapes)
            
            # Calculate feature quality
            quality = self._assess_feature_quality(all_diagrams)
            self._feature_quality.set(quality)
            
            return TopologicalFeatures(
                persistence_diagrams=all_diagrams,
                persistence_images=persistence_images,
                persistence_landscapes=persistence_landscapes,
                topological_embeddings=embeddings,
                metadata={
                    'quality_score': quality,
                    'num_features': len(all_diagrams),
                    'extraction_params': {
                        'persistence_dim': self.persistence_dim,
                        'filtration_type': self.filtration_type,
                        'max_edge_length': self.max_edge_length
                    }
                }
            )
    
    def _compute_persistence_diagrams(self, data: np.ndarray) -> List[PersistenceDiagram]:
        """Compute persistence diagrams using multiple filtrations."""
        diagrams = []
        
        if self.filtration_type == "rips":
            # Compute Rips persistence
            result = ripser(
                data,
                maxdim=max(self.persistence_dim),
                thresh=self.max_edge_length
            )
            
            for dim in self.persistence_dim:
                if dim < len(result['dgms']):
                    dgm = result['dgms'][dim]
                    # Separate finite and infinite features
                    finite_mask = np.isfinite(dgm[:, 1])
                    finite_pairs = dgm[finite_mask].tolist()
                    infinite_pairs = dgm[~finite_mask, 0].tolist()
                    
                    diagrams.append(PersistenceDiagram(
                        dimension=dim,
                        pairs=finite_pairs,
                        infinity_pairs=infinite_pairs
                    ))
        
        elif self.filtration_type == "alpha":
            # Use GUDHI for alpha complex
            alpha_complex = gudhi.AlphaComplex(points=data)
            simplex_tree = alpha_complex.create_simplex_tree(
                max_alpha_square=self.max_edge_length**2
            )
            simplex_tree.compute_persistence()
            
            for dim in self.persistence_dim:
                persistence_pairs = simplex_tree.persistence_intervals_in_dimension(dim)
                finite_mask = np.isfinite(persistence_pairs[:, 1])
                
                diagrams.append(PersistenceDiagram(
                    dimension=dim,
                    pairs=persistence_pairs[finite_mask].tolist(),
                    infinity_pairs=persistence_pairs[~finite_mask, 0].tolist()
                ))
        
        return diagrams
    
    def _diagrams_to_images(self, diagrams: List[PersistenceDiagram]) -> torch.Tensor:
        """Convert persistence diagrams to images with learned parameters."""
        images = []
        
        # Use adaptive bandwidth
        bandwidth = torch.sigmoid(self.persistence_image_params['bandwidth']).item()
        
        for dgm in diagrams:
            # Convert to persistence image
            img = dgm.to_persistence_image(
                resolution=self.image_resolution,
                sigma=bandwidth
            )
            
            # Apply learned weighting
            weight = torch.sigmoid(self.persistence_image_params['weight_function'])
            weighted_img = img * weight.detach().cpu().numpy()[0]
            
            images.append(weighted_img)
        
        # Stack and convert to tensor
        return torch.tensor(np.stack(images), dtype=torch.float32)
    
    def _diagrams_to_landscapes(self, diagrams: List[PersistenceDiagram]) -> torch.Tensor:
        """Convert persistence diagrams to landscapes."""
        landscapes = []
        
        for dgm in diagrams:
            landscape = dgm.to_persistence_landscape(
                num_landscapes=self.num_landscapes,
                resolution=100
            )
            landscapes.append(landscape)
        
        return torch.tensor(np.stack(landscapes), dtype=torch.float32)
    
    def _generate_embeddings(
        self,
        images: torch.Tensor,
        landscapes: torch.Tensor
    ) -> torch.Tensor:
        """Generate topological embeddings using learned encoder."""
        batch_size = images.shape[0]
        
        # Flatten persistence images
        flattened_images = images.view(batch_size, -1)
        
        # Encode through topological autoencoder
        base_embeddings = self.topo_encoder(flattened_images)
        
        # Multi-scale processing
        scale_features = []
        for i, scale_net in enumerate(self.scale_aggregator):
            # Apply different scales through network
            scaled = scale_net(base_embeddings)
            scale_features.append(scaled.unsqueeze(1))
        
        # Concatenate scale features
        multi_scale = torch.cat(scale_features, dim=1)  # (batch, num_scales, embed_dim)
        
        # Apply attention to aggregate scales
        attended, _ = self.feature_attention(multi_scale, multi_scale, multi_scale)
        
        # Final embedding is mean of attended features
        final_embeddings = attended.mean(dim=1)
        
        return final_embeddings
    
    def _assess_feature_quality(self, diagrams: List[PersistenceDiagram]) -> float:
        """Assess quality of extracted topological features."""
        if not diagrams:
            return 0.0
        
        quality_scores = []
        
        for dgm in diagrams:
            stats = dgm.summary_statistics()
            
            # Quality based on persistence entropy and feature count
            if stats['num_features'] > 0:
                entropy_score = min(stats['entropy'] / 2.0, 1.0)  # Normalize
                feature_score = min(stats['num_features'] / 50.0, 1.0)  # Normalize
                persistence_score = min(stats['mean_persistence'] / 0.5, 1.0)  # Normalize
                
                quality = (entropy_score + feature_score + persistence_score) / 3.0
                quality_scores.append(quality)
        
        return np.mean(quality_scores) if quality_scores else 0.0
    
    def compute_topological_distance(
        self,
        features1: TopologicalFeatures,
        features2: TopologicalFeatures
    ) -> float:
        """Compute distance between two sets of topological features."""
        distances = []
        
        # Wasserstein distances between persistence diagrams
        for dgm1, dgm2 in zip(features1.persistence_diagrams, features2.persistence_diagrams):
            if dgm1.dimension == dgm2.dimension:
                dist = dgm1.wasserstein_distance(dgm2)
                distances.append(dist)
        
        # Embedding distance
        embed_dist = torch.norm(
            features1.topological_embeddings - features2.topological_embeddings,
            p=2
        ).item()
        distances.append(embed_dist)
        
        return np.mean(distances)
    
    def adapt_to_data(self, state_batch: torch.Tensor):
        """
        Adapt extractor parameters based on data characteristics.
        This implements the self-modifying aspect inspired by 2025 research.
        """
        with torch.no_grad():
            # Extract features
            features = self.forward(state_batch)
            
            # Analyze feature quality
            quality = features.metadata['quality_score']
            
            # Adapt bandwidth based on quality
            if quality < 0.3:
                # Increase bandwidth for smoother features
                self.persistence_image_params['bandwidth'].data += 0.01
            elif quality > 0.7:
                # Decrease bandwidth for finer features
                self.persistence_image_params['bandwidth'].data -= 0.01
            
            # Clamp to reasonable range
            self.persistence_image_params['bandwidth'].data.clamp_(0.01, 1.0)