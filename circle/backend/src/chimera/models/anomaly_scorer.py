"""
Anomaly Scorer for AdaptiveTVAE

Implements advanced anomaly scoring with:
- Multi-modal anomaly detection
- Topological anomaly scoring
- Reconstruction-based scoring
- Latent space density estimation
- Ensemble scoring methods
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional, Any
import numpy as np
from scipy import stats
from sklearn.mixture import GaussianMixture
from sklearn.neighbors import LocalOutlierFactor
from sklearn.ensemble import IsolationForest

from prometheus_client import Counter, Histogram, Gauge
from opentelemetry import trace

from .topological_features import TopologicalFeatures
from ..monitoring.metrics import ChimeraMetrics
from ..monitoring.tracing import ChimeraTracer


class AnomalyScorer(nn.Module):
    """
    Multi-modal anomaly scoring system.
    
    Combines multiple anomaly detection approaches:
    1. Reconstruction error scoring
    2. Topological anomaly detection
    3. Latent space density estimation
    4. Ensemble methods for robustness
    """
    
    def __init__(
        self,
        input_dim: int,
        latent_dim: int,
        topological_dim: int = 128,
        ensemble_size: int = 5,
        device: torch.device = torch.device("cpu")
    ):
        super().__init__()
        
        self.input_dim = input_dim
        self.latent_dim = latent_dim
        self.topological_dim = topological_dim
        self.ensemble_size = ensemble_size
        self.device = device
        
        # Initialize metrics
        self.metrics = ChimeraMetrics()
        self.tracer = ChimeraTracer()
        
        # Reconstruction error network
        self.reconstruction_scorer = nn.Sequential(
            nn.Linear(input_dim * 2, 256),
            nn.ReLU(),
            nn.BatchNorm1d(256),
            nn.Dropout(0.2),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.BatchNorm1d(128),
            nn.Linear(128, 1),
            nn.Sigmoid()
        )
        
        # Topological anomaly scorer
        self.topological_scorer = nn.Sequential(
            nn.Linear(topological_dim, 128),
            nn.ReLU(),
            nn.BatchNorm1d(128),
            nn.Dropout(0.2),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.BatchNorm1d(64),
            nn.Linear(64, 1),
            nn.Sigmoid()
        )
        
        # Latent space anomaly scorer
        self.latent_scorer = nn.Sequential(
            nn.Linear(latent_dim * 2, 128),
            nn.ReLU(),
            nn.BatchNorm1d(128),
            nn.Dropout(0.2),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.BatchNorm1d(64),
            nn.Linear(64, 1),
            nn.Sigmoid()
        )
        
        # Ensemble combiner
        self.ensemble_combiner = nn.Sequential(
            nn.Linear(3 + ensemble_size, 32),
            nn.ReLU(),
            nn.BatchNorm1d(32),
            nn.Linear(32, 16),
            nn.ReLU(),
            nn.Linear(16, 1),
            nn.Sigmoid()
        )
        
        # Statistical models for density estimation
        self.gmm = None
        self.lof = LocalOutlierFactor(novelty=True, n_neighbors=20)
        self.isolation_forest = IsolationForest(contamination=0.1, random_state=42)
        
        # Calibration parameters
        self.calibration_alpha = nn.Parameter(torch.tensor(1.0))
        self.calibration_beta = nn.Parameter(torch.tensor(0.0))
        
        # History for adaptive thresholding
        self.score_history = []
        self.max_history_size = 10000
        
        self.to(device)
        
    def forward(
        self,
        x: torch.Tensor,
        x_recon: torch.Tensor,
        z_mean: torch.Tensor,
        z_log_var: torch.Tensor,
        topological_features: Optional[TopologicalFeatures] = None
    ) -> Dict[str, torch.Tensor]:
        """
        Compute anomaly scores using multiple methods.
        
        Args:
            x: Original input
            x_recon: Reconstructed input
            z_mean: Latent mean
            z_log_var: Latent log variance
            topological_features: Optional topological features
            
        Returns:
            Dictionary containing various anomaly scores
        """
        with self.tracer.start_span("anomaly_scoring") as span:
            batch_size = x.shape[0]
            
            # 1. Reconstruction-based scoring
            recon_features = torch.cat([x, x_recon], dim=1)
            recon_score = self.reconstruction_scorer(recon_features)
            
            # 2. Topological anomaly scoring
            if topological_features is not None:
                topo_score = self.topological_scorer(topological_features.combined_features)
            else:
                topo_score = torch.zeros(batch_size, 1, device=self.device)
            
            # 3. Latent space anomaly scoring
            z_sample = self._sample_latent(z_mean, z_log_var)
            latent_features = torch.cat([z_mean, z_sample], dim=1)
            latent_score = self.latent_scorer(latent_features)
            
            # 4. Statistical anomaly scores
            stat_scores = self._compute_statistical_scores(
                x.detach().cpu().numpy(),
                z_mean.detach().cpu().numpy()
            )
            stat_scores_tensor = torch.tensor(
                stat_scores, 
                device=self.device, 
                dtype=torch.float32
            ).unsqueeze(1)
            
            # 5. Ensemble scoring
            ensemble_scores = self._compute_ensemble_scores(
                x, x_recon, z_mean, z_log_var
            )
            
            # Combine all scores
            all_scores = torch.cat([
                recon_score,
                topo_score,
                latent_score,
                stat_scores_tensor,
                ensemble_scores
            ], dim=1)
            
            # Final combined score
            combined_score = self.ensemble_combiner(all_scores)
            
            # Apply calibration
            calibrated_score = self._calibrate_scores(combined_score)
            
            # Update metrics
            self.metrics.anomaly_score_histogram.observe(
                calibrated_score.mean().item()
            )
            
            # Store scores for adaptive thresholding
            self._update_score_history(calibrated_score)
            
            return {
                "anomaly_score": calibrated_score,
                "reconstruction_score": recon_score,
                "topological_score": topo_score,
                "latent_score": latent_score,
                "statistical_score": stat_scores_tensor,
                "ensemble_scores": ensemble_scores,
                "raw_combined_score": combined_score
            }
    
    def _sample_latent(
        self, 
        z_mean: torch.Tensor, 
        z_log_var: torch.Tensor
    ) -> torch.Tensor:
        """Sample from latent distribution."""
        std = torch.exp(0.5 * z_log_var)
        eps = torch.randn_like(std)
        return z_mean + eps * std
    
    def _compute_statistical_scores(
        self,
        x_numpy: np.ndarray,
        z_numpy: np.ndarray
    ) -> np.ndarray:
        """Compute statistical anomaly scores."""
        scores = []
        
        # GMM-based scoring
        if self.gmm is not None:
            try:
                gmm_scores = -self.gmm.score_samples(z_numpy)
                gmm_scores = (gmm_scores - gmm_scores.min()) / (gmm_scores.max() - gmm_scores.min() + 1e-8)
                scores.append(gmm_scores)
            except:
                scores.append(np.zeros(len(z_numpy)))
        else:
            scores.append(np.zeros(len(z_numpy)))
        
        # LOF scoring
        try:
            if hasattr(self.lof, 'decision_function'):
                lof_scores = -self.lof.decision_function(z_numpy)
                lof_scores = (lof_scores - lof_scores.min()) / (lof_scores.max() - lof_scores.min() + 1e-8)
                scores.append(lof_scores)
            else:
                scores.append(np.zeros(len(z_numpy)))
        except:
            scores.append(np.zeros(len(z_numpy)))
        
        # Isolation Forest scoring
        try:
            if hasattr(self.isolation_forest, 'decision_function'):
                if_scores = -self.isolation_forest.decision_function(z_numpy)
                if_scores = (if_scores - if_scores.min()) / (if_scores.max() - if_scores.min() + 1e-8)
                scores.append(if_scores)
            else:
                scores.append(np.zeros(len(z_numpy)))
        except:
            scores.append(np.zeros(len(z_numpy)))
        
        # Average statistical scores
        return np.mean(scores, axis=0)
    
    def _compute_ensemble_scores(
        self,
        x: torch.Tensor,
        x_recon: torch.Tensor,
        z_mean: torch.Tensor,
        z_log_var: torch.Tensor
    ) -> torch.Tensor:
        """Compute ensemble of anomaly scores using different methods."""
        batch_size = x.shape[0]
        ensemble_scores = []
        
        # 1. MSE-based score
        mse_score = F.mse_loss(x_recon, x, reduction='none').mean(dim=1, keepdim=True)
        mse_score = torch.sigmoid(mse_score)
        ensemble_scores.append(mse_score)
        
        # 2. MAE-based score
        mae_score = F.l1_loss(x_recon, x, reduction='none').mean(dim=1, keepdim=True)
        mae_score = torch.sigmoid(mae_score)
        ensemble_scores.append(mae_score)
        
        # 3. SSIM-based score (simplified)
        ssim_score = 1.0 - self._compute_ssim(x, x_recon)
        ensemble_scores.append(ssim_score)
        
        # 4. KL divergence score
        kl_score = -0.5 * torch.sum(
            1 + z_log_var - z_mean.pow(2) - z_log_var.exp(),
            dim=1, keepdim=True
        )
        kl_score = torch.sigmoid(kl_score / 10.0)  # Normalize
        ensemble_scores.append(kl_score)
        
        # 5. Gradient-based score
        grad_score = self._compute_gradient_score(x, x_recon)
        ensemble_scores.append(grad_score)
        
        # Pad to ensemble size if needed
        while len(ensemble_scores) < self.ensemble_size:
            ensemble_scores.append(torch.zeros(batch_size, 1, device=self.device))
        
        return torch.cat(ensemble_scores[:self.ensemble_size], dim=1)
    
    def _compute_ssim(
        self, 
        x: torch.Tensor, 
        x_recon: torch.Tensor
    ) -> torch.Tensor:
        """Simplified SSIM computation."""
        c1 = 0.01 ** 2
        c2 = 0.03 ** 2
        
        mu_x = x.mean(dim=1, keepdim=True)
        mu_y = x_recon.mean(dim=1, keepdim=True)
        
        sigma_x = x.var(dim=1, keepdim=True)
        sigma_y = x_recon.var(dim=1, keepdim=True)
        sigma_xy = ((x - mu_x) * (x_recon - mu_y)).mean(dim=1, keepdim=True)
        
        ssim = ((2 * mu_x * mu_y + c1) * (2 * sigma_xy + c2)) / \
               ((mu_x ** 2 + mu_y ** 2 + c1) * (sigma_x + sigma_y + c2))
        
        return ssim.clamp(0, 1)
    
    def _compute_gradient_score(
        self, 
        x: torch.Tensor, 
        x_recon: torch.Tensor
    ) -> torch.Tensor:
        """Compute gradient-based anomaly score."""
        # Compute gradients
        grad_x = torch.gradient(x, dim=-1)[0]
        grad_recon = torch.gradient(x_recon, dim=-1)[0]
        
        # Gradient difference
        grad_diff = F.mse_loss(grad_recon, grad_x, reduction='none').mean(dim=1, keepdim=True)
        
        return torch.sigmoid(grad_diff)
    
    def _calibrate_scores(self, scores: torch.Tensor) -> torch.Tensor:
        """Apply learned calibration to scores."""
        return torch.sigmoid(
            self.calibration_alpha * scores + self.calibration_beta
        )
    
    def _update_score_history(self, scores: torch.Tensor):
        """Update score history for adaptive thresholding."""
        self.score_history.extend(scores.detach().cpu().numpy().flatten().tolist())
        
        # Maintain maximum history size
        if len(self.score_history) > self.max_history_size:
            self.score_history = self.score_history[-self.max_history_size:]
    
    def fit_statistical_models(self, latent_vectors: np.ndarray):
        """Fit statistical models on normal data."""
        with self.tracer.start_span("fit_statistical_models"):
            # Fit GMM
            self.gmm = GaussianMixture(
                n_components=min(10, len(latent_vectors) // 100),
                covariance_type='full',
                random_state=42
            )
            self.gmm.fit(latent_vectors)
            
            # Fit LOF
            self.lof.fit(latent_vectors)
            
            # Fit Isolation Forest
            self.isolation_forest.fit(latent_vectors)
            
            self.metrics.model_fitted_counter.labels(model_type="statistical").inc()
    
    def get_adaptive_threshold(self, percentile: float = 95.0) -> float:
        """Get adaptive threshold based on score history."""
        if len(self.score_history) < 100:
            return 0.5  # Default threshold
        
        return np.percentile(self.score_history, percentile)
    
    def reset_calibration(self):
        """Reset calibration parameters."""
        self.calibration_alpha.data.fill_(1.0)
        self.calibration_beta.data.fill_(0.0)
        self.score_history = []