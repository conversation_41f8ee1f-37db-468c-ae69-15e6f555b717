"""
Variational Bottleneck with Adaptive Capacity Control

Implements an advanced VAE bottleneck with:
- Adaptive information capacity control
- Learnable KL divergence weighting
- Multi-scale latent representations
- Disentanglement mechanisms
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, Dict, Optional, Any
import numpy as np
from collections import deque

from prometheus_client import Counter, Histogram, Gauge
from opentelemetry import trace

from ..monitoring.metrics import ChimeraMetrics
from ..monitoring.tracing import ChimeraTracer


class VariationalBottleneck(nn.Module):
    """
    VAE bottleneck with adaptive capacity control.
    
    This implementation includes:
    1. Dynamic β-VAE weighting for KL divergence
    2. Learnable information capacity constraints
    3. Hierarchical latent representations
    4. Disentanglement promoting mechanisms
    """
    
    def __init__(
        self,
        hidden_dim: int,
        latent_dim: int,
        initial_beta: float = 1.0,
        initial_capacity: float = 5.0,
        min_capacity: float = 0.0,
        max_capacity: float = 25.0,
        num_latent_scales: int = 3,
        use_flow: bool = True,
        device: Optional[torch.device] = None
    ):
        super().__init__()
        
        self.hidden_dim = hidden_dim
        self.latent_dim = latent_dim
        self.num_latent_scales = num_latent_scales
        self.use_flow = use_flow
        self.device = device or torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Adaptive capacity control parameters
        self.beta = nn.Parameter(torch.tensor(initial_beta))
        self.capacity = nn.Parameter(torch.tensor(initial_capacity))
        self.min_capacity = min_capacity
        self.max_capacity = max_capacity
        
        # Multi-scale latent projections
        self.fc_mu_scales = nn.ModuleList([
            nn.Linear(hidden_dim, latent_dim // num_latent_scales)
            for _ in range(num_latent_scales)
        ])
        
        self.fc_var_scales = nn.ModuleList([
            nn.Linear(hidden_dim, latent_dim // num_latent_scales)
            for _ in range(num_latent_scales)
        ])
        
        # Normalizing flow for more expressive posterior
        if use_flow:
            self.flow_layers = nn.ModuleList([
                PlanarFlow(latent_dim) for _ in range(4)
            ])
        
        # Disentanglement promoting layers
        self.disentangle_net = nn.Sequential(
            nn.Linear(latent_dim, latent_dim * 2),
            nn.ReLU(),
            nn.Linear(latent_dim * 2, latent_dim),
            nn.Tanh()
        )
        
        # Capacity annealing schedule
        self.capacity_history = deque(maxlen=1000)
        self.kl_history = deque(maxlen=1000)
        
        # Initialize metrics
        self.metrics = ChimeraMetrics()
        self.tracer = ChimeraTracer().get_tracer(__name__)
        
        # Monitoring metrics
        self._sampling_counter = Counter(
            'chimera_vae_sampling_total',
            'Total number of VAE sampling operations'
        )
        self._kl_divergence = Histogram(
            'chimera_vae_kl_divergence',
            'KL divergence values'
        )
        self._capacity_gauge = Gauge(
            'chimera_vae_capacity',
            'Current VAE capacity parameter'
        )
        self._beta_gauge = Gauge(
            'chimera_vae_beta',
            'Current VAE beta parameter'
        )
        
    @trace.get_tracer(__name__).start_as_current_span("variational_forward")
    def forward(
        self,
        x: torch.Tensor,
        deterministic: bool = False
    ) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, Dict[str, Any]]:
        """
        Forward pass through variational bottleneck.
        
        Args:
            x: Input hidden representation
            deterministic: If True, use mean instead of sampling
            
        Returns:
            z: Latent representation
            mu: Mean of latent distribution
            log_var: Log variance of latent distribution
            kl_loss: KL divergence loss
            metadata: Additional information
        """
        with self.metrics.timer('variational_bottleneck'):
            self._sampling_counter.inc()
            
            batch_size = x.shape[0]
            
            # Multi-scale latent encoding
            mu_parts = []
            log_var_parts = []
            
            for i in range(self.num_latent_scales):
                mu_part = self.fc_mu_scales[i](x)
                log_var_part = self.fc_var_scales[i](x)
                
                mu_parts.append(mu_part)
                log_var_parts.append(log_var_part)
            
            # Concatenate multi-scale representations
            mu = torch.cat(mu_parts, dim=-1)
            log_var = torch.cat(log_var_parts, dim=-1)
            
            # Reparameterization trick
            if deterministic:
                z = mu
            else:
                std = torch.exp(0.5 * log_var)
                eps = torch.randn_like(std)
                z = mu + eps * std
            
            # Apply normalizing flow if enabled
            if self.use_flow and not deterministic:
                z, flow_log_det = self._apply_flow(z, mu, log_var)
            else:
                flow_log_det = torch.zeros(batch_size, device=self.device)
            
            # Apply disentanglement transformation
            z_disentangled = z + 0.1 * self.disentangle_net(z)
            
            # Compute KL divergence with adaptive capacity
            kl_loss, kl_components = self._compute_adaptive_kl(
                mu, log_var, flow_log_det
            )
            
            # Update monitoring metrics
            self._kl_divergence.observe(kl_loss.mean().item())
            self._capacity_gauge.set(self.capacity.item())
            self._beta_gauge.set(self.beta.item())
            
            # Prepare metadata
            metadata = {
                'kl_components': kl_components,
                'capacity': self.capacity.item(),
                'beta': self.beta.item(),
                'flow_log_det': flow_log_det.mean().item() if self.use_flow else 0.0,
                'latent_stats': {
                    'mu_mean': mu.mean().item(),
                    'mu_std': mu.std().item(),
                    'log_var_mean': log_var.mean().item(),
                    'z_mean': z_disentangled.mean().item(),
                    'z_std': z_disentangled.std().item()
                }
            }
            
            return z_disentangled, mu, log_var, kl_loss, metadata
    
    def _apply_flow(
        self,
        z: torch.Tensor,
        mu: torch.Tensor,
        log_var: torch.Tensor
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """Apply normalizing flow transformations."""
        log_det_sum = torch.zeros(z.shape[0], device=z.device)
        
        for flow in self.flow_layers:
            z, log_det = flow(z)
            log_det_sum += log_det
        
        return z, log_det_sum
    
    def _compute_adaptive_kl(
        self,
        mu: torch.Tensor,
        log_var: torch.Tensor,
        flow_log_det: torch.Tensor
    ) -> Tuple[torch.Tensor, Dict[str, float]]:
        """
        Compute KL divergence with adaptive capacity control.
        
        Implements progressive capacity increase and beta scheduling.
        """
        # Standard KL divergence
        kl_standard = -0.5 * torch.sum(
            1 + log_var - mu.pow(2) - log_var.exp(),
            dim=-1
        )
        
        # Add flow contribution if present
        kl_total = kl_standard - flow_log_det
        
        # Compute per-dimension KL for analysis
        kl_per_dim = kl_total / mu.shape[-1]
        
        # Apply capacity constraint (progressive KL annealing)
        clamped_capacity = torch.clamp(self.capacity, self.min_capacity, self.max_capacity)
        kl_constrained = torch.abs(kl_total - clamped_capacity)
        
        # Apply beta weighting
        kl_loss = self.beta * kl_constrained
        
        # Record for adaptive scheduling
        self.capacity_history.append(clamped_capacity.item())
        self.kl_history.append(kl_total.mean().item())
        
        # Compute disentanglement metric (mutual information approximation)
        # Higher values indicate better disentanglement
        mi_estimate = self._estimate_mutual_information(mu, log_var)
        
        components = {
            'kl_standard': kl_standard.mean().item(),
            'kl_total': kl_total.mean().item(),
            'kl_per_dim': kl_per_dim.mean().item(),
            'capacity': clamped_capacity.item(),
            'mi_estimate': mi_estimate
        }
        
        return kl_loss.mean(), components
    
    def _estimate_mutual_information(
        self,
        mu: torch.Tensor,
        log_var: torch.Tensor
    ) -> float:
        """
        Estimate mutual information between latent dimensions.
        Lower values indicate better disentanglement.
        """
        # Compute correlation matrix of means
        mu_centered = mu - mu.mean(dim=0, keepdim=True)
        cov_matrix = torch.mm(mu_centered.t(), mu_centered) / (mu.shape[0] - 1)
        
        # Normalize by diagonal elements
        diag = torch.sqrt(torch.diag(cov_matrix))
        corr_matrix = cov_matrix / (diag.unsqueeze(0) * diag.unsqueeze(1) + 1e-8)
        
        # Off-diagonal elements represent entanglement
        off_diagonal = corr_matrix - torch.eye(mu.shape[1], device=mu.device)
        mi_estimate = torch.abs(off_diagonal).mean().item()
        
        return mi_estimate
    
    def adapt_capacity(self, performance_metrics: Dict[str, float]):
        """
        Adapt capacity and beta parameters based on performance.
        
        This implements the self-modifying aspect of the VAE.
        """
        if len(self.kl_history) < 10:
            return
        
        # Analyze recent KL divergence trends
        recent_kl = list(self.kl_history)[-10:]
        kl_mean = np.mean(recent_kl)
        kl_std = np.std(recent_kl)
        
        # Get reconstruction quality indicator
        recon_quality = performance_metrics.get('reconstruction_loss', 1.0)
        
        with torch.no_grad():
            # Adapt capacity based on KL-reconstruction trade-off
            if recon_quality > 0.5 and kl_mean < self.capacity.item():
                # Poor reconstruction, increase capacity
                self.capacity.data += 0.5
            elif recon_quality < 0.1 and kl_mean > self.capacity.item() * 1.5:
                # Good reconstruction but high KL, decrease capacity
                self.capacity.data -= 0.5
            
            # Clamp capacity
            self.capacity.data = torch.clamp(
                self.capacity,
                self.min_capacity,
                self.max_capacity
            )
            
            # Adapt beta based on KL stability
            if kl_std > 2.0:
                # High variance, increase beta for stability
                self.beta.data *= 1.1
            elif kl_std < 0.5 and self.beta.item() > 0.5:
                # Low variance, can reduce beta
                self.beta.data *= 0.9
            
            # Clamp beta
            self.beta.data = torch.clamp(self.beta, 0.1, 10.0)
    
    def get_latent_statistics(self) -> Dict[str, Any]:
        """Get statistics about the latent space."""
        return {
            'capacity': self.capacity.item(),
            'beta': self.beta.item(),
            'capacity_history': list(self.capacity_history)[-100:],
            'kl_history': list(self.kl_history)[-100:],
            'num_flow_layers': len(self.flow_layers) if self.use_flow else 0,
            'latent_dim': self.latent_dim,
            'num_scales': self.num_latent_scales
        }


class PlanarFlow(nn.Module):
    """
    Planar normalizing flow transformation.
    
    Implements: z' = z + u * tanh(w^T z + b)
    """
    
    def __init__(self, dim: int):
        super().__init__()
        
        self.weight = nn.Parameter(torch.randn(dim))
        self.scale = nn.Parameter(torch.randn(dim))
        self.bias = nn.Parameter(torch.randn(1))
        
        # Initialize to near-identity
        self.weight.data.normal_(0, 0.01)
        self.scale.data.normal_(0, 0.01)
        self.bias.data.zero_()
    
    def forward(self, z: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Apply planar flow transformation.
        
        Returns:
            z_new: Transformed latent
            log_det: Log determinant of Jacobian
        """
        # Ensure invertibility constraint
        w_dot_u = torch.sum(self.weight * self.scale)
        u_hat = self.scale + (torch.log1p(torch.exp(w_dot_u)) - 1 - w_dot_u) * \
                self.weight / torch.sum(self.weight ** 2)
        
        # Apply transformation
        activation = torch.tanh(F.linear(z, self.weight.unsqueeze(0), self.bias))
        z_new = z + u_hat.unsqueeze(0) * activation.unsqueeze(-1)
        
        # Compute log determinant
        psi = (1 - activation ** 2).unsqueeze(-1) * self.weight.unsqueeze(0)
        log_det = torch.log(torch.abs(1 + torch.sum(psi * u_hat.unsqueeze(0), dim=-1)) + 1e-8)
        
        return z_new, log_det