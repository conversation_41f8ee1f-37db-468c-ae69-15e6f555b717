"""
Adaptive Topological Variational Autoencoder (AdaptiveTVAE)

Main model integrating:
- Topological feature extraction
- Self-modifying neural architecture
- Adaptive variational bottleneck
- Continuous learning capabilities
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional, Any
import numpy as np
from pathlib import Path
import json

from prometheus_client import Counter, Histogram, Gauge
from opentelemetry import trace

from .topological_features import TopologicalFeatureExtractor, TopologicalFeatures
from .adaptive_encoder import AdaptiveEncoder, ArchitectureConfig
from .variational_bottleneck import VariationalBottleneck
from .anomaly_scorer import AnomalyScorer
from ..monitoring.metrics import ChimeraMetrics
from ..monitoring.tracing import ChimeraTracer, trace_model_inference
from ..utils.config import ChimeraConfig


class AdaptiveDecoder(nn.Module):
    """
    Adaptive decoder that mirrors the encoder architecture.
    """
    
    def __init__(
        self,
        latent_dim: int,
        output_dim: int,
        hidden_dims: List[int] = [64, 128, 256],
        device: Optional[torch.device] = None
    ):
        super().__init__()
        
        self.latent_dim = latent_dim
        self.output_dim = output_dim
        self.device = device or torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Build decoder layers (reverse of encoder)
        self.decoder_layers = nn.ModuleList()
        
        dims = [latent_dim] + hidden_dims + [output_dim]
        
        for i in range(len(dims) - 1):
            layer = nn.Sequential(
                nn.Linear(dims[i], dims[i + 1]),
                nn.BatchNorm1d(dims[i + 1]) if i < len(dims) - 2 else nn.Identity(),
                nn.ReLU() if i < len(dims) - 2 else nn.Identity()
            )
            self.decoder_layers.append(layer)
    
    def forward(self, z: torch.Tensor) -> torch.Tensor:
        """Decode latent representation back to original space."""
        current = z
        
        for layer in self.decoder_layers:
            current = layer(current)
        
        return current


class AdaptiveTVAE(nn.Module):
    """
    Adaptive Topological Variational Autoencoder.
    
    This model combines:
    1. Topological feature extraction for understanding data structure
    2. Self-modifying encoder architecture that adapts to data complexity
    3. Adaptive variational bottleneck with capacity control
    4. Anomaly detection capabilities
    5. Continuous learning and adaptation
    """
    
    def __init__(self, config: ChimeraConfig):
        super().__init__()
        
        self.config = config
        self.device = torch.device(config.device)
        
        # Initialize metrics and tracing
        self.metrics = ChimeraMetrics()
        self.tracer = ChimeraTracer().get_tracer(__name__)
        
        # Model version tracking
        self.version = "1.0.0"
        self.generation = 0
        
        # Components
        self.topological_extractor = TopologicalFeatureExtractor(
            persistence_dim=config.topological.persistence_dim,
            filtration_type=config.topological.filtration_type,
            max_edge_length=config.topological.max_edge_length,
            image_resolution=config.topological.image_resolution,
            num_landscapes=config.topological.num_landscapes,
            embedding_dim=config.topological.embedding_dim,
            device=self.device
        ).to(self.device)
        
        # Determine input dimension (will be set dynamically)
        self.input_dim = None
        self.encoder = None
        self.decoder = None
        self.variational_bottleneck = None
        self.anomaly_scorer = None
        
        # Metrics
        self._forward_counter = Counter(
            'chimera_tvae_forward_passes_total',
            'Total number of forward passes'
        )
        self._reconstruction_error = Histogram(
            'chimera_tvae_reconstruction_error',
            'Reconstruction error distribution'
        )
        self._adaptation_counter = Counter(
            'chimera_tvae_adaptations_total',
            'Total number of model adaptations'
        )
        
        # Training state
        self.training_step = 0
        self.best_loss = float('inf')
        self.performance_history = []
    
    def _init_components(self, input_dim: int):
        """Initialize model components once input dimension is known."""
        self.input_dim = input_dim
        
        # Adaptive encoder
        self.encoder = AdaptiveEncoder(
            input_dim=input_dim + self.config.topological.embedding_dim,  # Input + topological features
            latent_dim=self.config.encoder.latent_dim,
            initial_hidden_dims=self.config.encoder.initial_hidden_dims,
            search_budget=self.config.encoder.search_budget,
            device=self.device
        ).to(self.device)
        
        # Variational bottleneck
        self.variational_bottleneck = VariationalBottleneck(
            hidden_dim=self.config.encoder.latent_dim,
            latent_dim=self.config.encoder.latent_dim,
            initial_beta=self.config.variational.initial_beta,
            initial_capacity=self.config.variational.initial_capacity,
            min_capacity=self.config.variational.min_capacity,
            max_capacity=self.config.variational.max_capacity,
            num_latent_scales=self.config.variational.num_latent_scales,
            use_flow=self.config.variational.use_flow,
            device=self.device
        ).to(self.device)
        
        # Decoder
        self.decoder = AdaptiveDecoder(
            latent_dim=self.config.encoder.latent_dim,
            output_dim=input_dim,
            hidden_dims=list(reversed(self.config.encoder.initial_hidden_dims)),
            device=self.device
        ).to(self.device)
        
        # Anomaly scorer
        if not hasattr(self, 'anomaly_scorer') or self.anomaly_scorer is None:
            from .anomaly_scorer import AnomalyScorer
            self.anomaly_scorer = AnomalyScorer(
                latent_dim=self.config.encoder.latent_dim,
                device=self.device
            ).to(self.device)
    
    @trace.get_tracer(__name__).start_as_current_span("forward")
    def forward(
        self,
        x: torch.Tensor,
        compute_anomaly: bool = True
    ) -> Dict[str, torch.Tensor]:
        """
        Forward pass through AdaptiveTVAE.
        
        Args:
            x: Input tensor of shape (batch_size, num_features) or
               (batch_size, num_points, point_dim) for topological features
            compute_anomaly: Whether to compute anomaly scores
            
        Returns:
            Dictionary containing:
                - reconstruction: Reconstructed input
                - z: Latent representation
                - mu: Mean of latent distribution
                - log_var: Log variance of latent distribution
                - kl_loss: KL divergence loss
                - anomaly_score: Anomaly scores (if requested)
                - metadata: Additional information
        """
        self._forward_counter.inc()
        
        with self.metrics.timer('forward_pass'):
            batch_size = x.shape[0]
            
            # Initialize components if needed
            if self.encoder is None:
                if len(x.shape) == 2:
                    self._init_components(x.shape[1])
                else:
                    # Flatten for now if 3D input
                    x_flat = x.view(batch_size, -1)
                    self._init_components(x_flat.shape[1])
            
            # Extract topological features if input is point cloud
            if len(x.shape) == 3:
                topo_features = self.topological_extractor(x)
                topo_embedding = topo_features.topological_embeddings
                
                # Flatten input for encoder
                x = x.view(batch_size, -1)
            else:
                # Create dummy topological features
                topo_features = None
                topo_embedding = torch.zeros(
                    batch_size,
                    self.config.topological.embedding_dim,
                    device=self.device
                )
            
            # Concatenate input with topological features
            encoder_input = torch.cat([x, topo_embedding], dim=-1)
            
            # Encode
            encoded, encoder_metadata = self.encoder(encoder_input, topo_features)
            
            # Variational bottleneck
            z, mu, log_var, kl_loss, vae_metadata = self.variational_bottleneck(
                encoded,
                deterministic=not self.training
            )
            
            # Decode
            reconstruction = self.decoder(z)
            
            # Compute anomaly scores if requested
            anomaly_scores = None
            anomaly_metadata = {}
            if compute_anomaly and self.anomaly_scorer is not None:
                anomaly_scores, anomaly_metadata = self.anomaly_scorer(
                    z, mu, log_var, reconstruction, x
                )
            
            # Record metrics
            recon_error = F.mse_loss(reconstruction, x, reduction='none').mean(dim=1)
            self._reconstruction_error.observe(recon_error.mean().item())
            
            # Compile results
            results = {
                'reconstruction': reconstruction,
                'z': z,
                'mu': mu,
                'log_var': log_var,
                'kl_loss': kl_loss,
                'anomaly_score': anomaly_scores,
                'metadata': {
                    'encoder': encoder_metadata,
                    'vae': vae_metadata,
                    'anomaly': anomaly_metadata,
                    'topological': topo_features.metadata if topo_features else {},
                    'reconstruction_error': recon_error.mean().item()
                }
            }
            
            # Trace inference
            trace_model_inference(
                model_name=f"adaptive_tvae_v{self.version}",
                batch_size=batch_size
            )
            
            return results
    
    def loss_function(
        self,
        x: torch.Tensor,
        reconstruction: torch.Tensor,
        mu: torch.Tensor,
        log_var: torch.Tensor,
        kl_loss: torch.Tensor,
        beta: Optional[float] = None
    ) -> Tuple[torch.Tensor, Dict[str, float]]:
        """
        Compute VAE loss.
        
        Returns:
            Total loss and dictionary of loss components
        """
        # Reconstruction loss
        recon_loss = F.mse_loss(reconstruction, x, reduction='mean')
        
        # Use adaptive beta if not provided
        if beta is None:
            beta = self.variational_bottleneck.beta.item()
        
        # Total loss
        total_loss = recon_loss + beta * kl_loss
        
        # Loss components for logging
        loss_components = {
            'total': total_loss.item(),
            'reconstruction': recon_loss.item(),
            'kl': kl_loss.item(),
            'beta': beta
        }
        
        return total_loss, loss_components
    
    def adapt(self, performance_metrics: Dict[str, float]):
        """
        Adapt model architecture based on performance.
        
        This implements the self-improvement aspect of the model.
        """
        self._adaptation_counter.inc()
        self.generation += 1
        
        with self.tracer.span(__name__, "model_adaptation"):
            # Adapt encoder architecture
            if self.encoder is not None:
                self.encoder.adapt_architecture(performance_metrics)
            
            # Adapt VAE capacity
            if self.variational_bottleneck is not None:
                self.variational_bottleneck.adapt_capacity(performance_metrics)
            
            # Adapt topological extractor
            if 'data_complexity' in performance_metrics:
                self.topological_extractor.adapt_to_data(
                    torch.randn(32, 100, 3).to(self.device)  # Dummy data for adaptation
                )
            
            # Update anomaly scorer thresholds
            if self.anomaly_scorer is not None:
                self.anomaly_scorer.update_thresholds(performance_metrics)
            
            # Record adaptation
            self.tracer.add_event("architecture.adapted", {
                "generation": self.generation,
                "performance": performance_metrics
            })
            
            # Update version if significant improvement
            if performance_metrics.get('accuracy', 0) > 0.95:
                self._increment_version()
    
    def _increment_version(self):
        """Increment model version."""
        parts = self.version.split('.')
        parts[-1] = str(int(parts[-1]) + 1)
        self.version = '.'.join(parts)
        
        self.metrics.model_info.info({
            'model_name': 'adaptive_tvae',
            'version': self.version,
            'generation': str(self.generation)
        })
    
    def save_checkpoint(self, path: str):
        """Save model checkpoint."""
        checkpoint = {
            'version': self.version,
            'generation': self.generation,
            'config': self.config.to_dict(),
            'model_state': self.state_dict(),
            'encoder_config': self.encoder.get_architecture_config().__dict__ if self.encoder else None,
            'performance_history': self.performance_history[-100:],  # Last 100 entries
            'best_loss': self.best_loss,
            'training_step': self.training_step
        }
        
        torch.save(checkpoint, path)
        
        # Also save architecture visualization
        arch_path = Path(path).parent / f"architecture_gen{self.generation}.json"
        if self.encoder:
            with open(arch_path, 'w') as f:
                json.dump(self.encoder.get_architecture_config().__dict__, f, indent=2)
    
    def load_checkpoint(self, path: str):
        """Load model checkpoint."""
        checkpoint = torch.load(path, map_location=self.device)
        
        self.version = checkpoint['version']
        self.generation = checkpoint['generation']
        self.best_loss = checkpoint.get('best_loss', float('inf'))
        self.training_step = checkpoint.get('training_step', 0)
        self.performance_history = checkpoint.get('performance_history', [])
        
        # Load state dict
        self.load_state_dict(checkpoint['model_state'])
        
        # Restore encoder architecture if available
        if checkpoint.get('encoder_config') and self.encoder:
            config = ArchitectureConfig(**checkpoint['encoder_config'])
            self.encoder.load_architecture_config(config)
    
    def get_model_summary(self) -> Dict[str, Any]:
        """Get summary of current model state."""
        summary = {
            'version': self.version,
            'generation': self.generation,
            'training_step': self.training_step,
            'best_loss': self.best_loss,
            'components': {
                'topological_extractor': True,
                'encoder': self.encoder is not None,
                'variational_bottleneck': self.variational_bottleneck is not None,
                'decoder': self.decoder is not None,
                'anomaly_scorer': self.anomaly_scorer is not None
            }
        }
        
        if self.encoder:
            summary['encoder_architecture'] = self.encoder.get_architecture_config().__dict__
        
        if self.variational_bottleneck:
            summary['vae_stats'] = self.variational_bottleneck.get_latent_statistics()
        
        return summary