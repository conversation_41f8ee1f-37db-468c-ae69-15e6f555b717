"""
Chimera Tracing Module

Provides OpenTelemetry distributed tracing for the AdaptiveTVAE system.
"""

from typing import Optional, Dict, Any, Callable
from contextlib import contextmanager
import functools

from opentelemetry import trace
from opentelemetry.trace import Tracer, Span, Status, StatusCode
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import (
    BatchSpanProcessor,
    ConsoleSpanExporter,
    SpanExporter
)
from opentelemetry.sdk.resources import Resource
from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter
from opentelemetry.instrumentation.requests import RequestsInstrumentor
from opentelemetry.propagate import set_global_textmap


class ChimeraTracer:
    """
    Centralized distributed tracing for Chimera components.
    
    Provides:
    - Span creation and management
    - Context propagation
    - Performance tracking
    - Error tracking
    """
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(
        self,
        service_name: str = "chimera-adaptive-tvae",
        otlp_endpoint: Optional[str] = None,
        console_export: bool = False
    ):
        # Only initialize once
        if ChimeraTracer._initialized:
            return
            
        ChimeraTracer._initialized = True
        
        # Create resource identifying the service
        resource = Resource.create({
            "service.name": service_name,
            "service.version": "1.0.0",
            "deployment.environment": "production"
        })
        
        # Create tracer provider
        self.tracer_provider = TracerProvider(resource=resource)
        trace.set_tracer_provider(self.tracer_provider)
        
        # Add exporters
        if otlp_endpoint:
            # Export to OTLP endpoint (e.g., Jaeger, Tempo)
            otlp_exporter = OTLPSpanExporter(endpoint=otlp_endpoint)
            self.tracer_provider.add_span_processor(
                BatchSpanProcessor(otlp_exporter)
            )
        
        if console_export:
            # Export to console for debugging
            console_exporter = ConsoleSpanExporter()
            self.tracer_provider.add_span_processor(
                BatchSpanProcessor(console_exporter)
            )
        
        # Instrument HTTP requests
        RequestsInstrumentor().instrument()
        
        # Cache tracers by name
        self._tracers: Dict[str, Tracer] = {}
    
    def get_tracer(self, name: str) -> Tracer:
        """Get or create a tracer with the given name."""
        if name not in self._tracers:
            self._tracers[name] = trace.get_tracer(name)
        return self._tracers[name]
    
    @contextmanager
    def span(
        self,
        tracer_name: str,
        span_name: str,
        attributes: Optional[Dict[str, Any]] = None,
        record_exception: bool = True
    ):
        """
        Context manager for creating spans.
        
        Usage:
            with tracer.span('component.name', 'operation.name'):
                # Do operation
        """
        tracer = self.get_tracer(tracer_name)
        
        with tracer.start_as_current_span(span_name) as span:
            # Add attributes
            if attributes:
                for key, value in attributes.items():
                    span.set_attribute(key, value)
            
            try:
                yield span
            except Exception as e:
                if record_exception:
                    span.record_exception(e)
                    span.set_status(Status(StatusCode.ERROR, str(e)))
                raise
    
    def trace_method(
        self,
        tracer_name: str,
        span_name: Optional[str] = None,
        attributes: Optional[Dict[str, Any]] = None
    ):
        """
        Decorator for tracing methods.
        
        Usage:
            @tracer.trace_method('component.name')
            def my_method(self, ...):
                ...
        """
        def decorator(func: Callable) -> Callable:
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                # Use function name as span name if not provided
                actual_span_name = span_name or func.__name__
                
                # Extract self if this is a method
                if args and hasattr(args[0], '__class__'):
                    class_name = args[0].__class__.__name__
                    actual_span_name = f"{class_name}.{actual_span_name}"
                
                with self.span(tracer_name, actual_span_name, attributes):
                    return func(*args, **kwargs)
            
            return wrapper
        return decorator
    
    def add_event(
        self,
        name: str,
        attributes: Optional[Dict[str, Any]] = None
    ):
        """Add an event to the current span."""
        span = trace.get_current_span()
        if span and span.is_recording():
            span.add_event(name, attributes=attributes or {})
    
    def set_attribute(self, key: str, value: Any):
        """Set an attribute on the current span."""
        span = trace.get_current_span()
        if span and span.is_recording():
            span.set_attribute(key, value)
    
    def record_exception(self, exception: Exception):
        """Record an exception in the current span."""
        span = trace.get_current_span()
        if span and span.is_recording():
            span.record_exception(exception)
            span.set_status(Status(StatusCode.ERROR, str(exception)))


# Convenience functions for common tracing patterns
def trace_model_inference(model_name: str, batch_size: int):
    """Trace model inference operations."""
    tracer = ChimeraTracer()
    tracer.set_attribute("model.name", model_name)
    tracer.set_attribute("batch.size", batch_size)
    tracer.add_event("inference.start", {"model": model_name})


def trace_training_epoch(epoch: int, loss: float, accuracy: float):
    """Trace training epoch."""
    tracer = ChimeraTracer()
    tracer.add_event("epoch.complete", {
        "epoch": epoch,
        "loss": loss,
        "accuracy": accuracy
    })


def trace_architecture_change(
    component: str,
    change_type: str,
    details: Optional[Dict[str, Any]] = None
):
    """Trace architecture modifications."""
    tracer = ChimeraTracer()
    event_attrs = {
        "component": component,
        "change_type": change_type
    }
    if details:
        event_attrs.update(details)
    
    tracer.add_event("architecture.modified", event_attrs)


def trace_anomaly_detection(
    anomaly_type: str,
    severity: str,
    details: Optional[Dict[str, Any]] = None
):
    """Trace anomaly detection events."""
    tracer = ChimeraTracer()
    event_attrs = {
        "anomaly.type": anomaly_type,
        "anomaly.severity": severity
    }
    if details:
        event_attrs.update(details)
    
    tracer.add_event("anomaly.detected", event_attrs)