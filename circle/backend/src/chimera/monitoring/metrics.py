"""
Chimera Metrics Module

Provides Prometheus metrics instrumentation for the AdaptiveTVAE system.
"""

import time
from contextlib import contextmanager
from typing import Dict, Optional, Any
from prometheus_client import Counter, Histogram, Gauge, Summary, Info
from prometheus_client import CollectorRegistry, generate_latest


class ChimeraMetrics:
    """
    Centralized metrics collection for Chimera components.
    
    Provides:
    - Request/operation counters
    - Latency histograms
    - Resource utilization gauges
    - Model performance metrics
    """
    
    def __init__(self, registry: Optional[CollectorRegistry] = None):
        self.registry = registry or CollectorRegistry()
        
        # Request metrics
        self.request_counter = Counter(
            'chimera_requests_total',
            'Total number of requests processed',
            ['method', 'endpoint', 'status'],
            registry=self.registry
        )
        
        # Latency metrics
        self.latency_histogram = Histogram(
            'chimera_request_duration_seconds',
            'Request latency in seconds',
            ['method', 'endpoint'],
            registry=self.registry
        )
        
        # Model performance metrics
        self.model_accuracy = Gauge(
            'chimera_model_accuracy',
            'Current model accuracy',
            ['model_name', 'dataset'],
            registry=self.registry
        )
        
        self.model_loss = Gauge(
            'chimera_model_loss',
            'Current model loss',
            ['model_name', 'loss_type'],
            registry=self.registry
        )
        
        self.model_drift = Gauge(
            'chimera_model_drift_score',
            'Model drift detection score',
            ['model_name', 'drift_type'],
            registry=self.registry
        )
        
        # Training metrics
        self.training_epochs = Counter(
            'chimera_training_epochs_total',
            'Total number of training epochs completed',
            ['model_name'],
            registry=self.registry
        )
        
        self.training_duration = Histogram(
            'chimera_training_duration_seconds',
            'Training duration in seconds',
            ['model_name', 'phase'],
            registry=self.registry
        )
        
        # Resource utilization
        self.gpu_utilization = Gauge(
            'chimera_gpu_utilization_percent',
            'GPU utilization percentage',
            ['device_id'],
            registry=self.registry
        )
        
        self.memory_usage = Gauge(
            'chimera_memory_usage_bytes',
            'Memory usage in bytes',
            ['type'],
            registry=self.registry
        )
        
        # Architecture evolution metrics
        self.architecture_changes = Counter(
            'chimera_architecture_changes_total',
            'Total number of architecture modifications',
            ['component', 'change_type'],
            registry=self.registry
        )
        
        self.architecture_complexity = Gauge(
            'chimera_architecture_complexity',
            'Current architecture complexity score',
            ['component'],
            registry=self.registry
        )
        
        # Data quality metrics
        self.data_quality_score = Gauge(
            'chimera_data_quality_score',
            'Data quality assessment score',
            ['dataset', 'quality_type'],
            registry=self.registry
        )
        
        self.anomaly_detection_rate = Gauge(
            'chimera_anomaly_detection_rate',
            'Rate of anomalies detected',
            ['detector', 'anomaly_type'],
            registry=self.registry
        )
        
        # Pipeline metrics
        self.pipeline_stage_duration = Histogram(
            'chimera_pipeline_stage_duration_seconds',
            'Duration of pipeline stages',
            ['pipeline', 'stage'],
            registry=self.registry
        )
        
        self.pipeline_failures = Counter(
            'chimera_pipeline_failures_total',
            'Total pipeline failures',
            ['pipeline', 'stage', 'error_type'],
            registry=self.registry
        )
        
        # Model versioning info
        self.model_info = Info(
            'chimera_model',
            'Information about deployed models',
            ['model_name'],
            registry=self.registry
        )
    
    @contextmanager
    def timer(self, metric_name: str, labels: Optional[Dict[str, str]] = None):
        """
        Context manager for timing operations.
        
        Usage:
            with metrics.timer('operation_name', {'label': 'value'}):
                # Do operation
        """
        start_time = time.time()
        try:
            yield
        finally:
            duration = time.time() - start_time
            
            if metric_name == 'request':
                self.latency_histogram.labels(**labels).observe(duration)
            elif metric_name == 'training':
                self.training_duration.labels(**labels).observe(duration)
            elif metric_name == 'pipeline_stage':
                self.pipeline_stage_duration.labels(**labels).observe(duration)
            else:
                # Generic timer - create a summary metric
                if not hasattr(self, f'{metric_name}_summary'):
                    setattr(
                        self,
                        f'{metric_name}_summary',
                        Summary(
                            f'chimera_{metric_name}_duration_seconds',
                            f'Duration of {metric_name} operations',
                            registry=self.registry
                        )
                    )
                getattr(self, f'{metric_name}_summary').observe(duration)
    
    def record_model_performance(
        self,
        model_name: str,
        accuracy: float,
        loss: float,
        dataset: str = 'unknown',
        loss_type: str = 'total'
    ):
        """Record model performance metrics."""
        self.model_accuracy.labels(
            model_name=model_name,
            dataset=dataset
        ).set(accuracy)
        
        self.model_loss.labels(
            model_name=model_name,
            loss_type=loss_type
        ).set(loss)
    
    def record_drift(
        self,
        model_name: str,
        drift_score: float,
        drift_type: str = 'data'
    ):
        """Record model drift detection results."""
        self.model_drift.labels(
            model_name=model_name,
            drift_type=drift_type
        ).set(drift_score)
    
    def record_architecture_change(
        self,
        component: str,
        change_type: str,
        new_complexity: Optional[float] = None
    ):
        """Record architecture modification."""
        self.architecture_changes.labels(
            component=component,
            change_type=change_type
        ).inc()
        
        if new_complexity is not None:
            self.architecture_complexity.labels(
                component=component
            ).set(new_complexity)
    
    def record_anomaly(
        self,
        detector: str,
        anomaly_type: str,
        detection_rate: float
    ):
        """Record anomaly detection results."""
        self.anomaly_detection_rate.labels(
            detector=detector,
            anomaly_type=anomaly_type
        ).set(detection_rate)
    
    def record_pipeline_failure(
        self,
        pipeline: str,
        stage: str,
        error_type: str
    ):
        """Record pipeline failure."""
        self.pipeline_failures.labels(
            pipeline=pipeline,
            stage=stage,
            error_type=error_type
        ).inc()
    
    def update_resource_usage(
        self,
        gpu_utilization: Optional[Dict[str, float]] = None,
        memory_usage: Optional[Dict[str, float]] = None
    ):
        """Update resource utilization metrics."""
        if gpu_utilization:
            for device_id, utilization in gpu_utilization.items():
                self.gpu_utilization.labels(device_id=device_id).set(utilization)
        
        if memory_usage:
            for mem_type, usage in memory_usage.items():
                self.memory_usage.labels(type=mem_type).set(usage)
    
    def export_metrics(self) -> bytes:
        """Export metrics in Prometheus format."""
        return generate_latest(self.registry)
    
    def get_metric_values(self) -> Dict[str, Any]:
        """Get current values of all metrics."""
        metrics = {}
        
        # Collect samples from all metrics
        for collector in self.registry._collector_to_names:
            for metric in collector.collect():
                for sample in metric.samples:
                    key = f"{sample.name}_{sample.labels}"
                    metrics[key] = sample.value
        
        return metrics