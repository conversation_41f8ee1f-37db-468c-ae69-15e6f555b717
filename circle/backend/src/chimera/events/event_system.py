"""
📨 Event-Driven Architecture for Chimera

Implements event sourcing, CQRS, and schema registry patterns
for scalable, auditable ML operations.
"""

import asyncio
import json
import logging
from abc import ABC, abstractmethod
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any, Type, Callable, Union
from uuid import UUID, uuid4

from pydantic import BaseModel, Field, validator
from confluent_kafka import Producer, Consumer, KafkaError
from confluent_kafka.schema_registry import SchemaRegistryClient
from confluent_kafka.schema_registry.avro import AvroSerializer, AvroDeserializer
from confluent_kafka.serialization import SerializationContext, MessageField

logger = logging.getLogger(__name__)


class EventType(Enum):
    """Types of domain events."""
    # Model lifecycle events
    MODEL_TRAINED = "model.trained"
    MODEL_DEPLOYED = "model.deployed"
    MODEL_UPDATED = "model.updated"
    MODEL_RETIRED = "model.retired"
    
    # Prediction events
    PREDICTION_REQUESTED = "prediction.requested"
    PREDICTION_COMPLETED = "prediction.completed"
    PREDICTION_FAILED = "prediction.failed"
    
    # Shadow mode events
    SHADOW_COMPARISON_COMPLETED = "shadow.comparison.completed"
    SHADOW_DIVERGENCE_DETECTED = "shadow.divergence.detected"
    
    # Canary events
    CANARY_STARTED = "canary.started"
    CANARY_PROGRESSED = "canary.progressed"
    CANARY_COMPLETED = "canary.completed"
    CANARY_ROLLED_BACK = "canary.rolled_back"


class EventMetadata(BaseModel):
    """Metadata for all events."""
    event_id: UUID = Field(default_factory=uuid4)
    event_type: EventType
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    source_service: str
    correlation_id: Optional[UUID] = None
    causation_id: Optional[UUID] = None
    user_id: Optional[str] = None
    trace_id: Optional[str] = None
    
    class Config:
        json_encoders = {
            UUID: str,
            datetime: lambda v: v.isoformat()
        }


class DomainEvent(BaseModel):
    """Base class for domain events."""
    metadata: EventMetadata
    payload: Dict[str, Any]
    
    @validator('payload')
    def validate_payload(cls, v, values):
        """Validate payload based on event type."""
        # Add custom validation logic here
        return v
        
    def to_avro_dict(self) -> Dict[str, Any]:
        """Convert to Avro-compatible dictionary."""
        return json.loads(self.json())


# Specific event types
class ModelDeployedEvent(DomainEvent):
    """Event when a model is deployed."""
    model_id: str
    model_version: str
    deployment_type: str  # "production", "shadow", "canary"
    deployment_config: Dict[str, Any]


class PredictionCompletedEvent(DomainEvent):
    """Event when a prediction is completed."""
    prediction_id: UUID
    model_id: str
    model_version: str
    input_hash: str
    prediction_result: Any
    latency_ms: float
    
    
class ShadowDivergenceEvent(DomainEvent):
    """Event when shadow and production diverge."""
    endpoint: str
    production_result: Any
    shadow_result: Any
    divergence_score: float
    divergence_type: str


class EventStore:
    """Event store for persisting and retrieving events."""
    
    def __init__(self, 
                 bootstrap_servers: str,
                 schema_registry_url: str,
                 topic_prefix: str = "chimera"):
        self.bootstrap_servers = bootstrap_servers
        self.schema_registry_url = schema_registry_url
        self.topic_prefix = topic_prefix
        
        # Initialize Kafka producer
        self.producer = Producer({
            'bootstrap.servers': bootstrap_servers,
            'client.id': 'chimera-event-store',
            'compression.type': 'snappy',
            'batch.size': 16384,
            'linger.ms': 10
        })
        
        # Initialize schema registry
        self.schema_registry = SchemaRegistryClient({
            'url': schema_registry_url
        })
        
        # Event handlers
        self.event_handlers: Dict[EventType, List[Callable]] = {}
        
        # Projections
        self.projections: Dict[str, Any] = {}
        
    async def append_event(self, event: DomainEvent):
        """Append event to event store."""
        topic = f"{self.topic_prefix}.events.{event.metadata.event_type.value}"
        
        # Serialize event
        key = str(event.metadata.event_id)
        value = event.to_avro_dict()
        
        # Produce to Kafka
        self.producer.produce(
            topic=topic,
            key=key,
            value=json.dumps(value),
            on_delivery=self._delivery_callback
        )
        
        # Trigger async processing
        await self._process_event(event)
        
        logger.info(f"Event {event.metadata.event_id} appended to {topic}")
        
    def _delivery_callback(self, err, msg):
        """Callback for Kafka delivery."""
        if err:
            logger.error(f"Event delivery failed: {err}")
        else:
            logger.debug(f"Event delivered to {msg.topic()} [{msg.partition()}]")
            
    async def _process_event(self, event: DomainEvent):
        """Process event through registered handlers."""
        handlers = self.event_handlers.get(event.metadata.event_type, [])
        
        for handler in handlers:
            try:
                if asyncio.iscoroutinefunction(handler):
                    await handler(event)
                else:
                    handler(event)
            except Exception as e:
                logger.error(f"Handler failed for event {event.metadata.event_id}: {e}")
                
    def register_handler(self, event_type: EventType, handler: Callable):
        """Register event handler."""
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        self.event_handlers[event_type].append(handler)
        
    async def get_events(self, 
                        aggregate_id: str,
                        event_types: Optional[List[EventType]] = None,
                        since: Optional[datetime] = None) -> List[DomainEvent]:
        """Retrieve events for an aggregate."""
        # In a real implementation, this would query the event store
        # For now, return empty list
        return []
        
    def get_projection(self, projection_name: str) -> Any:
        """Get current state of a projection."""
        return self.projections.get(projection_name)


class EventBus:
    """Event bus for pub/sub communication."""
    
    def __init__(self, event_store: EventStore):
        self.event_store = event_store
        self.subscribers: Dict[str, List[Callable]] = {}
        
    async def publish(self, event: DomainEvent):
        """Publish event to bus."""
        # Store event
        await self.event_store.append_event(event)
        
        # Notify subscribers
        await self._notify_subscribers(event)
        
    async def _notify_subscribers(self, event: DomainEvent):
        """Notify all subscribers of an event."""
        event_type_str = event.metadata.event_type.value
        
        # Get subscribers for this event type
        subscribers = self.subscribers.get(event_type_str, [])
        
        # Also get wildcard subscribers
        wildcard_subscribers = self.subscribers.get("*", [])
        
        all_subscribers = subscribers + wildcard_subscribers
        
        for subscriber in all_subscribers:
            try:
                if asyncio.iscoroutinefunction(subscriber):
                    await subscriber(event)
                else:
                    subscriber(event)
            except Exception as e:
                logger.error(f"Subscriber failed for event {event.metadata.event_id}: {e}")
                
    def subscribe(self, event_type: Union[EventType, str], handler: Callable):
        """Subscribe to events."""
        if isinstance(event_type, EventType):
            event_type = event_type.value
            
        if event_type not in self.subscribers:
            self.subscribers[event_type] = []
            
        self.subscribers[event_type].append(handler)
        logger.info(f"Subscribed handler to {event_type}")


class Command(BaseModel):
    """Base class for commands."""
    command_id: UUID = Field(default_factory=uuid4)
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    user_id: Optional[str] = None


class DeployModelCommand(Command):
    """Command to deploy a model."""
    model_id: str
    model_version: str
    deployment_type: str
    config: Dict[str, Any]


class CommandHandler(ABC):
    """Base class for command handlers."""
    
    @abstractmethod
    async def handle(self, command: Command) -> Optional[DomainEvent]:
        """Handle command and return resulting event."""
        pass


class DeployModelCommandHandler(CommandHandler):
    """Handler for model deployment commands."""
    
    def __init__(self, model_service, event_bus: EventBus):
        self.model_service = model_service
        self.event_bus = event_bus
        
    async def handle(self, command: DeployModelCommand) -> Optional[DomainEvent]:
        """Deploy model and emit event."""
        # Deploy model
        deployment_result = await self.model_service.deploy(
            model_id=command.model_id,
            version=command.model_version,
            deployment_type=command.deployment_type,
            config=command.config
        )
        
        # Create event
        event = ModelDeployedEvent(
            metadata=EventMetadata(
                event_type=EventType.MODEL_DEPLOYED,
                source_service="model-service",
                user_id=command.user_id,
                causation_id=command.command_id
            ),
            payload=deployment_result,
            model_id=command.model_id,
            model_version=command.model_version,
            deployment_type=command.deployment_type,
            deployment_config=command.config
        )
        
        # Publish event
        await self.event_bus.publish(event)
        
        return event


class QueryHandler:
    """Handler for read queries (CQRS pattern)."""
    
    def __init__(self, read_model_store):
        self.read_model_store = read_model_store
        
    async def get_model_deployment_status(self, model_id: str) -> Dict[str, Any]:
        """Get current deployment status of a model."""
        # Query read model
        return await self.read_model_store.get_deployment_status(model_id)
        
    async def get_deployment_history(self, 
                                   model_id: str,
                                   limit: int = 10) -> List[Dict[str, Any]]:
        """Get deployment history for a model."""
        return await self.read_model_store.get_deployment_history(model_id, limit)


class EventProjector:
    """Projects events to read models."""
    
    def __init__(self, event_bus: EventBus, read_model_store):
        self.event_bus = event_bus
        self.read_model_store = read_model_store
        
        # Subscribe to events
        self._subscribe_to_events()
        
    def _subscribe_to_events(self):
        """Subscribe to relevant events."""
        self.event_bus.subscribe(
            EventType.MODEL_DEPLOYED,
            self._handle_model_deployed
        )
        
        self.event_bus.subscribe(
            EventType.PREDICTION_COMPLETED,
            self._handle_prediction_completed
        )
        
    async def _handle_model_deployed(self, event: ModelDeployedEvent):
        """Update read model for model deployment."""
        await self.read_model_store.update_deployment_status(
            model_id=event.model_id,
            version=event.model_version,
            status="deployed",
            deployment_type=event.deployment_type,
            timestamp=event.metadata.timestamp
        )
        
    async def _handle_prediction_completed(self, event: PredictionCompletedEvent):
        """Update metrics for predictions."""
        await self.read_model_store.increment_prediction_count(
            model_id=event.model_id,
            version=event.model_version
        )
        
        await self.read_model_store.update_latency_metrics(
            model_id=event.model_id,
            latency_ms=event.latency_ms
        )


# Helper function for easy setup
def setup_event_system(
    bootstrap_servers: str,
    schema_registry_url: str,
    read_model_store: Any
) -> tuple[EventStore, EventBus, QueryHandler]:
    """Setup event system with all components."""
    # Create event store
    event_store = EventStore(
        bootstrap_servers=bootstrap_servers,
        schema_registry_url=schema_registry_url
    )
    
    # Create event bus
    event_bus = EventBus(event_store)
    
    # Create query handler
    query_handler = QueryHandler(read_model_store)
    
    # Create projector
    projector = EventProjector(event_bus, read_model_store)
    
    return event_store, event_bus, query_handler