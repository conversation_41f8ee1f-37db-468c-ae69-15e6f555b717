"""
🧬 Project Chimera: Adaptive Intelligence for AURA

This package implements the AdaptiveTVAE (Adaptive Topological Variational Autoencoder)
and the closed-loop MLOps pipeline for continuous learning and evolution.

Key Components:
- AdaptiveTVAE: Self-modifying neural architecture with topological awareness
- MLOps Pipeline: Automated retraining and deployment with safety constraints
- Monitoring: Real-time performance tracking and drift detection
- Collective Intelligence: Swarm-based learning patterns
"""

__version__ = "1.0.0"
__author__ = "AURA Intelligence Team"
__status__ = "Development"

from .models.adaptive_tvae import AdaptiveTVAE
from .mlops.monitor_agent import ModelMonitorAgent
from .mlops.training_pipeline import ChimeraTrainingPipeline

__all__ = [
    "AdaptiveTVAE",
    "ModelMonitorAgent",
    "ChimeraTrainingPipeline",
]