"""
Chimera Configuration Module

Provides configuration management for the AdaptiveTVAE system.
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any
import os
import json
import yaml
from pathlib import Path


@dataclass
class TopologicalConfig:
    """Configuration for topological feature extraction."""
    persistence_dim: List[int] = field(default_factory=lambda: [0, 1, 2])
    filtration_type: str = "rips"
    max_edge_length: float = 10.0
    image_resolution: tuple = (50, 50)
    num_landscapes: int = 5
    embedding_dim: int = 128


@dataclass
class EncoderConfig:
    """Configuration for adaptive encoder."""
    latent_dim: int = 32
    initial_hidden_dims: List[int] = field(default_factory=lambda: [256, 128, 64])
    search_budget: int = 100
    search_space: Dict[str, Any] = field(default_factory=lambda: {
        'layer_sizes': [32, 64, 128, 256, 512, 1024],
        'activation_functions': ['relu', 'gelu', 'silu', 'leaky_relu', 'elu'],
        'dropout_rates': [0.0, 0.1, 0.2, 0.3, 0.4],
        'max_layers': 6,
        'min_layers': 2
    })


@dataclass
class VariationalConfig:
    """Configuration for variational bottleneck."""
    initial_beta: float = 1.0
    initial_capacity: float = 5.0
    min_capacity: float = 0.0
    max_capacity: float = 25.0
    num_latent_scales: int = 3
    use_flow: bool = True


@dataclass
class TrainingConfig:
    """Configuration for training pipeline."""
    batch_size: int = 32
    learning_rate: float = 1e-3
    num_epochs: int = 100
    early_stopping_patience: int = 10
    gradient_clip_value: float = 1.0
    warmup_epochs: int = 5
    validation_split: float = 0.2


@dataclass
class MonitoringConfig:
    """Configuration for monitoring and observability."""
    prometheus_port: int = 9090
    otlp_endpoint: Optional[str] = None
    console_tracing: bool = False
    log_level: str = "INFO"
    metrics_interval: int = 60  # seconds
    alert_thresholds: Dict[str, float] = field(default_factory=lambda: {
        'model_drift': 0.1,
        'accuracy_drop': 0.05,
        'latency_ms': 100,
        'error_rate': 0.01
    })


@dataclass
class MLOpsConfig:
    """Configuration for MLOps pipeline."""
    model_registry_url: str = "http://localhost:8080"
    nats_url: str = "nats://localhost:4222"
    drift_detection_window: int = 1000  # samples
    retraining_threshold: float = 0.1
    model_versioning: bool = True
    auto_rollback: bool = True
    canary_deployment_ratio: float = 0.1


@dataclass
class ChimeraConfig:
    """Main configuration for Chimera AdaptiveTVAE system."""
    # Model components
    topological: TopologicalConfig = field(default_factory=TopologicalConfig)
    encoder: EncoderConfig = field(default_factory=EncoderConfig)
    variational: VariationalConfig = field(default_factory=VariationalConfig)
    
    # Training and operations
    training: TrainingConfig = field(default_factory=TrainingConfig)
    monitoring: MonitoringConfig = field(default_factory=MonitoringConfig)
    mlops: MLOpsConfig = field(default_factory=MLOpsConfig)
    
    # System settings
    device: str = "cuda"
    seed: int = 42
    experiment_name: str = "chimera_adaptive_tvae"
    checkpoint_dir: str = "./checkpoints"
    log_dir: str = "./logs"
    
    @classmethod
    def from_file(cls, config_path: str) -> 'ChimeraConfig':
        """Load configuration from file (JSON or YAML)."""
        path = Path(config_path)
        
        if not path.exists():
            raise FileNotFoundError(f"Config file not found: {config_path}")
        
        with open(path, 'r') as f:
            if path.suffix == '.json':
                config_dict = json.load(f)
            elif path.suffix in ['.yaml', '.yml']:
                config_dict = yaml.safe_load(f)
            else:
                raise ValueError(f"Unsupported config format: {path.suffix}")
        
        return cls.from_dict(config_dict)
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'ChimeraConfig':
        """Create configuration from dictionary."""
        # Create nested configs
        topological = TopologicalConfig(**config_dict.get('topological', {}))
        encoder = EncoderConfig(**config_dict.get('encoder', {}))
        variational = VariationalConfig(**config_dict.get('variational', {}))
        training = TrainingConfig(**config_dict.get('training', {}))
        monitoring = MonitoringConfig(**config_dict.get('monitoring', {}))
        mlops = MLOpsConfig(**config_dict.get('mlops', {}))
        
        # Remove nested configs from dict
        for key in ['topological', 'encoder', 'variational', 'training', 'monitoring', 'mlops']:
            config_dict.pop(key, None)
        
        # Create main config
        return cls(
            topological=topological,
            encoder=encoder,
            variational=variational,
            training=training,
            monitoring=monitoring,
            mlops=mlops,
            **config_dict
        )
    
    @classmethod
    def from_env(cls) -> 'ChimeraConfig':
        """Create configuration from environment variables."""
        config = cls()
        
        # Override with environment variables
        env_mappings = {
            'CHIMERA_DEVICE': 'device',
            'CHIMERA_SEED': 'seed',
            'CHIMERA_EXPERIMENT': 'experiment_name',
            'CHIMERA_CHECKPOINT_DIR': 'checkpoint_dir',
            'CHIMERA_LOG_DIR': 'log_dir',
            'CHIMERA_BATCH_SIZE': ('training', 'batch_size', int),
            'CHIMERA_LEARNING_RATE': ('training', 'learning_rate', float),
            'CHIMERA_EPOCHS': ('training', 'num_epochs', int),
            'CHIMERA_PROMETHEUS_PORT': ('monitoring', 'prometheus_port', int),
            'CHIMERA_OTLP_ENDPOINT': ('monitoring', 'otlp_endpoint', str),
            'CHIMERA_LOG_LEVEL': ('monitoring', 'log_level', str),
            'CHIMERA_NATS_URL': ('mlops', 'nats_url', str),
            'CHIMERA_MODEL_REGISTRY': ('mlops', 'model_registry_url', str),
        }
        
        for env_var, mapping in env_mappings.items():
            value = os.environ.get(env_var)
            if value is not None:
                if isinstance(mapping, tuple):
                    # Nested config
                    nested_config, attr, type_func = mapping
                    nested = getattr(config, nested_config)
                    setattr(nested, attr, type_func(value))
                else:
                    # Top-level config
                    setattr(config, mapping, value)
        
        return config
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return {
            'topological': self.topological.__dict__,
            'encoder': self.encoder.__dict__,
            'variational': self.variational.__dict__,
            'training': self.training.__dict__,
            'monitoring': self.monitoring.__dict__,
            'mlops': self.mlops.__dict__,
            'device': self.device,
            'seed': self.seed,
            'experiment_name': self.experiment_name,
            'checkpoint_dir': self.checkpoint_dir,
            'log_dir': self.log_dir
        }
    
    def save(self, config_path: str):
        """Save configuration to file."""
        path = Path(config_path)
        config_dict = self.to_dict()
        
        with open(path, 'w') as f:
            if path.suffix == '.json':
                json.dump(config_dict, f, indent=2)
            elif path.suffix in ['.yaml', '.yml']:
                yaml.dump(config_dict, f, default_flow_style=False)
            else:
                raise ValueError(f"Unsupported config format: {path.suffix}")
    
    def validate(self) -> List[str]:
        """Validate configuration and return list of issues."""
        issues = []
        
        # Validate paths
        if not Path(self.checkpoint_dir).parent.exists():
            issues.append(f"Checkpoint directory parent does not exist: {self.checkpoint_dir}")
        
        if not Path(self.log_dir).parent.exists():
            issues.append(f"Log directory parent does not exist: {self.log_dir}")
        
        # Validate numeric ranges
        if self.training.learning_rate <= 0:
            issues.append("Learning rate must be positive")
        
        if self.training.batch_size <= 0:
            issues.append("Batch size must be positive")
        
        if self.variational.initial_capacity < self.variational.min_capacity:
            issues.append("Initial capacity must be >= min capacity")
        
        if self.variational.initial_capacity > self.variational.max_capacity:
            issues.append("Initial capacity must be <= max capacity")
        
        # Validate device
        if self.device not in ['cpu', 'cuda', 'mps']:
            issues.append(f"Invalid device: {self.device}")
        
        return issues