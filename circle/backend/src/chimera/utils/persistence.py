"""
Persistence Diagram utilities for Topological Data Analysis
"""

from dataclasses import dataclass
from typing import List, Tuple, Optional, Dict, Any
import numpy as np
import torch
from gudhi import RipsComplex
from gudhi.representations import PersistenceImage, PersistenceLandscape
import persim


@dataclass
class PersistenceDiagram:
    """
    Represents a persistence diagram with birth-death pairs for different homology dimensions.
    
    Attributes:
        dimension: The homology dimension (0=connected components, 1=loops, 2=voids)
        pairs: List of (birth, death) tuples representing topological features
        infinity_pairs: Features that persist to infinity
    """
    dimension: int
    pairs: List[Tuple[float, float]]
    infinity_pairs: List[float]
    
    def __post_init__(self):
        """Validate and normalize the persistence diagram."""
        # Convert to numpy arrays for efficient computation
        self.pairs = np.array(self.pairs) if self.pairs else np.array([])
        self.infinity_pairs = np.array(self.infinity_pairs) if self.infinity_pairs else np.array([])
        
    @property
    def persistence(self) -> np.ndarray:
        """Calculate persistence (death - birth) for each feature."""
        if len(self.pairs) == 0:
            return np.array([])
        return self.pairs[:, 1] - self.pairs[:, 0]
    
    @property
    def midlife(self) -> np.ndarray:
        """Calculate midlife (birth + death) / 2 for each feature."""
        if len(self.pairs) == 0:
            return np.array([])
        return (self.pairs[:, 0] + self.pairs[:, 1]) / 2
    
    def to_persistence_image(self, resolution: Tuple[int, int] = (50, 50), 
                           sigma: float = 0.1) -> np.ndarray:
        """
        Convert persistence diagram to a persistence image.
        
        Args:
            resolution: Image resolution (height, width)
            sigma: Gaussian kernel bandwidth
            
        Returns:
            Persistence image as numpy array
        """
        pi = PersistenceImage(resolution=resolution, bandwidth=sigma)
        
        # Combine finite and infinite pairs
        all_pairs = []
        if len(self.pairs) > 0:
            all_pairs.extend(self.pairs.tolist())
        
        # For infinite pairs, use a large value as death time
        max_val = np.max(self.pairs[:, 1]) if len(self.pairs) > 0 else 100.0
        for birth in self.infinity_pairs:
            all_pairs.append([birth, max_val * 1.5])
            
        if not all_pairs:
            return np.zeros(resolution)
            
        # Reshape for gudhi format
        diagram = np.array(all_pairs)
        return pi.fit_transform([diagram])[0]
    
    def to_persistence_landscape(self, num_landscapes: int = 5, 
                               resolution: int = 100) -> np.ndarray:
        """
        Convert persistence diagram to persistence landscapes.
        
        Args:
            num_landscapes: Number of landscape functions to compute
            resolution: Number of sample points
            
        Returns:
            Persistence landscapes as numpy array
        """
        pl = PersistenceLandscape(num_landscapes=num_landscapes, resolution=resolution)
        
        # Prepare diagram
        all_pairs = []
        if len(self.pairs) > 0:
            all_pairs.extend(self.pairs.tolist())
            
        max_val = np.max(self.pairs[:, 1]) if len(self.pairs) > 0 else 100.0
        for birth in self.infinity_pairs:
            all_pairs.append([birth, max_val * 1.5])
            
        if not all_pairs:
            return np.zeros((num_landscapes, resolution))
            
        diagram = np.array(all_pairs)
        return pl.fit_transform([diagram])[0]
    
    def wasserstein_distance(self, other: 'PersistenceDiagram', p: int = 2) -> float:
        """
        Compute Wasserstein distance between two persistence diagrams.
        
        Args:
            other: Another persistence diagram
            p: Order of the Wasserstein distance
            
        Returns:
            Wasserstein distance
        """
        # Prepare diagrams for persim
        diag1 = self.pairs if len(self.pairs) > 0 else np.array([[0, 0]])
        diag2 = other.pairs if len(other.pairs) > 0 else np.array([[0, 0]])
        
        return persim.wasserstein(diag1, diag2, matching=False)
    
    def bottleneck_distance(self, other: 'PersistenceDiagram') -> float:
        """
        Compute bottleneck distance between two persistence diagrams.
        
        Args:
            other: Another persistence diagram
            
        Returns:
            Bottleneck distance
        """
        diag1 = self.pairs if len(self.pairs) > 0 else np.array([[0, 0]])
        diag2 = other.pairs if len(other.pairs) > 0 else np.array([[0, 0]])
        
        return persim.bottleneck(diag1, diag2, matching=False)
    
    def to_tensor(self, device: Optional[torch.device] = None) -> Dict[str, torch.Tensor]:
        """
        Convert persistence diagram to PyTorch tensors.
        
        Args:
            device: Target device for tensors
            
        Returns:
            Dictionary containing tensor representations
        """
        if device is None:
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            
        return {
            'pairs': torch.tensor(self.pairs, dtype=torch.float32, device=device),
            'infinity_pairs': torch.tensor(self.infinity_pairs, dtype=torch.float32, device=device),
            'persistence': torch.tensor(self.persistence, dtype=torch.float32, device=device),
            'midlife': torch.tensor(self.midlife, dtype=torch.float32, device=device),
            'dimension': torch.tensor(self.dimension, dtype=torch.int32, device=device)
        }
    
    def summary_statistics(self) -> Dict[str, float]:
        """
        Compute summary statistics of the persistence diagram.
        
        Returns:
            Dictionary of statistics
        """
        if len(self.pairs) == 0:
            return {
                'num_features': 0,
                'num_infinite': len(self.infinity_pairs),
                'total_persistence': 0.0,
                'mean_persistence': 0.0,
                'max_persistence': 0.0,
                'entropy': 0.0
            }
            
        persistence = self.persistence
        
        # Compute persistence entropy
        if np.sum(persistence) > 0:
            p = persistence / np.sum(persistence)
            entropy = -np.sum(p * np.log(p + 1e-10))
        else:
            entropy = 0.0
            
        return {
            'num_features': len(self.pairs),
            'num_infinite': len(self.infinity_pairs),
            'total_persistence': float(np.sum(persistence)),
            'mean_persistence': float(np.mean(persistence)),
            'max_persistence': float(np.max(persistence)),
            'entropy': float(entropy)
        }