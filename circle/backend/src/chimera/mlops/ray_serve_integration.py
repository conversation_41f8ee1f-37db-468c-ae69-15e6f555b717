"""
🚀 Ray Serve Integration for Chimera ML Models

Provides scalable, production-ready model serving using Ray Serve
with support for model multiplexing, autoscaling, and A/B testing.
"""

import logging
from typing import Dict, List, Optional, Any, Union, Callable
from dataclasses import dataclass
from abc import ABC, abstractmethod
import numpy as np

import ray
from ray import serve
from ray.serve.handle import DeploymentHandle
from pydantic import BaseModel, Field
import pandas as pd
import asyncio

logger = logging.getLogger(__name__)


class ModelConfig(BaseModel):
    """Configuration for a model deployment."""
    model_id: str
    model_path: str
    model_type: str  # "sklearn", "pytorch", "tensorflow", "xgboost"
    version: str
    preprocessing_fn: Optional[str] = None
    postprocessing_fn: Optional[str] = None
    batch_size: int = Field(default=1, ge=1)
    max_batch_wait_ms: int = Field(default=10, ge=0)
    num_replicas: int = Field(default=1, ge=1)
    ray_actor_options: Dict[str, Any] = Field(default_factory=dict)


class PredictionRequest(BaseModel):
    """Standard prediction request format."""
    model_id: str
    inputs: Union[Dict[str, Any], List[Dict[str, Any]]]
    parameters: Optional[Dict[str, Any]] = None


class PredictionResponse(BaseModel):
    """Standard prediction response format."""
    model_id: str
    predictions: Union[Any, List[Any]]
    model_version: str
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class ModelMetrics:
    """Metrics for model performance."""
    request_count: int = 0
    prediction_count: int = 0
    error_count: int = 0
    total_latency_ms: float = 0
    batch_sizes: List[int] = None
    
    def __post_init__(self):
        if self.batch_sizes is None:
            self.batch_sizes = []


class BaseModelDeployment(ABC):
    """Base class for model deployments."""
    
    def __init__(self, config: ModelConfig):
        self.config = config
        self.model = None
        self.metrics = ModelMetrics()
        self._load_model()
        
    @abstractmethod
    def _load_model(self):
        """Load the model from storage."""
        pass
        
    @abstractmethod
    def predict(self, inputs: Any) -> Any:
        """Generate predictions."""
        pass
        
    def preprocess(self, inputs: Any) -> Any:
        """Preprocess inputs before prediction."""
        if self.config.preprocessing_fn:
            # Dynamic preprocessing function loading
            module_name, func_name = self.config.preprocessing_fn.rsplit('.', 1)
            module = __import__(module_name, fromlist=[func_name])
            preprocess_fn = getattr(module, func_name)
            return preprocess_fn(inputs)
        return inputs
        
    def postprocess(self, predictions: Any) -> Any:
        """Postprocess predictions."""
        if self.config.postprocessing_fn:
            # Dynamic postprocessing function loading
            module_name, func_name = self.config.postprocessing_fn.rsplit('.', 1)
            module = __import__(module_name, fromlist=[func_name])
            postprocess_fn = getattr(module, func_name)
            return postprocess_fn(predictions)
        return predictions


@serve.deployment(
    route_prefix="/models",
    ray_actor_options={"num_cpus": 1}
)
class SklearnModelDeployment(BaseModelDeployment):
    """Deployment for scikit-learn models."""
    
    def _load_model(self):
        """Load sklearn model."""
        import joblib
        self.model = joblib.load(self.config.model_path)
        logger.info(f"Loaded sklearn model from {self.config.model_path}")
        
    def predict(self, inputs: Union[np.ndarray, pd.DataFrame]) -> np.ndarray:
        """Generate predictions."""
        predictions = self.model.predict(inputs)
        return predictions


@serve.deployment(
    route_prefix="/models",
    ray_actor_options={"num_gpus": 0.5}
)
class PyTorchModelDeployment(BaseModelDeployment):
    """Deployment for PyTorch models."""
    
    def _load_model(self):
        """Load PyTorch model."""
        import torch
        
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model = torch.load(self.config.model_path, map_location=self.device)
        self.model.eval()
        logger.info(f"Loaded PyTorch model on {self.device}")
        
    def predict(self, inputs: torch.Tensor) -> torch.Tensor:
        """Generate predictions."""
        import torch
        
        with torch.no_grad():
            inputs = inputs.to(self.device)
            predictions = self.model(inputs)
        return predictions.cpu()


@serve.deployment(
    route_prefix="/models",
    ray_actor_options={"num_cpus": 2}
)
class ModelRouter:
    """Routes requests to appropriate model deployments."""
    
    def __init__(self):
        self.model_registry: Dict[str, DeploymentHandle] = {}
        self.model_configs: Dict[str, ModelConfig] = {}
        self.routing_metrics: Dict[str, int] = {}
        
    def register_model(self, config: ModelConfig):
        """Register a new model deployment."""
        # Create appropriate deployment based on model type
        if config.model_type == "sklearn":
            deployment_cls = SklearnModelDeployment
        elif config.model_type == "pytorch":
            deployment_cls = PyTorchModelDeployment
        else:
            raise ValueError(f"Unsupported model type: {config.model_type}")
            
        # Deploy with configuration
        deployment = deployment_cls.options(
            num_replicas=config.num_replicas,
            max_concurrent_queries=100,
            **config.ray_actor_options
        ).bind(config)
        
        # Store handle
        self.model_registry[config.model_id] = deployment
        self.model_configs[config.model_id] = config
        self.routing_metrics[config.model_id] = 0
        
        logger.info(f"Registered model {config.model_id} version {config.version}")
        
    async def predict(self, request: PredictionRequest) -> PredictionResponse:
        """Route prediction request to appropriate model."""
        if request.model_id not in self.model_registry:
            raise ValueError(f"Model {request.model_id} not found")
            
        # Get model handle
        model_handle = self.model_registry[request.model_id]
        config = self.model_configs[request.model_id]
        
        # Update metrics
        self.routing_metrics[request.model_id] += 1
        
        # Preprocess if needed
        inputs = request.inputs
        
        # Make prediction
        predictions = await model_handle.predict.remote(inputs)
        
        # Build response
        response = PredictionResponse(
            model_id=request.model_id,
            predictions=predictions,
            model_version=config.version,
            metadata={
                "model_type": config.model_type,
                "request_count": self.routing_metrics[request.model_id]
            }
        )
        
        return response
        
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about registered models."""
        return {
            model_id: {
                "version": config.version,
                "type": config.model_type,
                "replicas": config.num_replicas,
                "requests_served": self.routing_metrics.get(model_id, 0)
            }
            for model_id, config in self.model_configs.items()
        }


@serve.deployment(
    route_prefix="/ensemble",
    ray_actor_options={"num_cpus": 1}
)
class EnsembleDeployment:
    """Ensemble multiple models for improved predictions."""
    
    def __init__(self, model_handles: List[DeploymentHandle], weights: Optional[List[float]] = None):
        self.model_handles = model_handles
        self.weights = weights or [1.0 / len(model_handles)] * len(model_handles)
        
    async def predict(self, inputs: Any) -> Any:
        """Generate ensemble predictions."""
        # Get predictions from all models in parallel
        prediction_refs = [
            handle.predict.remote(inputs) 
            for handle in self.model_handles
        ]
        
        predictions = await asyncio.gather(*prediction_refs)
        
        # Weighted average ensemble
        ensemble_pred = sum(
            weight * pred 
            for weight, pred in zip(self.weights, predictions)
        )
        
        return ensemble_pred


class ModelServingManager:
    """Manages Ray Serve deployments for ML models."""
    
    def __init__(self, ray_address: Optional[str] = None):
        # Initialize Ray
        if not ray.is_initialized():
            ray.init(address=ray_address)
            
        # Start Ray Serve
        serve.start(detached=True)
        
        self.router_handle = None
        
    def deploy_model_router(self) -> DeploymentHandle:
        """Deploy the main model router."""
        self.router_handle = serve.run(
            ModelRouter.bind(),
            name="model-router",
            route_prefix="/api/v1"
        )
        return self.router_handle
        
    def deploy_model(self, config: ModelConfig):
        """Deploy a single model."""
        if not self.router_handle:
            self.deploy_model_router()
            
        # Register with router
        ray.get(self.router_handle.register_model.remote(config))
        
    def deploy_ensemble(self, model_configs: List[ModelConfig], weights: Optional[List[float]] = None):
        """Deploy an ensemble of models."""
        # Deploy individual models
        handles = []
        for config in model_configs:
            if config.model_type == "sklearn":
                deployment = SklearnModelDeployment.bind(config)
            elif config.model_type == "pytorch":
                deployment = PyTorchModelDeployment.bind(config)
            else:
                raise ValueError(f"Unsupported model type: {config.model_type}")
                
            handle = serve.run(
                deployment,
                name=f"model-{config.model_id}",
                route_prefix=f"/models/{config.model_id}"
            )
            handles.append(handle)
            
        # Deploy ensemble
        ensemble_handle = serve.run(
            EnsembleDeployment.bind(handles, weights),
            name="ensemble",
            route_prefix="/api/v1/ensemble"
        )
        
        return ensemble_handle
        
    def update_model(self, model_id: str, new_config: ModelConfig):
        """Update a deployed model with zero downtime."""
        # Deploy new version
        new_deployment_name = f"model-{model_id}-v{new_config.version}"
        
        if new_config.model_type == "sklearn":
            deployment = SklearnModelDeployment.bind(new_config)
        elif new_config.model_type == "pytorch":
            deployment = PyTorchModelDeployment.bind(new_config)
        else:
            raise ValueError(f"Unsupported model type: {new_config.model_type}")
            
        # Deploy new version
        new_handle = serve.run(
            deployment,
            name=new_deployment_name,
            route_prefix=f"/models/{model_id}/v{new_config.version}"
        )
        
        # Update router to use new version
        if self.router_handle:
            ray.get(self.router_handle.register_model.remote(new_config))
            
        logger.info(f"Updated model {model_id} to version {new_config.version}")
        
    def get_deployment_status(self) -> Dict[str, Any]:
        """Get status of all deployments."""
        deployments = serve.list_deployments()
        
        status = {}
        for deployment in deployments:
            info = serve.get_deployment(deployment).get_info()
            status[deployment] = {
                "status": info.deployment_config.autoscaling_config,
                "num_replicas": info.deployment_config.num_replicas,
                "ray_actor_options": info.deployment_config.ray_actor_options
            }
            
        return status
        
    def shutdown(self):
        """Shutdown Ray Serve."""
        serve.shutdown()
        ray.shutdown()