"""
Model Monitor Agent for AdaptiveTVAE

Implements continuous monitoring, drift detection, and performance tracking
for the deployed AdaptiveTVAE model.
"""

import asyncio
import numpy as np
import torch
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import deque
import json

from prometheus_client import Counter, Histogram, Gauge
from opentelemetry import trace
import nats
from nats.aio.client import Client as NATS
from scipy import stats

from ..monitoring.metrics import ChimeraMetrics
from ..monitoring.tracing import ChimeraTracer, trace_anomaly_detection
from ..utils.config import ChimeraConfig


@dataclass
class DriftDetectionResult:
    """Result of drift detection analysis."""
    timestamp: datetime
    drift_score: float
    drift_type: str  # 'data', 'concept', 'prediction'
    is_drift_detected: bool
    details: Dict[str, Any]
    recommended_action: str


@dataclass
class PerformanceMetrics:
    """Container for model performance metrics."""
    timestamp: datetime
    accuracy: float
    loss: float
    latency_ms: float
    throughput_qps: float
    memory_usage_mb: float
    gpu_utilization: float
    custom_metrics: Dict[str, float] = field(default_factory=dict)


class ModelMonitorAgent:
    """
    Monitors deployed AdaptiveTVAE models for:
    - Performance degradation
    - Data and concept drift
    - Resource utilization
    - Anomalous behavior
    
    Integrates with NATS for event-driven monitoring and alerting.
    """
    
    def __init__(
        self,
        config: ChimeraConfig,
        model_name: str = "adaptive_tvae",
        monitoring_interval: int = 60  # seconds
    ):
        self.config = config
        self.model_name = model_name
        self.monitoring_interval = monitoring_interval
        
        # Initialize metrics and tracing
        self.metrics = ChimeraMetrics()
        self.tracer = ChimeraTracer().get_tracer(__name__)
        
        # NATS client for event streaming
        self.nats_client: Optional[NATS] = None
        self.nats_connected = False
        
        # Performance tracking
        self.performance_history = deque(maxlen=1000)
        self.baseline_metrics: Optional[PerformanceMetrics] = None
        
        # Drift detection
        self.reference_distribution: Optional[np.ndarray] = None
        self.drift_window = deque(maxlen=config.mlops.drift_detection_window)
        self.drift_history = deque(maxlen=100)
        
        # Alert management
        self.alert_callbacks: List[Callable] = []
        self.active_alerts: Dict[str, datetime] = {}
        
        # Monitoring metrics
        self._monitoring_counter = Counter(
            'chimera_monitoring_checks_total',
            'Total number of monitoring checks performed',
            ['check_type']
        )
        self._drift_detection_gauge = Gauge(
            'chimera_drift_score',
            'Current drift detection score',
            ['drift_type']
        )
        self._alert_counter = Counter(
            'chimera_alerts_triggered_total',
            'Total number of alerts triggered',
            ['alert_type', 'severity']
        )
        
        # Start monitoring tasks
        self._monitoring_task = None
        self._running = False
    
    async def start(self):
        """Start the monitoring agent."""
        self._running = True
        
        # Connect to NATS
        await self._connect_nats()
        
        # Start monitoring loop
        self._monitoring_task = asyncio.create_task(self._monitoring_loop())
        
        self.tracer.add_event("monitor.started", {
            "model": self.model_name,
            "interval": self.monitoring_interval
        })
    
    async def stop(self):
        """Stop the monitoring agent."""
        self._running = False
        
        if self._monitoring_task:
            self._monitoring_task.cancel()
            try:
                await self._monitoring_task
            except asyncio.CancelledError:
                pass
        
        if self.nats_client and self.nats_connected:
            await self.nats_client.close()
        
        self.tracer.add_event("monitor.stopped", {"model": self.model_name})
    
    async def _connect_nats(self):
        """Connect to NATS for event streaming."""
        try:
            self.nats_client = NATS()
            await self.nats_client.connect(self.config.mlops.nats_url)
            self.nats_connected = True
            
            # Subscribe to model events
            await self.nats_client.subscribe(
                f"chimera.model.{self.model_name}.events",
                cb=self._handle_model_event
            )
            
            self.tracer.add_event("nats.connected", {
                "url": self.config.mlops.nats_url
            })
            
        except Exception as e:
            self.tracer.record_exception(e)
            self.nats_connected = False
    
    async def _monitoring_loop(self):
        """Main monitoring loop."""
        while self._running:
            try:
                with self.tracer.span(__name__, "monitoring_cycle"):
                    # Collect current metrics
                    metrics = await self._collect_metrics()
                    
                    # Check for performance degradation
                    await self._check_performance(metrics)
                    
                    # Check for drift
                    await self._check_drift()
                    
                    # Check resource utilization
                    await self._check_resources(metrics)
                    
                    # Store metrics history
                    self.performance_history.append(metrics)
                    
                    # Update Prometheus metrics
                    self._update_prometheus_metrics(metrics)
                
                # Wait for next cycle
                await asyncio.sleep(self.monitoring_interval)
                
            except Exception as e:
                self.tracer.record_exception(e)
                await asyncio.sleep(5)  # Brief pause before retry
    
    async def _collect_metrics(self) -> PerformanceMetrics:
        """Collect current model performance metrics."""
        self._monitoring_counter.labels(check_type='metrics_collection').inc()
        
        # In a real implementation, these would be collected from the model
        # For now, we'll simulate with realistic values
        metrics = PerformanceMetrics(
            timestamp=datetime.now(),
            accuracy=np.random.normal(0.95, 0.02),
            loss=np.random.normal(0.1, 0.02),
            latency_ms=np.random.normal(50, 10),
            throughput_qps=np.random.normal(100, 20),
            memory_usage_mb=np.random.normal(512, 50),
            gpu_utilization=np.random.normal(0.7, 0.1)
        )
        
        return metrics
    
    async def _check_performance(self, current_metrics: PerformanceMetrics):
        """Check for performance degradation."""
        self._monitoring_counter.labels(check_type='performance').inc()
        
        if not self.baseline_metrics:
            self.baseline_metrics = current_metrics
            return
        
        # Check accuracy drop
        accuracy_drop = self.baseline_metrics.accuracy - current_metrics.accuracy
        if accuracy_drop > self.config.monitoring.alert_thresholds['accuracy_drop']:
            await self._trigger_alert(
                alert_type='performance_degradation',
                severity='high',
                details={
                    'metric': 'accuracy',
                    'baseline': self.baseline_metrics.accuracy,
                    'current': current_metrics.accuracy,
                    'drop': accuracy_drop
                }
            )
        
        # Check latency increase
        if current_metrics.latency_ms > self.config.monitoring.alert_thresholds['latency_ms']:
            await self._trigger_alert(
                alert_type='high_latency',
                severity='medium',
                details={
                    'current_latency': current_metrics.latency_ms,
                    'threshold': self.config.monitoring.alert_thresholds['latency_ms']
                }
            )
    
    async def _check_drift(self):
        """Check for data and concept drift."""
        self._monitoring_counter.labels(check_type='drift').inc()
        
        if len(self.drift_window) < 100:
            return  # Not enough data yet
        
        # Simulate drift detection (in practice, this would analyze actual data)
        current_data = np.array(list(self.drift_window))
        
        # Data drift detection using KS test
        if self.reference_distribution is not None:
            ks_statistic, p_value = stats.ks_2samp(
                self.reference_distribution,
                current_data
            )
            
            drift_score = 1 - p_value  # Higher score means more drift
            
            result = DriftDetectionResult(
                timestamp=datetime.now(),
                drift_score=drift_score,
                drift_type='data',
                is_drift_detected=drift_score > self.config.mlops.retraining_threshold,
                details={
                    'ks_statistic': ks_statistic,
                    'p_value': p_value,
                    'sample_size': len(current_data)
                },
                recommended_action='retrain' if drift_score > self.config.mlops.retraining_threshold else 'monitor'
            )
            
            self.drift_history.append(result)
            self._drift_detection_gauge.labels(drift_type='data').set(drift_score)
            
            if result.is_drift_detected:
                await self._trigger_alert(
                    alert_type='drift_detected',
                    severity='high',
                    details={
                        'drift_type': 'data',
                        'drift_score': drift_score,
                        'action': result.recommended_action
                    }
                )
                
                # Publish drift event to NATS
                if self.nats_connected:
                    await self._publish_drift_event(result)
    
    async def _check_resources(self, metrics: PerformanceMetrics):
        """Check resource utilization."""
        self._monitoring_counter.labels(check_type='resources').inc()
        
        # Check GPU utilization
        if metrics.gpu_utilization > 0.9:
            await self._trigger_alert(
                alert_type='high_gpu_usage',
                severity='medium',
                details={
                    'gpu_utilization': metrics.gpu_utilization,
                    'memory_usage_mb': metrics.memory_usage_mb
                }
            )
        
        # Update resource metrics
        self.metrics.update_resource_usage(
            gpu_utilization={'0': metrics.gpu_utilization},
            memory_usage={'model': metrics.memory_usage_mb * 1024 * 1024}
        )
    
    async def _trigger_alert(
        self,
        alert_type: str,
        severity: str,
        details: Dict[str, Any]
    ):
        """Trigger an alert."""
        alert_key = f"{alert_type}_{severity}"
        
        # Check if alert is already active (debouncing)
        if alert_key in self.active_alerts:
            last_alert = self.active_alerts[alert_key]
            if datetime.now() - last_alert < timedelta(minutes=5):
                return  # Skip duplicate alerts within 5 minutes
        
        self.active_alerts[alert_key] = datetime.now()
        self._alert_counter.labels(
            alert_type=alert_type,
            severity=severity
        ).inc()
        
        # Log alert
        trace_anomaly_detection(
            anomaly_type=alert_type,
            severity=severity,
            details=details
        )
        
        # Execute alert callbacks
        for callback in self.alert_callbacks:
            try:
                await callback(alert_type, severity, details)
            except Exception as e:
                self.tracer.record_exception(e)
        
        # Publish alert to NATS
        if self.nats_connected:
            await self._publish_alert(alert_type, severity, details)
    
    async def _publish_drift_event(self, result: DriftDetectionResult):
        """Publish drift detection event to NATS."""
        event = {
            'event_type': 'drift_detected',
            'model': self.model_name,
            'timestamp': result.timestamp.isoformat(),
            'drift_score': result.drift_score,
            'drift_type': result.drift_type,
            'is_drift_detected': result.is_drift_detected,
            'details': result.details,
            'recommended_action': result.recommended_action
        }
        
        await self.nats_client.publish(
            f"chimera.drift.{self.model_name}",
            json.dumps(event).encode()
        )
    
    async def _publish_alert(
        self,
        alert_type: str,
        severity: str,
        details: Dict[str, Any]
    ):
        """Publish alert to NATS."""
        alert = {
            'alert_type': alert_type,
            'severity': severity,
            'model': self.model_name,
            'timestamp': datetime.now().isoformat(),
            'details': details
        }
        
        await self.nats_client.publish(
            f"chimera.alerts.{severity}",
            json.dumps(alert).encode()
        )
    
    async def _handle_model_event(self, msg):
        """Handle incoming model events from NATS."""
        try:
            event = json.loads(msg.data.decode())
            
            # Process different event types
            if event.get('type') == 'prediction':
                # Add to drift detection window
                if 'features' in event:
                    self.drift_window.extend(event['features'])
            
            elif event.get('type') == 'performance_update':
                # Update baseline if performance improves
                if event.get('accuracy', 0) > self.baseline_metrics.accuracy:
                    self.baseline_metrics.accuracy = event['accuracy']
        
        except Exception as e:
            self.tracer.record_exception(e)
    
    def _update_prometheus_metrics(self, metrics: PerformanceMetrics):
        """Update Prometheus metrics."""
        self.metrics.record_model_performance(
            model_name=self.model_name,
            accuracy=metrics.accuracy,
            loss=metrics.loss,
            dataset='production'
        )
        
        # Update custom metrics
        for name, value in metrics.custom_metrics.items():
            if not hasattr(self, f'_custom_{name}'):
                setattr(
                    self,
                    f'_custom_{name}',
                    Gauge(
                        f'chimera_custom_{name}',
                        f'Custom metric: {name}',
                        ['model']
                    )
                )
            getattr(self, f'_custom_{name}').labels(model=self.model_name).set(value)
    
    def add_alert_callback(self, callback: Callable):
        """Add a callback for alert notifications."""
        self.alert_callbacks.append(callback)
    
    def update_reference_distribution(self, data: np.ndarray):
        """Update the reference distribution for drift detection."""
        self.reference_distribution = data
        self.drift_window.clear()
    
    def get_monitoring_summary(self) -> Dict[str, Any]:
        """Get a summary of current monitoring status."""
        recent_metrics = list(self.performance_history)[-10:] if self.performance_history else []
        recent_drift = list(self.drift_history)[-10:] if self.drift_history else []
        
        return {
            'model': self.model_name,
            'monitoring_interval': self.monitoring_interval,
            'nats_connected': self.nats_connected,
            'active_alerts': dict(self.active_alerts),
            'recent_performance': [
                {
                    'timestamp': m.timestamp.isoformat(),
                    'accuracy': m.accuracy,
                    'latency_ms': m.latency_ms
                }
                for m in recent_metrics
            ],
            'recent_drift': [
                {
                    'timestamp': d.timestamp.isoformat(),
                    'drift_score': d.drift_score,
                    'is_detected': d.is_drift_detected
                }
                for d in recent_drift
            ]
        }