"""
MLOps Event Publisher for Chimera

Publishes MLOps pipeline events (retraining, deployment, rollback) to the event bus
for consumption by downstream systems including AURA agent council.
"""

import asyncio
import json
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum
import uuid

import nats
from nats.aio.client import Client as NATS
from prometheus_client import Counter, Histogram

logger = logging.getLogger(__name__)


class MLOpsEventType(Enum):
    """Types of MLOps events."""
    # Training events
    TRAINING_STARTED = "training_started"
    TRAINING_COMPLETED = "training_completed"
    TRAINING_FAILED = "training_failed"
    
    # Validation events
    VALIDATION_STARTED = "validation_started"
    VALIDATION_PASSED = "validation_passed"
    VALIDATION_FAILED = "validation_failed"
    
    # Deployment events
    DEPLOYMENT_STARTED = "deployment_started"
    DEPLOYMENT_COMPLETED = "deployment_completed"
    DEPLOYMENT_FAILED = "deployment_failed"
    
    # Rollback events
    ROLLBACK_INITIATED = "rollback_initiated"
    ROLLBACK_COMPLETED = "rollback_completed"
    
    # Monitoring events
    DRIFT_DETECTED = "drift_detected"
    PERFORMANCE_DEGRADED = "performance_degraded"
    ANOMALY_SURGE = "anomaly_surge"


@dataclass
class MLOpsEvent:
    """Standard MLOps event structure."""
    event_id: str
    event_type: MLOpsEventType
    timestamp: datetime
    model_version: str
    pipeline_id: str
    stage: str  # training, validation, deployment, monitoring
    status: str  # started, in_progress, completed, failed
    metadata: Dict[str, Any]
    metrics: Optional[Dict[str, float]] = None
    artifacts: Optional[Dict[str, str]] = None
    error_details: Optional[Dict[str, Any]] = None
    
    def to_json(self) -> str:
        """Convert to JSON for transmission."""
        data = asdict(self)
        data['event_type'] = self.event_type.value
        data['timestamp'] = self.timestamp.isoformat()
        return json.dumps(data)


class MLOpsEventPublisher:
    """
    Publishes MLOps pipeline events for downstream consumption.
    
    Features:
    - Structured event format
    - Priority-based publishing
    - Event aggregation for related events
    - Delivery guarantees with acknowledgment
    - Comprehensive metrics
    """
    
    def __init__(
        self,
        nats_url: str = "nats://localhost:4222",
        stream_name: str = "CHIMERA_MLOPS"
    ):
        self.nats_url = nats_url
        self.stream_name = stream_name
        
        # NATS client
        self.nc: Optional[NATS] = None
        self.js = None  # JetStream context
        
        # Event aggregation
        self.event_buffer: List[MLOpsEvent] = []
        self.buffer_size = 10
        self.flush_interval = 5.0  # seconds
        
        # Metrics
        self.events_published = Counter(
            'chimera_mlops_events_published_total',
            'Total MLOps events published',
            ['event_type', 'stage']
        )
        self.publish_latency = Histogram(
            'chimera_mlops_event_publish_latency_seconds',
            'Event publishing latency',
            ['event_type']
        )
        self.publish_errors = Counter(
            'chimera_mlops_event_publish_errors_total',
            'Event publishing errors',
            ['event_type', 'error_type']
        )
        
    async def start(self):
        """Start the event publisher and connect to NATS."""
        logger.info("Starting MLOps Event Publisher...")
        
        # Connect to NATS
        self.nc = await nats.connect(self.nats_url)
        self.js = self.nc.jetstream()
        
        # Create JetStream stream if it doesn't exist
        await self._ensure_stream_exists()
        
        # Start background tasks
        asyncio.create_task(self._flush_events_periodically())
        
        logger.info("MLOps Event Publisher started")
    
    async def stop(self):
        """Stop the event publisher."""
        # Flush any remaining events
        await self._flush_events()
        
        if self.nc:
            await self.nc.close()
    
    async def _ensure_stream_exists(self):
        """Ensure JetStream stream exists for MLOps events."""
        try:
            await self.js.stream_info(self.stream_name)
            logger.info(f"Stream {self.stream_name} already exists")
        except:
            # Create stream
            await self.js.add_stream(
                name=self.stream_name,
                subjects=[
                    "chimera.mlops.*",
                    "chimera.training.*",
                    "chimera.deployment.*",
                    "chimera.monitoring.*"
                ],
                retention="limits",
                max_msgs=100000,
                max_age=86400 * 7,  # 7 days
                storage="file",
                num_replicas=3
            )
            logger.info(f"Created stream {self.stream_name}")
    
    async def publish_training_started(
        self,
        model_version: str,
        pipeline_id: str,
        trigger_reason: str,
        training_config: Dict[str, Any]
    ):
        """Publish training started event."""
        event = MLOpsEvent(
            event_id=str(uuid.uuid4()),
            event_type=MLOpsEventType.TRAINING_STARTED,
            timestamp=datetime.now(),
            model_version=model_version,
            pipeline_id=pipeline_id,
            stage="training",
            status="started",
            metadata={
                "trigger_reason": trigger_reason,
                "training_config": training_config,
                "estimated_duration_minutes": 30
            }
        )
        
        await self._publish_event(event, priority="normal")
    
    async def publish_training_completed(
        self,
        model_version: str,
        pipeline_id: str,
        metrics: Dict[str, float],
        artifacts: Dict[str, str],
        training_duration: float
    ):
        """Publish training completed event."""
        event = MLOpsEvent(
            event_id=str(uuid.uuid4()),
            event_type=MLOpsEventType.TRAINING_COMPLETED,
            timestamp=datetime.now(),
            model_version=model_version,
            pipeline_id=pipeline_id,
            stage="training",
            status="completed",
            metadata={
                "training_duration_seconds": training_duration,
                "model_size_mb": metrics.get("model_size_mb", 0)
            },
            metrics=metrics,
            artifacts=artifacts
        )
        
        await self._publish_event(event, priority="normal")
    
    async def publish_validation_results(
        self,
        model_version: str,
        pipeline_id: str,
        passed: bool,
        validation_metrics: Dict[str, float],
        failure_reasons: Optional[List[str]] = None
    ):
        """Publish validation results event."""
        event_type = (
            MLOpsEventType.VALIDATION_PASSED 
            if passed 
            else MLOpsEventType.VALIDATION_FAILED
        )
        
        event = MLOpsEvent(
            event_id=str(uuid.uuid4()),
            event_type=event_type,
            timestamp=datetime.now(),
            model_version=model_version,
            pipeline_id=pipeline_id,
            stage="validation",
            status="completed" if passed else "failed",
            metadata={
                "validation_passed": passed,
                "failure_reasons": failure_reasons or []
            },
            metrics=validation_metrics
        )
        
        # Higher priority for failures
        priority = "high" if not passed else "normal"
        await self._publish_event(event, priority=priority)
    
    async def publish_deployment_event(
        self,
        model_version: str,
        deployment_type: str,  # shadow, canary, production
        status: str,  # started, completed, failed
        deployment_config: Dict[str, Any],
        error_details: Optional[Dict[str, Any]] = None
    ):
        """Publish deployment event."""
        event_type_map = {
            "started": MLOpsEventType.DEPLOYMENT_STARTED,
            "completed": MLOpsEventType.DEPLOYMENT_COMPLETED,
            "failed": MLOpsEventType.DEPLOYMENT_FAILED
        }
        
        event = MLOpsEvent(
            event_id=str(uuid.uuid4()),
            event_type=event_type_map[status],
            timestamp=datetime.now(),
            model_version=model_version,
            pipeline_id=f"deploy-{model_version}",
            stage="deployment",
            status=status,
            metadata={
                "deployment_type": deployment_type,
                "deployment_config": deployment_config,
                "target_environment": deployment_config.get("environment", "unknown")
            },
            error_details=error_details
        )
        
        # High priority for production deployments
        priority = "high" if deployment_type == "production" else "normal"
        await self._publish_event(event, priority=priority)
    
    async def publish_rollback_event(
        self,
        current_version: str,
        rollback_to_version: str,
        reason: str,
        metrics_snapshot: Dict[str, float]
    ):
        """Publish rollback event."""
        event = MLOpsEvent(
            event_id=str(uuid.uuid4()),
            event_type=MLOpsEventType.ROLLBACK_INITIATED,
            timestamp=datetime.now(),
            model_version=current_version,
            pipeline_id=f"rollback-{current_version}",
            stage="deployment",
            status="rollback",
            metadata={
                "rollback_to_version": rollback_to_version,
                "rollback_reason": reason,
                "automated": True
            },
            metrics=metrics_snapshot
        )
        
        # Critical priority for rollbacks
        await self._publish_event(event, priority="critical")
    
    async def publish_drift_detected(
        self,
        model_version: str,
        drift_score: float,
        drift_type: str,
        affected_features: List[str],
        recommended_action: str
    ):
        """Publish drift detection event."""
        event = MLOpsEvent(
            event_id=str(uuid.uuid4()),
            event_type=MLOpsEventType.DRIFT_DETECTED,
            timestamp=datetime.now(),
            model_version=model_version,
            pipeline_id="monitoring",
            stage="monitoring",
            status="alert",
            metadata={
                "drift_type": drift_type,
                "affected_features": affected_features,
                "recommended_action": recommended_action
            },
            metrics={
                "drift_score": drift_score,
                "confidence": 0.95
            }
        )
        
        # Priority based on drift score
        priority = "high" if drift_score > 0.5 else "normal"
        await self._publish_event(event, priority=priority)
    
    async def publish_performance_degradation(
        self,
        model_version: str,
        metric_name: str,
        current_value: float,
        baseline_value: float,
        degradation_percentage: float
    ):
        """Publish performance degradation event."""
        event = MLOpsEvent(
            event_id=str(uuid.uuid4()),
            event_type=MLOpsEventType.PERFORMANCE_DEGRADED,
            timestamp=datetime.now(),
            model_version=model_version,
            pipeline_id="monitoring",
            stage="monitoring",
            status="alert",
            metadata={
                "metric_name": metric_name,
                "degradation_percentage": degradation_percentage,
                "requires_investigation": degradation_percentage > 10
            },
            metrics={
                "current_value": current_value,
                "baseline_value": baseline_value,
                "degradation": degradation_percentage
            }
        )
        
        # Priority based on degradation
        priority = "high" if degradation_percentage > 20 else "normal"
        await self._publish_event(event, priority=priority)
    
    async def _publish_event(self, event: MLOpsEvent, priority: str = "normal"):
        """Publish a single event or add to buffer."""
        if priority == "critical":
            # Publish immediately for critical events
            await self._publish_single_event(event)
        else:
            # Add to buffer for batch publishing
            self.event_buffer.append(event)
            
            # Flush if buffer is full
            if len(self.event_buffer) >= self.buffer_size:
                await self._flush_events()
    
    async def _publish_single_event(self, event: MLOpsEvent):
        """Publish a single event to NATS."""
        with self.publish_latency.labels(
            event_type=event.event_type.value
        ).time():
            try:
                # Determine subject based on event type
                subject = self._get_subject_for_event(event)
                
                # Publish with acknowledgment
                ack = await self.js.publish(
                    subject,
                    event.to_json().encode(),
                    headers={
                        "event-id": event.event_id,
                        "event-type": event.event_type.value,
                        "model-version": event.model_version,
                        "priority": self._get_priority_for_event(event)
                    }
                )
                
                # Update metrics
                self.events_published.labels(
                    event_type=event.event_type.value,
                    stage=event.stage
                ).inc()
                
                logger.info(
                    f"Published {event.event_type.value} event: {event.event_id} "
                    f"(seq: {ack.seq})"
                )
                
            except Exception as e:
                logger.error(f"Failed to publish event {event.event_id}: {e}")
                self.publish_errors.labels(
                    event_type=event.event_type.value,
                    error_type=type(e).__name__
                ).inc()
                raise
    
    async def _flush_events(self):
        """Flush buffered events."""
        if not self.event_buffer:
            return
        
        events_to_publish = self.event_buffer.copy()
        self.event_buffer.clear()
        
        # Publish events concurrently
        tasks = [
            self._publish_single_event(event)
            for event in events_to_publish
        ]
        
        await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _flush_events_periodically(self):
        """Background task to flush events periodically."""
        while True:
            await asyncio.sleep(self.flush_interval)
            await self._flush_events()
    
    def _get_subject_for_event(self, event: MLOpsEvent) -> str:
        """Get NATS subject for event type."""
        subject_map = {
            "training": "chimera.training",
            "validation": "chimera.training",
            "deployment": "chimera.deployment",
            "monitoring": "chimera.monitoring"
        }
        
        base_subject = subject_map.get(event.stage, "chimera.mlops")
        return f"{base_subject}.{event.event_type.value}"
    
    def _get_priority_for_event(self, event: MLOpsEvent) -> str:
        """Determine priority for event."""
        high_priority_events = [
            MLOpsEventType.ROLLBACK_INITIATED,
            MLOpsEventType.DEPLOYMENT_FAILED,
            MLOpsEventType.VALIDATION_FAILED,
            MLOpsEventType.PERFORMANCE_DEGRADED
        ]
        
        if event.event_type in high_priority_events:
            return "high"
        
        # Check metrics for threshold breaches
        if event.metrics:
            if event.metrics.get("drift_score", 0) > 0.5:
                return "high"
            if event.metrics.get("degradation", 0) > 20:
                return "high"
        
        return "normal"
    
    async def get_event_stats(self) -> Dict[str, Any]:
        """Get event publishing statistics."""
        stream_info = await self.js.stream_info(self.stream_name)
        
        return {
            "stream_name": self.stream_name,
            "total_messages": stream_info.state.messages,
            "total_bytes": stream_info.state.bytes,
            "consumer_count": stream_info.state.consumer_count,
            "first_sequence": stream_info.state.first_seq,
            "last_sequence": stream_info.state.last_seq,
            "buffered_events": len(self.event_buffer)
        }


# Singleton instance
_publisher: Optional[MLOpsEventPublisher] = None


async def get_mlops_publisher() -> MLOpsEventPublisher:
    """Get or create the MLOps event publisher."""
    global _publisher
    
    if _publisher is None:
        _publisher = MLOpsEventPublisher()
        await _publisher.start()
    
    return _publisher


async def publish_mlops_event(event_type: str, **kwargs):
    """Convenience function to publish MLOps events."""
    publisher = await get_mlops_publisher()
    
    # Route to appropriate method based on event type
    method_map = {
        "training_started": publisher.publish_training_started,
        "training_completed": publisher.publish_training_completed,
        "validation_results": publisher.publish_validation_results,
        "deployment": publisher.publish_deployment_event,
        "rollback": publisher.publish_rollback_event,
        "drift_detected": publisher.publish_drift_detected,
        "performance_degraded": publisher.publish_performance_degradation
    }
    
    method = method_map.get(event_type)
    if method:
        await method(**kwargs)
    else:
        logger.warning(f"Unknown event type: {event_type}")