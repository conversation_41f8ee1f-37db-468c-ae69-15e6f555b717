"""
Chimera Training Pipeline

Implements the closed-loop MLOps pipeline for continuous learning with:
- Event-driven retraining triggered by NATS
- Safety constraints and validation
- Automated deployment with rollback
- A/B testing capabilities
"""

import asyncio
import json
import torch
import torch.nn as nn
from typing import Dict, List, Optional, Any, Callable, Tuple
from dataclasses import dataclass, field
from datetime import datetime
import numpy as np
from pathlib import Path
import tempfile
import shutil
import hashlib

import nats
from nats.aio.client import Client as NATS
from prometheus_client import Counter, Histogram, Gauge
from opentelemetry import trace
import mlflow
import optuna
from torch.utils.data import DataLoader, Dataset
import wandb

from ..models.adaptive_tvae import AdaptiveTVAE
from ..monitoring.metrics import ChimeraMetrics
from ..monitoring.tracing import ChimeraTracer
from ..utils.config import ChimeraConfig
from .monitor_agent import DriftDetectionResult


@dataclass
class TrainingConfig:
    """Configuration for training pipeline."""
    batch_size: int = 32
    learning_rate: float = 1e-4
    epochs: int = 100
    early_stopping_patience: int = 10
    validation_split: float = 0.2
    gradient_clip_value: float = 1.0
    
    # Safety constraints
    max_performance_degradation: float = 0.05
    min_validation_score: float = 0.85
    rollback_threshold: float = 0.1
    
    # A/B testing
    canary_deployment_ratio: float = 0.1
    canary_evaluation_period: int = 3600  # seconds
    
    # Hyperparameter optimization
    use_hyperopt: bool = True
    n_trials: int = 50
    
    # Model versioning
    model_registry_path: str = "/models/chimera"
    artifact_store_path: str = "/artifacts/chimera"


@dataclass
class TrainingResult:
    """Result of a training run."""
    model_version: str
    metrics: Dict[str, float]
    validation_score: float
    training_time: float
    hyperparameters: Dict[str, Any]
    safety_check_passed: bool
    deployment_ready: bool
    artifacts: Dict[str, str]


class ChimeraDataset(Dataset):
    """Dataset wrapper for Chimera training."""
    
    def __init__(self, data: np.ndarray, labels: Optional[np.ndarray] = None):
        self.data = torch.FloatTensor(data)
        self.labels = labels
        
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        if self.labels is not None:
            return self.data[idx], self.labels[idx]
        return self.data[idx]


class ChimeraTrainingPipeline:
    """
    Closed-loop MLOps pipeline for AdaptiveTVAE.
    
    Features:
    - Event-driven retraining via NATS
    - Automated hyperparameter optimization
    - Safety validation and rollback
    - A/B testing and canary deployments
    - Comprehensive monitoring and tracing
    """
    
    def __init__(
        self,
        config: ChimeraConfig,
        training_config: Optional[TrainingConfig] = None,
        nats_url: str = "nats://localhost:4222"
    ):
        self.config = config
        self.training_config = training_config or TrainingConfig()
        self.nats_url = nats_url
        
        # Initialize components
        self.metrics = ChimeraMetrics()
        self.tracer = ChimeraTracer()
        
        # NATS client
        self.nc: Optional[NATS] = None
        self.subscription = None
        
        # Model registry
        self.current_model: Optional[AdaptiveTVAE] = None
        self.model_versions: Dict[str, AdaptiveTVAE] = {}
        self.deployment_history: List[Dict[str, Any]] = []
        
        # Training state
        self.is_training = False
        self.training_queue = asyncio.Queue()
        
        # Initialize MLflow
        mlflow.set_tracking_uri("http://localhost:5000")
        mlflow.set_experiment("chimera_adaptive_tvae")
        
        # Initialize Weights & Biases
        wandb.init(
            project="chimera",
            config=self.training_config.__dict__,
            mode="offline"  # Change to "online" for cloud logging
        )
        
    async def start(self):
        """Start the training pipeline and connect to NATS."""
        with self.tracer.start_span("pipeline_start") as span:
            # Connect to NATS
            self.nc = await nats.connect(self.nats_url)
            
            # Subscribe to drift detection events
            self.subscription = await self.nc.subscribe(
                "chimera.drift_detected",
                cb=self._handle_drift_event
            )
            
            # Start training worker
            asyncio.create_task(self._training_worker())
            
            self.metrics.pipeline_status_gauge.set(1)
            print(f"🚀 Chimera Training Pipeline started and listening on NATS")
    
    async def stop(self):
        """Stop the training pipeline."""
        if self.subscription:
            await self.subscription.unsubscribe()
        if self.nc:
            await self.nc.close()
        
        self.metrics.pipeline_status_gauge.set(0)
    
    async def _handle_drift_event(self, msg):
        """Handle drift detection events from NATS."""
        with self.tracer.start_span("handle_drift_event") as span:
            try:
                # Parse drift event
                drift_data = json.loads(msg.data.decode())
                drift_result = DriftDetectionResult(**drift_data)
                
                span.set_attribute("drift_score", drift_result.drift_score)
                span.set_attribute("drift_type", drift_result.drift_type)
                
                # Check if retraining is needed
                if self._should_retrain(drift_result):
                    await self.training_queue.put(drift_result)
                    self.metrics.retraining_triggered_counter.inc()
                    
                    # Acknowledge message
                    await msg.ack()
                    
            except Exception as e:
                span.record_exception(e)
                self.metrics.error_counter.labels(
                    error_type="drift_event_handling"
                ).inc()
    
    def _should_retrain(self, drift_result: DriftDetectionResult) -> bool:
        """Determine if retraining is needed based on drift detection."""
        # Check drift score threshold
        if drift_result.drift_score < 0.3:
            return False
        
        # Check confidence
        if drift_result.confidence < 0.8:
            return False
        
        # Check time since last training
        if self.deployment_history:
            last_deployment = self.deployment_history[-1]
            time_since_last = (
                datetime.now() - datetime.fromisoformat(last_deployment["timestamp"])
            ).total_seconds()
            
            # Don't retrain too frequently
            if time_since_last < 3600:  # 1 hour
                return False
        
        return True
    
    async def _training_worker(self):
        """Worker that processes training requests."""
        while True:
            try:
                # Wait for drift event
                drift_result = await self.training_queue.get()
                
                if not self.is_training:
                    self.is_training = True
                    
                    # Run training
                    result = await self._run_training_pipeline(drift_result)
                    
                    # Deploy if successful
                    if result.deployment_ready:
                        await self._deploy_model(result)
                    
                    self.is_training = False
                    
            except Exception as e:
                self.metrics.error_counter.labels(
                    error_type="training_worker"
                ).inc()
                self.is_training = False
    
    async def _run_training_pipeline(
        self,
        drift_result: DriftDetectionResult
    ) -> TrainingResult:
        """Run the full training pipeline."""
        with self.tracer.start_span("training_pipeline") as span:
            start_time = datetime.now()
            
            # Start MLflow run
            with mlflow.start_run() as run:
                # Log drift information
                mlflow.log_params({
                    "drift_score": drift_result.drift_score,
                    "drift_type": drift_result.drift_type,
                    "trigger": "automated"
                })
                
                # Load training data
                train_loader, val_loader = await self._prepare_data()
                
                # Hyperparameter optimization
                if self.training_config.use_hyperopt:
                    best_params = await self._optimize_hyperparameters(
                        train_loader, val_loader
                    )
                    mlflow.log_params(best_params)
                else:
                    best_params = self._get_default_hyperparameters()
                
                # Train model
                model, metrics = await self._train_model(
                    train_loader, val_loader, best_params
                )
                
                # Validate model
                validation_score = await self._validate_model(model, val_loader)
                
                # Safety checks
                safety_passed = await self._run_safety_checks(model, validation_score)
                
                # Generate model version
                model_version = self._generate_model_version()
                
                # Save artifacts
                artifacts = await self._save_artifacts(model, model_version)
                
                # Log to MLflow
                mlflow.log_metrics(metrics)
                mlflow.log_metric("validation_score", validation_score)
                mlflow.log_metric("safety_check_passed", float(safety_passed))
                
                # Create result
                result = TrainingResult(
                    model_version=model_version,
                    metrics=metrics,
                    validation_score=validation_score,
                    training_time=(datetime.now() - start_time).total_seconds(),
                    hyperparameters=best_params,
                    safety_check_passed=safety_passed,
                    deployment_ready=safety_passed and validation_score > self.training_config.min_validation_score,
                    artifacts=artifacts
                )
                
                # Update metrics
                self.metrics.training_duration_histogram.observe(result.training_time)
                self.metrics.model_version_counter.labels(
                    version=model_version
                ).inc()
                
                return result
    
    async def _prepare_data(self) -> Tuple[DataLoader, DataLoader]:
        """Prepare training and validation data."""
        # This is a placeholder - in production, this would:
        # 1. Query recent data from the data store
        # 2. Apply preprocessing
        # 3. Handle class imbalance
        # 4. Create train/val split
        
        # For now, create synthetic data
        n_samples = 10000
        input_dim = self.config.encoder_config.latent_dim
        
        # Normal data (majority)
        normal_data = np.random.randn(int(n_samples * 0.9), input_dim)
        
        # Anomalous data (minority)
        anomaly_data = np.random.randn(int(n_samples * 0.1), input_dim) * 3 + 5
        
        # Combine
        all_data = np.vstack([normal_data, anomaly_data])
        labels = np.hstack([
            np.zeros(len(normal_data)),
            np.ones(len(anomaly_data))
        ])
        
        # Shuffle
        indices = np.random.permutation(len(all_data))
        all_data = all_data[indices]
        labels = labels[indices]
        
        # Split
        split_idx = int(len(all_data) * (1 - self.training_config.validation_split))
        train_data = all_data[:split_idx]
        val_data = all_data[split_idx:]
        
        # Create datasets
        train_dataset = ChimeraDataset(train_data)
        val_dataset = ChimeraDataset(val_data)
        
        # Create loaders
        train_loader = DataLoader(
            train_dataset,
            batch_size=self.training_config.batch_size,
            shuffle=True,
            num_workers=4
        )
        
        val_loader = DataLoader(
            val_dataset,
            batch_size=self.training_config.batch_size,
            shuffle=False,
            num_workers=4
        )
        
        return train_loader, val_loader
    
    async def _optimize_hyperparameters(
        self,
        train_loader: DataLoader,
        val_loader: DataLoader
    ) -> Dict[str, Any]:
        """Optimize hyperparameters using Optuna."""
        
        def objective(trial):
            # Suggest hyperparameters
            params = {
                "learning_rate": trial.suggest_loguniform("learning_rate", 1e-5, 1e-2),
                "batch_size": trial.suggest_categorical("batch_size", [16, 32, 64, 128]),
                "latent_dim": trial.suggest_int("latent_dim", 16, 64),
                "beta": trial.suggest_loguniform("beta", 0.1, 10.0),
                "architecture_search_budget": trial.suggest_int("architecture_search_budget", 50, 200),
            }
            
            # Train small model
            model = AdaptiveTVAE(self.config)
            optimizer = torch.optim.Adam(model.parameters(), lr=params["learning_rate"])
            
            # Quick training
            for epoch in range(10):
                train_loss = 0
                for batch in train_loader:
                    optimizer.zero_grad()
                    outputs = model(batch)
                    loss = outputs["loss"]
                    loss.backward()
                    optimizer.step()
                    train_loss += loss.item()
                
                if epoch == 9:  # Last epoch
                    val_loss = 0
                    with torch.no_grad():
                        for batch in val_loader:
                            outputs = model(batch)
                            val_loss += outputs["loss"].item()
                    
                    return val_loss / len(val_loader)
            
            return float('inf')
        
        # Create study
        study = optuna.create_study(direction="minimize")
        study.optimize(objective, n_trials=self.training_config.n_trials)
        
        return study.best_params
    
    def _get_default_hyperparameters(self) -> Dict[str, Any]:
        """Get default hyperparameters."""
        return {
            "learning_rate": self.training_config.learning_rate,
            "batch_size": self.training_config.batch_size,
            "latent_dim": self.config.encoder_config.latent_dim,
            "beta": 1.0,
            "architecture_search_budget": self.config.encoder_config.search_budget,
        }
    
    async def _train_model(
        self,
        train_loader: DataLoader,
        val_loader: DataLoader,
        hyperparameters: Dict[str, Any]
    ) -> Tuple[AdaptiveTVAE, Dict[str, float]]:
        """Train the AdaptiveTVAE model."""
        # Create model
        model = AdaptiveTVAE(self.config)
        optimizer = torch.optim.Adam(
            model.parameters(),
            lr=hyperparameters["learning_rate"]
        )
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, patience=5, factor=0.5
        )
        
        # Training metrics
        metrics = {
            "final_train_loss": 0,
            "final_val_loss": 0,
            "best_val_loss": float('inf'),
            "epochs_trained": 0,
        }
        
        # Early stopping
        best_val_loss = float('inf')
        patience_counter = 0
        
        # Training loop
        for epoch in range(self.training_config.epochs):
            # Training phase
            model.train()
            train_loss = 0
            
            for batch in train_loader:
                optimizer.zero_grad()
                outputs = model(batch)
                loss = outputs["loss"]
                
                # Gradient clipping
                torch.nn.utils.clip_grad_norm_(
                    model.parameters(),
                    self.training_config.gradient_clip_value
                )
                
                loss.backward()
                optimizer.step()
                train_loss += loss.item()
                
                # Log to wandb
                wandb.log({
                    "train_loss": loss.item(),
                    "learning_rate": optimizer.param_groups[0]['lr']
                })
            
            # Validation phase
            model.eval()
            val_loss = 0
            
            with torch.no_grad():
                for batch in val_loader:
                    outputs = model(batch)
                    val_loss += outputs["loss"].item()
            
            avg_train_loss = train_loss / len(train_loader)
            avg_val_loss = val_loss / len(val_loader)
            
            # Update scheduler
            scheduler.step(avg_val_loss)
            
            # Early stopping
            if avg_val_loss < best_val_loss:
                best_val_loss = avg_val_loss
                patience_counter = 0
                # Save best model
                best_model_state = model.state_dict()
            else:
                patience_counter += 1
                
            if patience_counter >= self.training_config.early_stopping_patience:
                print(f"Early stopping at epoch {epoch}")
                break
            
            # Log epoch metrics
            wandb.log({
                "epoch": epoch,
                "avg_train_loss": avg_train_loss,
                "avg_val_loss": avg_val_loss
            })
            
            metrics["epochs_trained"] = epoch + 1
        
        # Load best model
        model.load_state_dict(best_model_state)
        
        # Final metrics
        metrics["final_train_loss"] = avg_train_loss
        metrics["final_val_loss"] = avg_val_loss
        metrics["best_val_loss"] = best_val_loss
        
        return model, metrics
    
    async def _validate_model(
        self,
        model: AdaptiveTVAE,
        val_loader: DataLoader
    ) -> float:
        """Validate model performance."""
        model.eval()
        
        total_score = 0
        total_samples = 0
        
        with torch.no_grad():
            for batch in val_loader:
                outputs = model(batch)
                
                # Compute validation score (inverse of loss)
                score = 1.0 / (1.0 + outputs["loss"].item())
                total_score += score * batch.shape[0]
                total_samples += batch.shape[0]
        
        return total_score / total_samples
    
    async def _run_safety_checks(
        self,
        model: AdaptiveTVAE,
        validation_score: float
    ) -> bool:
        """Run safety checks on the trained model."""
        # Check minimum validation score
        if validation_score < self.training_config.min_validation_score:
            return False
        
        # Check for NaN/Inf in model parameters
        for param in model.parameters():
            if torch.isnan(param).any() or torch.isinf(param).any():
                return False
        
        # Check model size constraints
        model_size = sum(p.numel() for p in model.parameters())
        if model_size > 100_000_000:  # 100M parameters max
            return False
        
        # If we have a current model, check performance degradation
        if self.current_model is not None:
            # Run comparative evaluation
            # This is simplified - in production would do proper A/B testing
            performance_ratio = validation_score / 0.9  # Placeholder
            
            if performance_ratio < (1 - self.training_config.max_performance_degradation):
                return False
        
        return True
    
    def _generate_model_version(self) -> str:
        """Generate unique model version identifier."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        random_suffix = hashlib.md5(
            str(np.random.random()).encode()
        ).hexdigest()[:8]
        
        return f"chimera_v{timestamp}_{random_suffix}"
    
    async def _save_artifacts(
        self,
        model: AdaptiveTVAE,
        model_version: str
    ) -> Dict[str, str]:
        """Save model artifacts."""
        artifacts = {}
        
        # Create artifact directory
        artifact_dir = Path(self.training_config.artifact_store_path) / model_version
        artifact_dir.mkdir(parents=True, exist_ok=True)
        
        # Save model state
        model_path = artifact_dir / "model.pt"
        torch.save({
            "model_state_dict": model.state_dict(),
            "config": self.config,
            "version": model_version,
            "timestamp": datetime.now().isoformat()
        }, model_path)
        artifacts["model"] = str(model_path)
        
        # Save model architecture
        architecture_path = artifact_dir / "architecture.json"
        with open(architecture_path, 'w') as f:
            json.dump(model.get_architecture_config(), f, indent=2)
        artifacts["architecture"] = str(architecture_path)
        
        # Log to MLflow
        mlflow.pytorch.log_model(model, "model")
        mlflow.log_artifacts(str(artifact_dir))
        
        return artifacts
    
    async def _deploy_model(self, result: TrainingResult):
        """Deploy the trained model."""
        with self.tracer.start_span("model_deployment") as span:
            span.set_attribute("model_version", result.model_version)
            
            # Load model
            model_data = torch.load(result.artifacts["model"])
            model = AdaptiveTVAE(model_data["config"])
            model.load_state_dict(model_data["model_state_dict"])
            
            # Store in registry
            self.model_versions[result.model_version] = model
            
            # Canary deployment
            if self.current_model is not None:
                # Deploy as canary first
                await self._run_canary_deployment(model, result.model_version)
            else:
                # First deployment - full rollout
                self.current_model = model
                
            # Update deployment history
            self.deployment_history.append({
                "version": result.model_version,
                "timestamp": datetime.now().isoformat(),
                "metrics": result.metrics,
                "validation_score": result.validation_score,
                "deployment_type": "canary" if self.current_model else "full"
            })
            
            # Publish deployment event
            if self.nc:
                await self.nc.publish(
                    "chimera.model_deployed",
                    json.dumps({
                        "version": result.model_version,
                        "timestamp": datetime.now().isoformat()
                    }).encode()
                )
            
            self.metrics.model_deployments_counter.labels(
                version=result.model_version
            ).inc()
    
    async def _run_canary_deployment(
        self,
        new_model: AdaptiveTVAE,
        model_version: str
    ):
        """Run canary deployment with gradual rollout."""
        # This is a simplified version
        # In production would:
        # 1. Route traffic based on canary ratio
        # 2. Monitor performance metrics
        # 3. Gradually increase traffic
        # 4. Rollback if issues detected
        
        print(f"🐤 Starting canary deployment for {model_version}")
        
        # Simulate canary period
        await asyncio.sleep(10)  # In production: self.training_config.canary_evaluation_period
        
        # Check canary metrics (simplified)
        canary_success = np.random.random() > 0.1  # 90% success rate
        
        if canary_success:
            print(f"✅ Canary deployment successful, promoting {model_version}")
            self.current_model = new_model
        else:
            print(f"❌ Canary deployment failed, rolling back")
            self.metrics.rollback_counter.inc()
    
    def get_current_model(self) -> Optional[AdaptiveTVAE]:
        """Get the currently deployed model."""
        return self.current_model
    
    def get_model_version(self, version: str) -> Optional[AdaptiveTVAE]:
        """Get a specific model version."""
        return self.model_versions.get(version)
    
    def get_deployment_history(self) -> List[Dict[str, Any]]:
        """Get deployment history."""
        return self.deployment_history