"""
🔐 AURA Intelligence Authentication Middleware
JWT-based authentication and authorization
"""

import time
from typing import Optional

from fastapi import Request, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response
import jwt
import structlog

from src.core.config import get_settings

logger = structlog.get_logger("aura.auth")
security = HTTPBearer()


class AuthMiddleware(BaseHTTPMiddleware):
    """Authentication middleware for API requests"""
    
    def __init__(self, app):
        super().__init__(app)
        self.settings = get_settings()
        
        # Public endpoints that don't require authentication
        self.public_endpoints = {
            "/",
            "/docs",
            "/redoc",
            "/openapi.json",
            "/api/v1/health",
            "/api/v1/health/liveness",
            "/api/v1/health/readiness",
            "/metrics"
        }
    
    async def dispatch(self, request: Request, call_next):
        """Process authentication for incoming requests"""
        
        # Skip authentication for public endpoints
        if request.url.path in self.public_endpoints:
            return await call_next(request)
        
        # Skip authentication in development mode
        if self.settings.debug:
            return await call_next(request)
        
        try:
            # Extract and validate JWT token
            token = self._extract_token(request)
            if token:
                user_info = self._validate_token(token)
                request.state.user = user_info
            else:
                # No token provided
                return self._unauthorized_response()
            
            response = await call_next(request)
            return response
            
        except HTTPException as e:
            return self._error_response(e.status_code, e.detail)
        except Exception as e:
            logger.error("Authentication error", error=str(e))
            return self._error_response(500, "Internal authentication error")
    
    def _extract_token(self, request: Request) -> Optional[str]:
        """Extract JWT token from request headers"""
        
        # Try Authorization header first
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            return auth_header[7:]  # Remove "Bearer " prefix
        
        # Try query parameter (for WebSocket connections)
        token = request.query_params.get("token")
        if token:
            return token
        
        return None
    
    def _validate_token(self, token: str) -> dict:
        """Validate JWT token and extract user information"""
        
        try:
            # Decode JWT token
            payload = jwt.decode(
                token,
                self.settings.secret_key,
                algorithms=[self.settings.algorithm]
            )
            
            # Check expiration
            if payload.get("exp", 0) < time.time():
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Token has expired"
                )
            
            # Extract user information
            user_info = {
                "user_id": payload.get("sub"),
                "username": payload.get("username"),
                "roles": payload.get("roles", []),
                "permissions": payload.get("permissions", []),
                "exp": payload.get("exp")
            }
            
            logger.info("User authenticated", 
                       user_id=user_info["user_id"],
                       username=user_info["username"])
            
            return user_info
            
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token has expired"
            )
        except jwt.InvalidTokenError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token"
            )
    
    def _unauthorized_response(self) -> Response:
        """Return unauthorized response"""
        return Response(
            content='{"detail": "Authentication required"}',
            status_code=401,
            headers={"Content-Type": "application/json"}
        )
    
    def _error_response(self, status_code: int, detail: str) -> Response:
        """Return error response"""
        return Response(
            content=f'{{"detail": "{detail}"}}',
            status_code=status_code,
            headers={"Content-Type": "application/json"}
        )


def create_access_token(user_data: dict) -> str:
    """Create JWT access token for user"""
    
    settings = get_settings()
    
    # Token payload
    payload = {
        "sub": user_data["user_id"],
        "username": user_data["username"],
        "roles": user_data.get("roles", []),
        "permissions": user_data.get("permissions", []),
        "iat": int(time.time()),
        "exp": int(time.time()) + (settings.access_token_expire_minutes * 60)
    }
    
    # Create token
    token = jwt.encode(payload, settings.secret_key, algorithm=settings.algorithm)
    
    logger.info("Access token created", 
               user_id=user_data["user_id"],
               username=user_data["username"])
    
    return token


def verify_permission(required_permission: str):
    """Decorator to verify user has required permission"""
    
    def decorator(func):
        async def wrapper(request: Request, *args, **kwargs):
            user = getattr(request.state, 'user', None)
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            
            permissions = user.get("permissions", [])
            if required_permission not in permissions and "admin" not in user.get("roles", []):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Permission '{required_permission}' required"
                )
            
            return await func(request, *args, **kwargs)
        return wrapper
    return decorator


def verify_role(required_role: str):
    """Decorator to verify user has required role"""
    
    def decorator(func):
        async def wrapper(request: Request, *args, **kwargs):
            user = getattr(request.state, 'user', None)
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            
            roles = user.get("roles", [])
            if required_role not in roles and "admin" not in roles:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Role '{required_role}' required"
                )
            
            return await func(request, *args, **kwargs)
        return wrapper
    return decorator
