"""
📊 AURA Intelligence Monitoring Middleware
Request tracking, metrics collection, and performance monitoring
"""

import time
import uuid
from typing import Dict, Any

from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response
from prometheus_client import Counter, Histogram, Gauge
import structlog

from src.core.config import get_settings

logger = structlog.get_logger("aura.monitoring")

# Prometheus metrics
REQUEST_COUNT = Counter(
    'aura_http_requests_total',
    'Total HTTP requests',
    ['method', 'endpoint', 'status_code']
)

REQUEST_DURATION = Histogram(
    'aura_http_request_duration_seconds',
    'HTTP request duration in seconds',
    ['method', 'endpoint']
)

ACTIVE_REQUESTS = Gauge(
    'aura_http_requests_active',
    'Number of active HTTP requests'
)

ERROR_COUNT = Counter(
    'aura_http_errors_total',
    'Total HTTP errors',
    ['method', 'endpoint', 'error_type']
)


class MonitoringMiddleware(BaseHTTPMiddleware):
    """Monitoring middleware for request tracking and metrics"""
    
    def __init__(self, app):
        super().__init__(app)
        self.settings = get_settings()
    
    async def dispatch(self, request: Request, call_next):
        """Process request monitoring"""
        
        # Generate request ID
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id
        
        # Start timing
        start_time = time.time()
        
        # Increment active requests
        ACTIVE_REQUESTS.inc()
        
        # Extract request info
        method = request.method
        path = request.url.path
        endpoint = self._normalize_endpoint(path)
        
        # Log request start
        logger.info("Request started",
                   request_id=request_id,
                   method=method,
                   path=path,
                   client_ip=self._get_client_ip(request),
                   user_agent=request.headers.get("user-agent"))
        
        try:
            # Process request
            response = await call_next(request)
            
            # Calculate duration
            duration = time.time() - start_time
            status_code = response.status_code
            
            # Record metrics
            REQUEST_COUNT.labels(
                method=method,
                endpoint=endpoint,
                status_code=status_code
            ).inc()
            
            REQUEST_DURATION.labels(
                method=method,
                endpoint=endpoint
            ).observe(duration)
            
            # Log successful request
            logger.info("Request completed",
                       request_id=request_id,
                       method=method,
                       path=path,
                       status_code=status_code,
                       duration=duration,
                       response_size=self._get_response_size(response))
            
            # Add monitoring headers
            response.headers["X-Request-ID"] = request_id
            response.headers["X-Response-Time"] = f"{duration:.3f}s"
            
            return response
            
        except Exception as e:
            # Calculate duration for failed requests
            duration = time.time() - start_time
            
            # Record error metrics
            ERROR_COUNT.labels(
                method=method,
                endpoint=endpoint,
                error_type=type(e).__name__
            ).inc()
            
            REQUEST_COUNT.labels(
                method=method,
                endpoint=endpoint,
                status_code=500
            ).inc()
            
            # Log error
            logger.error("Request failed",
                        request_id=request_id,
                        method=method,
                        path=path,
                        duration=duration,
                        error=str(e),
                        error_type=type(e).__name__)
            
            # Re-raise the exception
            raise
            
        finally:
            # Decrement active requests
            ACTIVE_REQUESTS.dec()
    
    def _normalize_endpoint(self, path: str) -> str:
        """Normalize endpoint path for metrics"""
        
        # Remove query parameters
        if '?' in path:
            path = path.split('?')[0]
        
        # Normalize common patterns
        path_parts = path.split('/')
        normalized_parts = []
        
        for part in path_parts:
            # Replace UUIDs and IDs with placeholder
            if self._looks_like_id(part):
                normalized_parts.append('{id}')
            else:
                normalized_parts.append(part)
        
        return '/'.join(normalized_parts)
    
    def _looks_like_id(self, part: str) -> bool:
        """Check if path part looks like an ID"""
        
        # UUID pattern
        if len(part) == 36 and part.count('-') == 4:
            return True
        
        # Numeric ID
        if part.isdigit():
            return True
        
        # Other ID patterns
        if len(part) > 10 and any(c.isdigit() for c in part):
            return True
        
        return False
    
    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP address"""
        
        # Check for forwarded headers (load balancer/proxy)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(',')[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fallback to direct connection
        if hasattr(request, 'client') and request.client:
            return request.client.host
        
        return "unknown"
    
    def _get_response_size(self, response: Response) -> int:
        """Get response size in bytes"""
        
        content_length = response.headers.get("content-length")
        if content_length:
            try:
                return int(content_length)
            except ValueError:
                pass
        
        # Estimate size if not available
        if hasattr(response, 'body'):
            return len(response.body)
        
        return 0


class PerformanceTracker:
    """Track performance metrics for specific operations"""
    
    def __init__(self, operation_name: str):
        self.operation_name = operation_name
        self.start_time = None
        self.metrics: Dict[str, Any] = {}
    
    def __enter__(self):
        self.start_time = time.time()
        logger.debug("Operation started", operation=self.operation_name)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            duration = time.time() - self.start_time
            self.metrics['duration'] = duration
            
            if exc_type:
                logger.error("Operation failed",
                           operation=self.operation_name,
                           duration=duration,
                           error=str(exc_val))
            else:
                logger.info("Operation completed",
                          operation=self.operation_name,
                          duration=duration,
                          **self.metrics)
    
    def add_metric(self, key: str, value: Any):
        """Add custom metric to track"""
        self.metrics[key] = value


def track_performance(operation_name: str):
    """Decorator to track function performance"""
    
    def decorator(func):
        async def async_wrapper(*args, **kwargs):
            with PerformanceTracker(operation_name) as tracker:
                result = await func(*args, **kwargs)
                
                # Add result metrics if available
                if isinstance(result, dict) and 'metrics' in result:
                    for key, value in result['metrics'].items():
                        tracker.add_metric(key, value)
                
                return result
        
        def sync_wrapper(*args, **kwargs):
            with PerformanceTracker(operation_name) as tracker:
                result = func(*args, **kwargs)
                
                # Add result metrics if available
                if isinstance(result, dict) and 'metrics' in result:
                    for key, value in result['metrics'].items():
                        tracker.add_metric(key, value)
                
                return result
        
        # Return appropriate wrapper based on function type
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


# Custom metrics for AURA Intelligence
TOPOLOGY_ANALYSIS_DURATION = Histogram(
    'aura_topology_analysis_duration_seconds',
    'Time spent on topology analysis'
)

KNOWLEDGE_GRAPH_QUERIES = Counter(
    'aura_knowledge_graph_queries_total',
    'Total knowledge graph queries',
    ['query_type', 'status']
)

AGENT_EXECUTIONS = Counter(
    'aura_agent_executions_total',
    'Total agent executions',
    ['agent_type', 'status']
)

PREDICTION_ACCURACY = Gauge(
    'aura_prediction_accuracy',
    'Current prediction model accuracy'
)

INCIDENT_PROCESSING_TIME = Histogram(
    'aura_incident_processing_seconds',
    'Time to process incidents',
    ['incident_type', 'severity']
)
