"""
🧠 AURA Intelligence Enhanced Consciousness Core
Smart integration of best libraries while preserving our existing architecture and context
"""

from typing import Dict, Any, Optional, List
from datetime import datetime
import asyncio
import uuid

import structlog
from pydantic import BaseModel

# Import our existing models and brain integration (PRESERVE CONTEXT)
from src.models.agents import AgentType, IncidentAnalysis, ConsciousnessState
from src.models.topology import TopologyFeatures, TopologyPrediction
from src.core.brain_integration import brain_integration
from src.core.connection_manager import ConnectionManager

# Enhanced libraries (optional imports - graceful degradation)
try:
    from cognee import add, search, cognify
    COGNEE_AVAILABLE = True
except ImportError:
    COGNEE_AVAILABLE = False

try:
    from mem0 import MemoryClient
    MEM0_AVAILABLE = True
except ImportError:
    MEM0_AVAILABLE = False

import redis.asyncio as redis

logger = structlog.get_logger("aura.enhanced_consciousness")


class EnhancedConsciousnessCore:
    """
    Enhanced consciousness core that ADDS capabilities to our existing system:
    - Preserves all existing schemas and models
    - Enhances with Cognee for advanced memory (if available)
    - Adds Mem0 for context preservation (if available)
    - Maintains compatibility with our brain integration
    - Keeps our connection manager and agent orchestration
    """
    
    def __init__(self, 
                 connection_manager: ConnectionManager,
                 redis_client: Optional[redis.Redis] = None):
        self.logger = logger
        self.connection_manager = connection_manager
        self.redis = redis_client
        
        # Preserve our existing state management
        self.state = ConsciousnessState.AWARE
        self.attention_focus: Optional[str] = None
        self.context: Dict[str, Any] = {}
        
        # Enhanced memory (optional)
        self.mem0_client = MemoryClient() if MEM0_AVAILABLE else None
        self.cognee_initialized = False
        
        # Performance tracking (preserve existing metrics)
        self.metrics = {
            "decisions_made": 0,
            "successful_predictions": 0,
            "context_switches": 0,
            "enhanced_operations": 0
        }
    
    async def initialize(self):
        """Initialize with graceful enhancement"""
        try:
            self.logger.info("🧠 Initializing Enhanced Consciousness")
            
            # Initialize Cognee if available
            if COGNEE_AVAILABLE:
                await cognify()
                self.cognee_initialized = True
                self.logger.info("✅ Cognee memory enhancement enabled")
            
            # Load existing context
            await self._load_context()
            
            self.logger.info("✅ Enhanced Consciousness initialized")
            
        except Exception as e:
            self.logger.error("❌ Enhanced Consciousness init failed", error=str(e))
            # Graceful fallback - system still works without enhancements
            self.logger.info("🔄 Falling back to base consciousness system")
    
    async def analyze_incident_with_consciousness(self, 
                                                incident_data: Dict[str, Any]) -> IncidentAnalysis:
        """
        Enhanced incident analysis using our existing brain + optional enhancements
        """
        try:
            # Use our existing brain integration (PRESERVE FUNCTIONALITY)
            brain_analysis = await brain_integration.perform_causal_inference(incident_data)
            
            # Enhance with Cognee memory search if available
            relevant_context = []
            if self.cognee_initialized:
                try:
                    relevant_context = await search(str(incident_data))
                    self.metrics["enhanced_operations"] += 1
                except Exception as e:
                    self.logger.warning("Cognee search failed, using base system", error=str(e))
            
            # Store in Mem0 if available
            if self.mem0_client:
                try:
                    await self.mem0_client.add(
                        messages=[{"role": "system", "content": str(incident_data)}],
                        user_id="consciousness_core"
                    )
                except Exception as e:
                    self.logger.warning("Mem0 storage failed", error=str(e))
            
            # Create enhanced analysis using our existing schema
            analysis = IncidentAnalysis(
                analysis_id=f"enhanced_{uuid.uuid4().hex[:8]}",
                incident_id=incident_data.get("id", "unknown"),
                incident_type=brain_analysis.get("most_likely_cause", "unknown"),
                severity="high" if brain_analysis.get("overall_confidence", 0) > 0.8 else "medium",
                priority_score=brain_analysis.get("overall_confidence", 0.5),
                root_causes=brain_analysis.get("root_causes", []),
                recommended_actions=self._generate_recommendations(brain_analysis),
                confidence=brain_analysis.get("overall_confidence", 0.5),
                analysis_timestamp=datetime.utcnow()
            )
            
            # Enhanced context if available
            if relevant_context:
                analysis.context_state = {"enhanced_context": len(relevant_context)}
            
            self.metrics["decisions_made"] += 1
            
            self.logger.info("🎯 Enhanced incident analysis completed",
                           analysis_id=analysis.analysis_id,
                           confidence=analysis.confidence,
                           enhanced=bool(relevant_context))
            
            return analysis
            
        except Exception as e:
            self.logger.error("❌ Enhanced analysis failed", error=str(e))
            # Fallback to base brain integration
            return await self._fallback_analysis(incident_data)
    
    async def predict_with_consciousness(self, 
                                       topology_features: TopologyFeatures) -> TopologyPrediction:
        """
        Enhanced prediction using our existing models + consciousness
        """
        try:
            # Use our existing brain integration
            prediction_result = await brain_integration.predict_failure(topology_features.dict())
            
            # Enhance with consciousness context
            consciousness_context = await self._get_consciousness_context()
            
            # Create prediction using our existing schema
            prediction = TopologyPrediction(
                prediction_id=f"conscious_{uuid.uuid4().hex[:8]}",
                snapshot_id=topology_features.snapshot_id,
                failure_probability=prediction_result.get("failure_probability", 0.0),
                time_to_failure=prediction_result.get("time_to_failure"),
                confidence=prediction_result.get("confidence", 0.5),
                predicted_failure_type=prediction_result.get("predicted_failure_type"),
                affected_services=prediction_result.get("affected_services", []),
                impact_score=prediction_result.get("impact_score", 0.0),
                warning_indicators=prediction_result.get("warning_indicators", []),
                critical_features=self._extract_critical_features(topology_features),
                prediction_timestamp=datetime.utcnow(),
                prediction_horizon=300  # 5 minutes
            )
            
            # Store prediction context
            await self._store_prediction_context(prediction, consciousness_context)
            
            self.metrics["successful_predictions"] += 1
            
            return prediction
            
        except Exception as e:
            self.logger.error("❌ Conscious prediction failed", error=str(e))
            raise
    
    async def focus_attention(self, target: str, context: Dict[str, Any] = None):
        """Enhanced attention focusing with memory"""
        self.attention_focus = target
        if context:
            self.context.update(context)
        
        # Store in enhanced memory if available
        if self.cognee_initialized:
            try:
                await add(f"attention_focus: {target}", context or {})
            except Exception as e:
                self.logger.warning("Cognee attention storage failed", error=str(e))
        
        # Notify connection manager
        await self.connection_manager.add_to_memory(
            "consciousness_core",
            {"event": "attention_focus", "target": target, "context": context}
        )
        
        self.logger.info("👁️ Enhanced attention focused", target=target)
    
    def _generate_recommendations(self, brain_analysis: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on brain analysis"""
        recommendations = []
        
        root_causes = brain_analysis.get("root_causes", [])
        confidence = brain_analysis.get("overall_confidence", 0.0)
        
        if "database_connection_pool" in root_causes:
            recommendations.append("Scale database connection pool")
        if "memory_leak" in root_causes:
            recommendations.append("Restart affected services")
        if "resource_exhaustion" in root_causes:
            recommendations.append("Scale up resources")
        
        if confidence > 0.8:
            recommendations.append("Execute automated remediation")
        else:
            recommendations.append("Manual investigation required")
        
        return recommendations
    
    def _extract_critical_features(self, topology_features: TopologyFeatures) -> List[str]:
        """Extract critical features from topology"""
        critical = []
        
        if topology_features.complexity_score > 2.0:
            critical.append("high_complexity")
        if topology_features.betti_numbers.beta_1 > 3:
            critical.append("excessive_loops")
        if topology_features.stability_score < 0.7:
            critical.append("topological_instability")
        
        return critical
    
    async def _get_consciousness_context(self) -> Dict[str, Any]:
        """Get current consciousness context"""
        return {
            "state": self.state.value if hasattr(self.state, 'value') else str(self.state),
            "attention_focus": self.attention_focus,
            "context_size": len(self.context),
            "enhanced_available": {
                "cognee": self.cognee_initialized,
                "mem0": self.mem0_client is not None
            }
        }
    
    async def _store_prediction_context(self, 
                                      prediction: TopologyPrediction,
                                      context: Dict[str, Any]):
        """Store prediction context for learning"""
        if self.redis:
            try:
                await self.redis.setex(
                    f"prediction_context:{prediction.prediction_id}",
                    3600,  # 1 hour
                    str({"prediction": prediction.dict(), "context": context})
                )
            except Exception as e:
                self.logger.warning("Redis context storage failed", error=str(e))
    
    async def _load_context(self):
        """Load existing context"""
        if self.mem0_client:
            try:
                memories = await self.mem0_client.get_all(user_id="consciousness_core")
                if memories:
                    self.context["persistent_memories"] = len(memories)
                    self.logger.info("📥 Context loaded", memories=len(memories))
            except Exception as e:
                self.logger.warning("Context loading failed", error=str(e))
    
    async def _fallback_analysis(self, incident_data: Dict[str, Any]) -> IncidentAnalysis:
        """Fallback analysis using base system"""
        return IncidentAnalysis(
            analysis_id=f"fallback_{uuid.uuid4().hex[:8]}",
            incident_id=incident_data.get("id", "unknown"),
            incident_type="unknown",
            severity="medium",
            priority_score=0.5,
            root_causes=["analysis_failed"],
            recommended_actions=["manual_investigation"],
            confidence=0.3,
            analysis_timestamp=datetime.utcnow()
        )
    
    async def get_status(self) -> Dict[str, Any]:
        """Get enhanced consciousness status"""
        return {
            "state": self.state.value if hasattr(self.state, 'value') else str(self.state),
            "attention_focus": self.attention_focus,
            "context_size": len(self.context),
            "enhancements": {
                "cognee_available": COGNEE_AVAILABLE,
                "cognee_initialized": self.cognee_initialized,
                "mem0_available": MEM0_AVAILABLE,
                "mem0_client": self.mem0_client is not None
            },
            "metrics": self.metrics,
            "timestamp": datetime.utcnow().isoformat()
        }
    
    async def cleanup(self):
        """Cleanup with enhanced features"""
        # Save context if enhanced memory available
        if self.cognee_initialized:
            try:
                from cognee import prune
                await prune()
            except Exception as e:
                self.logger.warning("Cognee cleanup failed", error=str(e))
        
        self.logger.info("🧹 Enhanced Consciousness cleaned up")


# Factory function for easy integration
async def create_enhanced_consciousness(connection_manager: ConnectionManager,
                                      redis_client: Optional[redis.Redis] = None) -> EnhancedConsciousnessCore:
    """Create and initialize enhanced consciousness"""
    consciousness = EnhancedConsciousnessCore(connection_manager, redis_client)
    await consciousness.initialize()
    return consciousness
