"""
⚡ AURA Intelligence Event Mesh
Smart event-driven architecture using best libraries while preserving our context
"""

from typing import Dict, Any, Optional, List, Callable, Union
from datetime import datetime
import asyncio
import uuid
import json
from dataclasses import dataclass, field
from enum import Enum

import structlog

# Import our existing models (PRESERVE CONTEXT)
from src.models.topology import TopologySnapshot, TopologyFeatures
from src.models.agents import AgentType, IncidentAnalysis
from src.core.connection_manager import Message, ConnectionManager

# Enhanced event streaming (optional imports - graceful degradation)
try:
    import pulsar  # Apache Pulsar for advanced streaming
    PULSAR_AVAILABLE = True
except ImportError:
    PULSAR_AVAILABLE = False

try:
    import nats  # NATS for ultra-low latency
    NATS_AVAILABLE = True
except ImportError:
    NATS_AVAILABLE = False

import redis.asyncio as redis

logger = structlog.get_logger("aura.event_mesh")


class EventType(str, Enum):
    """Event types in our system - preserving our schema"""
    TOPOLOGY_ANALYSIS = "topology_analysis"
    INCIDENT_DETECTED = "incident_detected"
    AGENT_RESULT = "agent_result"
    PREDICTION_MADE = "prediction_made"
    CONSCIOUSNESS_STATE = "consciousness_state"
    SYSTEM_HEALTH = "system_health"


@dataclass
class AuraEvent:
    """Enhanced event structure preserving our existing message format"""
    event_id: str = field(default_factory=lambda: uuid.uuid4().hex)
    event_type: EventType = EventType.SYSTEM_HEALTH
    source: str = "unknown"
    destination: Optional[str] = None
    payload: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.utcnow)
    correlation_id: Optional[str] = None
    priority: int = 1  # 1=low, 5=critical
    
    # Enhanced fields
    trace_id: Optional[str] = None
    span_id: Optional[str] = None
    
    def to_message(self) -> Message:
        """Convert to our existing Message format for compatibility"""
        return Message(
            message_id=self.event_id,
            source=self.source,
            destination=self.destination or "broadcast",
            message_type=self.event_type.value,
            payload=self.payload,
            timestamp=self.timestamp,
            priority=self.priority,
            correlation_id=self.correlation_id
        )


class EnhancedEventMesh:
    """
    Enhanced event mesh that ADDS capabilities to our existing system:
    - Preserves ConnectionManager integration
    - Adds Pulsar for high-throughput streaming (if available)
    - Adds NATS for ultra-low latency (if available)
    - Falls back to Redis streams gracefully
    - Maintains all existing functionality
    """
    
    def __init__(self, 
                 connection_manager: ConnectionManager,
                 redis_client: redis.Redis,
                 pulsar_url: str = "pulsar://localhost:6650",
                 nats_url: str = "nats://localhost:4222"):
        self.logger = logger
        self.connection_manager = connection_manager
        self.redis = redis_client
        
        # Enhanced streaming clients (optional)
        self.pulsar_client = None
        self.pulsar_producer = None
        self.nats_client = None
        
        # Configuration
        self.pulsar_url = pulsar_url
        self.nats_url = nats_url
        
        # Event handlers - preserve existing pattern
        self.event_handlers: Dict[EventType, List[Callable]] = {}
        
        # Performance metrics
        self.metrics = {
            "events_published": 0,
            "events_consumed": 0,
            "pulsar_events": 0,
            "nats_events": 0,
            "redis_events": 0,
            "handler_executions": 0
        }
    
    async def initialize(self):
        """Initialize with enhanced streaming capabilities"""
        try:
            self.logger.info("⚡ Initializing Enhanced Event Mesh")
            
            # Initialize Pulsar if available
            if PULSAR_AVAILABLE:
                try:
                    self.pulsar_client = pulsar.Client(self.pulsar_url)
                    self.pulsar_producer = self.pulsar_client.create_producer('aura-events')
                    self.logger.info("✅ Pulsar streaming enabled")
                except Exception as e:
                    self.logger.warning("Pulsar initialization failed", error=str(e))
            
            # Initialize NATS if available
            if NATS_AVAILABLE:
                try:
                    self.nats_client = await nats.connect(self.nats_url)
                    self.logger.info("✅ NATS ultra-low latency enabled")
                except Exception as e:
                    self.logger.warning("NATS initialization failed", error=str(e))
            
            # Start event processing
            asyncio.create_task(self._process_events())
            
            self.logger.info("✅ Enhanced Event Mesh initialized")
            
        except Exception as e:
            self.logger.error("❌ Event Mesh initialization failed", error=str(e))
            # Graceful fallback - system still works with Redis only
            self.logger.info("🔄 Falling back to Redis-only event streaming")
    
    async def publish_event(self, event: AuraEvent) -> bool:
        """Publish event using best available transport"""
        try:
            # Ultra-low latency events via NATS
            if self.nats_client and event.priority >= 4:
                await self._publish_via_nats(event)
                self.metrics["nats_events"] += 1
            
            # High-throughput events via Pulsar
            elif self.pulsar_producer and event.event_type in [
                EventType.TOPOLOGY_ANALYSIS, 
                EventType.AGENT_RESULT
            ]:
                await self._publish_via_pulsar(event)
                self.metrics["pulsar_events"] += 1
            
            # Fallback to Redis streams
            else:
                await self._publish_via_redis(event)
                self.metrics["redis_events"] += 1
            
            # Also send through existing connection manager for compatibility
            message = event.to_message()
            await self.connection_manager.message_queue.put(message)
            
            self.metrics["events_published"] += 1
            
            self.logger.debug("📤 Event published", 
                            event_id=event.event_id,
                            event_type=event.event_type.value)
            
            return True
            
        except Exception as e:
            self.logger.error("❌ Event publishing failed", 
                            event_id=event.event_id, error=str(e))
            return False
    
    async def _publish_via_nats(self, event: AuraEvent):
        """Publish via NATS for ultra-low latency"""
        subject = f"aura.{event.event_type.value}"
        data = json.dumps({
            "event_id": event.event_id,
            "payload": event.payload,
            "timestamp": event.timestamp.isoformat(),
            "source": event.source
        }).encode()
        
        await self.nats_client.publish(subject, data)
    
    async def _publish_via_pulsar(self, event: AuraEvent):
        """Publish via Pulsar for high throughput"""
        message_data = json.dumps({
            "event_id": event.event_id,
            "event_type": event.event_type.value,
            "payload": event.payload,
            "timestamp": event.timestamp.isoformat(),
            "source": event.source
        }).encode()
        
        self.pulsar_producer.send(message_data)
    
    async def _publish_via_redis(self, event: AuraEvent):
        """Publish via Redis streams (fallback)"""
        stream_key = f"aura:events:{event.event_type.value}"
        
        await self.redis.xadd(stream_key, {
            "event_id": event.event_id,
            "payload": json.dumps(event.payload),
            "timestamp": event.timestamp.isoformat(),
            "source": event.source,
            "priority": str(event.priority)
        })
    
    def subscribe_to_event(self, event_type: EventType, handler: Callable):
        """Subscribe to specific event type"""
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        
        self.event_handlers[event_type].append(handler)
        
        self.logger.info("📥 Event handler registered", 
                        event_type=event_type.value,
                        handler=handler.__name__)
    
    async def _process_events(self):
        """Background event processing"""
        while True:
            try:
                # Process NATS events
                if self.nats_client:
                    await self._process_nats_events()
                
                # Process Redis events
                await self._process_redis_events()
                
                await asyncio.sleep(0.1)  # Small delay to prevent busy waiting
                
            except Exception as e:
                self.logger.error("❌ Event processing error", error=str(e))
                await asyncio.sleep(1)
    
    async def _process_nats_events(self):
        """Process NATS events"""
        # This would implement NATS subscription processing
        # For now, just a placeholder
        pass
    
    async def _process_redis_events(self):
        """Process Redis stream events"""
        try:
            # Read from all event streams
            for event_type in EventType:
                stream_key = f"aura:events:{event_type.value}"
                
                # Read latest events
                events = await self.redis.xread({stream_key: '$'}, count=10, block=100)
                
                for stream, messages in events:
                    for message_id, fields in messages:
                        await self._handle_event(event_type, fields)
                        self.metrics["events_consumed"] += 1
                        
        except Exception as e:
            self.logger.error("❌ Redis event processing failed", error=str(e))
    
    async def _handle_event(self, event_type: EventType, event_data: Dict[str, Any]):
        """Handle individual event"""
        try:
            handlers = self.event_handlers.get(event_type, [])
            
            for handler in handlers:
                try:
                    await handler(event_data)
                    self.metrics["handler_executions"] += 1
                except Exception as e:
                    self.logger.error("❌ Event handler failed", 
                                    event_type=event_type.value,
                                    handler=handler.__name__,
                                    error=str(e))
                    
        except Exception as e:
            self.logger.error("❌ Event handling failed", 
                            event_type=event_type.value, error=str(e))
    
    # Convenience methods for common events
    async def publish_topology_analysis(self, topology_features: TopologyFeatures):
        """Publish topology analysis event"""
        event = AuraEvent(
            event_type=EventType.TOPOLOGY_ANALYSIS,
            source="topology_analyzer",
            payload=topology_features.dict(),
            priority=3
        )
        await self.publish_event(event)
    
    async def publish_incident_detected(self, incident_analysis: IncidentAnalysis):
        """Publish incident detection event"""
        event = AuraEvent(
            event_type=EventType.INCIDENT_DETECTED,
            source="agent_orchestrator",
            payload=incident_analysis.dict(),
            priority=5 if incident_analysis.severity == "critical" else 4
        )
        await self.publish_event(event)
    
    async def publish_agent_result(self, agent_type: AgentType, result: Dict[str, Any]):
        """Publish agent result event"""
        event = AuraEvent(
            event_type=EventType.AGENT_RESULT,
            source=f"agent_{agent_type.value}",
            payload={"agent_type": agent_type.value, "result": result},
            priority=2
        )
        await self.publish_event(event)
    
    async def get_metrics(self) -> Dict[str, Any]:
        """Get event mesh metrics"""
        return {
            "metrics": self.metrics,
            "capabilities": {
                "pulsar_available": self.pulsar_client is not None,
                "nats_available": self.nats_client is not None,
                "redis_available": True
            },
            "handlers_registered": {
                event_type.value: len(handlers) 
                for event_type, handlers in self.event_handlers.items()
            },
            "timestamp": datetime.utcnow().isoformat()
        }
    
    async def cleanup(self):
        """Cleanup event mesh resources"""
        if self.pulsar_client:
            self.pulsar_client.close()
        
        if self.nats_client:
            await self.nats_client.close()
        
        self.logger.info("🧹 Enhanced Event Mesh cleaned up")


# Factory function for easy integration
async def create_event_mesh(connection_manager: ConnectionManager,
                          redis_client: redis.Redis) -> EnhancedEventMesh:
    """Create and initialize enhanced event mesh"""
    event_mesh = EnhancedEventMesh(connection_manager, redis_client)
    await event_mesh.initialize()
    return event_mesh
