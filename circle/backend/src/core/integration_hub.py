"""
🎯 AURA Intelligence Integration Hub
Smart orchestration of all enhanced components while preserving existing architecture
"""

from typing import Dict, Any, Optional
from datetime import datetime
import asyncio

import structlog
import redis.asyncio as redis

# Import our existing services (PRESERVE ALL CONTEXT)
from src.services.knowledge_graph import KnowledgeGraphService
from src.services.topology_analyzer import TopologyAnalyzer
from src.services.agent_orchestrator import AgentOrchestrator
from src.services.prediction_engine import PredictionEngine

# Import our enhanced components
from src.core.enhanced_consciousness import EnhancedConsciousnessCore, create_enhanced_consciousness
from src.core.brain_integration import EnhancedBrainIntegrationLayer
from src.core.event_mesh import EnhancedEventMesh, create_event_mesh, EventType
from src.core.connection_manager import ConnectionManager

# Import our existing models
from src.models.topology import TopologySnapshot, TopologyFeatures
from src.models.agents import IncidentAnalysis, AgentType

logger = structlog.get_logger("aura.integration_hub")


class AuraIntelligenceHub:
    """
    Master integration hub that orchestrates all components:
    - Preserves ALL existing functionality
    - Enhances with consciousness-driven intelligence
    - Adds advanced event streaming
    - Maintains backward compatibility
    - Provides unified interface for all operations
    """
    
    def __init__(self, redis_client: redis.Redis):
        self.logger = logger
        self.redis = redis_client
        
        # Existing services (PRESERVE FUNCTIONALITY)
        self.knowledge_graph: Optional[KnowledgeGraphService] = None
        self.topology_analyzer: Optional[TopologyAnalyzer] = None
        self.agent_orchestrator: Optional[AgentOrchestrator] = None
        self.prediction_engine: Optional[PredictionEngine] = None
        
        # Enhanced components
        self.connection_manager: Optional[ConnectionManager] = None
        self.consciousness_core: Optional[EnhancedConsciousnessCore] = None
        self.brain_integration: Optional[EnhancedBrainIntegrationLayer] = None
        self.event_mesh: Optional[EnhancedEventMesh] = None
        
        # System state
        self.initialized = False
        self.health_status = "initializing"
        
        # Performance metrics
        self.metrics = {
            "total_operations": 0,
            "enhanced_operations": 0,
            "consciousness_decisions": 0,
            "event_mesh_messages": 0,
            "brain_integrations": 0
        }
    
    async def initialize(self):
        """Initialize the complete AURA Intelligence system"""
        try:
            self.logger.info("🚀 Initializing AURA Intelligence Hub")
            
            # Initialize existing services first (PRESERVE ORDER)
            await self._initialize_core_services()
            
            # Initialize enhanced components
            await self._initialize_enhanced_components()
            
            # Set up event subscriptions
            await self._setup_event_subscriptions()
            
            self.initialized = True
            self.health_status = "healthy"
            
            self.logger.info("✅ AURA Intelligence Hub fully initialized")
            
        except Exception as e:
            self.logger.error("❌ Hub initialization failed", error=str(e))
            self.health_status = "failed"
            raise
    
    async def _initialize_core_services(self):
        """Initialize existing core services"""
        # Initialize in dependency order
        self.knowledge_graph = KnowledgeGraphService()
        await self.knowledge_graph.initialize()
        
        self.topology_analyzer = TopologyAnalyzer()
        await self.topology_analyzer.initialize()
        
        self.agent_orchestrator = AgentOrchestrator(self.knowledge_graph)
        await self.agent_orchestrator.initialize()
        
        self.prediction_engine = PredictionEngine(
            self.knowledge_graph, 
            self.topology_analyzer
        )
        await self.prediction_engine.initialize()
        
        self.logger.info("✅ Core services initialized")
    
    async def _initialize_enhanced_components(self):
        """Initialize enhanced components"""
        # Connection manager
        self.connection_manager = ConnectionManager(self.redis)
        await self.connection_manager.initialize()
        
        # Enhanced consciousness
        self.consciousness_core = await create_enhanced_consciousness(
            self.connection_manager, self.redis
        )
        
        # Enhanced brain integration
        self.brain_integration = EnhancedBrainIntegrationLayer()
        await self.brain_integration.initialize()
        
        # Enhanced event mesh
        self.event_mesh = await create_event_mesh(
            self.connection_manager, self.redis
        )
        
        self.logger.info("✅ Enhanced components initialized")
    
    async def _setup_event_subscriptions(self):
        """Set up event subscriptions for intelligent orchestration"""
        # Subscribe to topology analysis events
        self.event_mesh.subscribe_to_event(
            EventType.TOPOLOGY_ANALYSIS,
            self._handle_topology_event
        )
        
        # Subscribe to incident detection events
        self.event_mesh.subscribe_to_event(
            EventType.INCIDENT_DETECTED,
            self._handle_incident_event
        )
        
        # Subscribe to agent results
        self.event_mesh.subscribe_to_event(
            EventType.AGENT_RESULT,
            self._handle_agent_result_event
        )
        
        self.logger.info("✅ Event subscriptions configured")
    
    # === ENHANCED ANALYSIS METHODS ===
    
    async def analyze_topology_with_consciousness(self, 
                                                snapshot: TopologySnapshot) -> TopologyFeatures:
        """Enhanced topology analysis with consciousness"""
        try:
            # Focus consciousness on topology analysis
            await self.consciousness_core.focus_attention(
                "topology_analysis",
                {"snapshot_id": snapshot.id, "services": len(snapshot.services)}
            )
            
            # Use existing topology analyzer (PRESERVE FUNCTIONALITY)
            features = await self.topology_analyzer.analyze_topology(snapshot)
            
            # Publish event for other components
            await self.event_mesh.publish_topology_analysis(features)
            
            self.metrics["total_operations"] += 1
            self.metrics["enhanced_operations"] += 1
            
            self.logger.info("🔍 Conscious topology analysis completed",
                           snapshot_id=snapshot.id,
                           complexity=features.complexity_score)
            
            return features
            
        except Exception as e:
            self.logger.error("❌ Conscious topology analysis failed", error=str(e))
            # Fallback to base topology analyzer
            return await self.topology_analyzer.analyze_topology(snapshot)
    
    async def analyze_incident_with_intelligence(self, 
                                               incident_data: Dict[str, Any]) -> IncidentAnalysis:
        """Enhanced incident analysis with full intelligence"""
        try:
            # Use consciousness for intelligent analysis
            analysis = await self.consciousness_core.analyze_incident_with_consciousness(
                incident_data
            )
            
            # Enhance with agent orchestration (PRESERVE EXISTING)
            agent_results = await self.agent_orchestrator.analyze_incident(incident_data)
            
            # Combine consciousness and agent insights
            enhanced_analysis = self._combine_analysis_results(analysis, agent_results)
            
            # Publish incident detection event
            await self.event_mesh.publish_incident_detected(enhanced_analysis)
            
            self.metrics["consciousness_decisions"] += 1
            self.metrics["total_operations"] += 1
            
            self.logger.info("🧠 Intelligent incident analysis completed",
                           analysis_id=enhanced_analysis.analysis_id,
                           confidence=enhanced_analysis.confidence)
            
            return enhanced_analysis
            
        except Exception as e:
            self.logger.error("❌ Intelligent incident analysis failed", error=str(e))
            # Fallback to base agent orchestrator
            return await self.agent_orchestrator.analyze_incident(incident_data)
    
    async def predict_with_consciousness(self, 
                                       topology_features: TopologyFeatures) -> Dict[str, Any]:
        """Enhanced prediction with consciousness"""
        try:
            # Use consciousness for prediction
            prediction = await self.consciousness_core.predict_with_consciousness(
                topology_features
            )
            
            # Enhance with existing prediction engine
            engine_prediction = await self.prediction_engine.predict_failure(topology_features)
            
            # Combine predictions intelligently
            combined_prediction = self._combine_predictions(prediction, engine_prediction)
            
            self.metrics["brain_integrations"] += 1
            self.metrics["total_operations"] += 1
            
            return combined_prediction.dict()
            
        except Exception as e:
            self.logger.error("❌ Conscious prediction failed", error=str(e))
            # Fallback to base prediction engine
            return (await self.prediction_engine.predict_failure(topology_features)).dict()
    
    # === EVENT HANDLERS ===
    
    async def _handle_topology_event(self, event_data: Dict[str, Any]):
        """Handle topology analysis events"""
        try:
            self.logger.debug("📊 Processing topology event", 
                            event_id=event_data.get("event_id"))
            
            # Could trigger additional analysis or notifications
            # For now, just log and update metrics
            self.metrics["event_mesh_messages"] += 1
            
        except Exception as e:
            self.logger.error("❌ Topology event handling failed", error=str(e))
    
    async def _handle_incident_event(self, event_data: Dict[str, Any]):
        """Handle incident detection events"""
        try:
            self.logger.info("🚨 Processing incident event",
                           event_id=event_data.get("event_id"))
            
            # Could trigger automated remediation or escalation
            self.metrics["event_mesh_messages"] += 1
            
        except Exception as e:
            self.logger.error("❌ Incident event handling failed", error=str(e))
    
    async def _handle_agent_result_event(self, event_data: Dict[str, Any]):
        """Handle agent result events"""
        try:
            agent_type = event_data.get("payload", {}).get("agent_type")
            self.logger.debug("🤖 Processing agent result event",
                            agent_type=agent_type)
            
            self.metrics["event_mesh_messages"] += 1
            
        except Exception as e:
            self.logger.error("❌ Agent result event handling failed", error=str(e))
    
    # === UTILITY METHODS ===
    
    def _combine_analysis_results(self, 
                                consciousness_analysis: IncidentAnalysis,
                                agent_analysis: IncidentAnalysis) -> IncidentAnalysis:
        """Combine consciousness and agent analysis results"""
        # Use consciousness analysis as base, enhance with agent insights
        combined = consciousness_analysis
        
        # Combine confidence scores (weighted average)
        combined.confidence = (
            consciousness_analysis.confidence * 0.6 + 
            agent_analysis.confidence * 0.4
        )
        
        # Merge recommended actions
        combined.recommended_actions.extend(agent_analysis.recommended_actions)
        combined.recommended_actions = list(set(combined.recommended_actions))  # Remove duplicates
        
        # Use higher severity
        if agent_analysis.severity == "critical":
            combined.severity = "critical"
        
        return combined
    
    def _combine_predictions(self, consciousness_pred, engine_pred):
        """Combine consciousness and engine predictions"""
        # Use consciousness prediction as base
        combined = consciousness_pred
        
        # Average failure probabilities
        combined.failure_probability = (
            consciousness_pred.failure_probability * 0.7 +
            engine_pred.failure_probability * 0.3
        )
        
        # Use more conservative time to failure
        if engine_pred.time_to_failure and consciousness_pred.time_to_failure:
            combined.time_to_failure = min(
                engine_pred.time_to_failure,
                consciousness_pred.time_to_failure
            )
        
        return combined
    
    # === STATUS AND HEALTH ===
    
    async def get_system_status(self) -> Dict[str, Any]:
        """Get complete system status"""
        status = {
            "hub_status": self.health_status,
            "initialized": self.initialized,
            "metrics": self.metrics,
            "components": {}
        }
        
        # Get status from all components
        if self.consciousness_core:
            status["components"]["consciousness"] = await self.consciousness_core.get_status()
        
        if self.event_mesh:
            status["components"]["event_mesh"] = await self.event_mesh.get_metrics()
        
        if self.knowledge_graph:
            status["components"]["knowledge_graph"] = await self.knowledge_graph.health_check()
        
        if self.topology_analyzer:
            status["components"]["topology_analyzer"] = await self.topology_analyzer.health_check()
        
        status["timestamp"] = datetime.utcnow().isoformat()
        
        return status
    
    async def cleanup(self):
        """Cleanup all components"""
        self.logger.info("🧹 Cleaning up AURA Intelligence Hub")
        
        # Cleanup in reverse order
        if self.event_mesh:
            await self.event_mesh.cleanup()
        
        if self.consciousness_core:
            await self.consciousness_core.cleanup()
        
        if self.connection_manager:
            await self.connection_manager.cleanup()
        
        if self.prediction_engine:
            await self.prediction_engine.cleanup()
        
        if self.agent_orchestrator:
            await self.agent_orchestrator.cleanup()
        
        if self.topology_analyzer:
            await self.topology_analyzer.cleanup()
        
        if self.knowledge_graph:
            await self.knowledge_graph.cleanup()
        
        self.logger.info("✅ AURA Intelligence Hub cleaned up")


# Global hub instance
aura_hub: Optional[AuraIntelligenceHub] = None


async def get_aura_hub(redis_client: redis.Redis) -> AuraIntelligenceHub:
    """Get or create the global AURA Intelligence Hub"""
    global aura_hub
    
    if aura_hub is None:
        aura_hub = AuraIntelligenceHub(redis_client)
        await aura_hub.initialize()
    
    return aura_hub
