"""
🧠 AURA Intelligence Consciousness Core
Enhanced with best-in-class libraries while preserving our existing architecture
"""

from typing import Dict, Any, Optional, List, TypeVar, Generic
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import asyncio
import uuid
import weakref
from collections import defaultdict, deque

import structlog
from pydantic import BaseModel, Field

# Enhanced with best-in-class libraries (additive, not replacement)
try:
    from cognee import add, prune, search, cognify  # Memory enhancement
    COGNEE_AVAILABLE = True
except ImportError:
    COGNEE_AVAILABLE = False

try:
    from langraph import StateGraph, END  # State management enhancement
    LANGRAPH_AVAILABLE = True
except ImportError:
    LANGRAPH_AVAILABLE = False

try:
    from mem0 import MemoryClient  # Context enhancement
    MEM0_AVAILABLE = True
except ImportError:
    MEM0_AVAILABLE = False

import redis.asyncio as redis

# Import our existing brain components (preserve existing integration)
from src.core.brain_integration import brain_integration

logger = structlog.get_logger("aura.consciousness")


# Preserve our existing enums and dataclasses
class ConsciousnessState(str, Enum):
    """States of consciousness - keeping our original design"""
    DORMANT = "dormant"
    AWARE = "aware"
    FOCUSED = "focused"
    LEARNING = "learning"
    CREATING = "creating"
    TRANSCENDENT = "transcendent"


class MemoryType(str, Enum):
    """Types of memory - preserving our schema"""
    WORKING = "working"
    EPISODIC = "episodic"
    SEMANTIC = "semantic"
    PROCEDURAL = "procedural"
    EMOTIONAL = "emotional"
    CONTEXTUAL = "contextual"


@dataclass
class MemoryFragment:
    """Enhanced memory fragment with optional external library integration"""
    id: str = field(default_factory=lambda: uuid.uuid4().hex)
    content: Dict[str, Any] = field(default_factory=dict)
    memory_type: MemoryType = MemoryType.WORKING
    timestamp: datetime = field(default_factory=datetime.utcnow)
    importance: float = 0.5
    access_count: int = 0
    last_accessed: datetime = field(default_factory=datetime.utcnow)
    associations: List[str] = field(default_factory=list)
    emotional_valence: float = 0.0
    confidence: float = 1.0

    # Enhanced with external libraries if available
    _cognee_id: Optional[str] = None
    _mem0_id: Optional[str] = None

    def access(self):
        """Record memory access with enhanced tracking"""
        self.access_count += 1
        self.last_accessed = datetime.utcnow()
        self.importance = min(1.0, self.importance + 0.01)


class ConsciousnessCore:
    """
    Senior-level consciousness core using best libraries:
    - Cognee: Memory management and knowledge graphs
    - LangGraph: State management and workflows  
    - Mem0: Context preservation and retrieval
    - Redis: High-performance caching
    """
    
    def __init__(self, redis_client: Optional[redis.Redis] = None):
        self.logger = logger
        self.redis = redis_client
        
        # Initialize best-in-class components
        self.memory_client = MemoryClient()  # Mem0 for context
        self.state = ConsciousnessState()
        
        # LangGraph workflow for consciousness states
        self.workflow = self._build_consciousness_workflow()
        
    def _build_consciousness_workflow(self) -> StateGraph:
        """Build consciousness workflow using LangGraph"""
        workflow = StateGraph(ConsciousnessState)
        
        # Define consciousness transitions
        workflow.add_node("aware", self._aware_state)
        workflow.add_node("focused", self._focused_state) 
        workflow.add_node("learning", self._learning_state)
        workflow.add_node("creating", self._creating_state)
        
        # Define transitions
        workflow.add_edge("aware", "focused")
        workflow.add_edge("focused", "learning")
        workflow.add_edge("learning", "creating")
        workflow.add_edge("creating", END)
        
        workflow.set_entry_point("aware")
        
        return workflow.compile()
    
    async def _aware_state(self, state: ConsciousnessState) -> ConsciousnessState:
        """Basic awareness state"""
        state.level = 0.3
        await self._store_context("state_transition", {"to": "aware"})
        return state
    
    async def _focused_state(self, state: ConsciousnessState) -> ConsciousnessState:
        """Focused attention state"""
        state.level = 0.6
        await self._store_context("state_transition", {"to": "focused"})
        return state
    
    async def _learning_state(self, state: ConsciousnessState) -> ConsciousnessState:
        """Active learning state"""
        state.level = 0.8
        await self._store_context("state_transition", {"to": "learning"})
        return state
    
    async def _creating_state(self, state: ConsciousnessState) -> ConsciousnessState:
        """Creative problem-solving state"""
        state.level = 0.9
        await self._store_context("state_transition", {"to": "creating"})
        return state
    
    async def initialize(self):
        """Initialize consciousness with best practices"""
        try:
            self.logger.info("🧠 Initializing Consciousness Core")
            
            # Initialize Cognee for memory management
            await cognify()
            
            # Load persistent context from Mem0
            await self._load_persistent_context()
            
            self.logger.info("✅ Consciousness Core initialized")
            
        except Exception as e:
            self.logger.error("❌ Consciousness initialization failed", error=str(e))
            raise
    
    async def focus_attention(self, target: str, context: Dict[str, Any] = None):
        """Focus consciousness using LangGraph workflow"""
        self.state.focus = target
        if context:
            self.state.context.update(context)
        
        # Store in Cognee memory
        await add(f"attention_focus: {target}", context or {})
        
        # Transition through workflow
        result = await self.workflow.ainvoke(self.state)
        self.state = result
        
        self.logger.info("👁️ Attention focused", target=target, level=self.state.level)
    
    async def make_decision(self, decision_context: Dict[str, Any]) -> Dict[str, Any]:
        """Make decision using memory and brain integration"""
        try:
            # Search relevant memories with Cognee
            relevant_memories = await search(str(decision_context))
            
            # Get brain analysis
            brain_analysis = await brain_integration.perform_causal_inference(decision_context)
            
            # Store decision context in Mem0
            await self.memory_client.add(
                messages=[{"role": "system", "content": str(decision_context)}],
                user_id="consciousness_core"
            )
            
            # Combine memory and brain insights
            decision = {
                "decision_id": f"dec_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
                "context": decision_context,
                "memories": relevant_memories,
                "brain_analysis": brain_analysis,
                "confidence": brain_analysis.get("overall_confidence", 0.5),
                "timestamp": datetime.utcnow().isoformat()
            }
            
            # Cache decision in Redis
            if self.redis:
                await self.redis.setex(
                    f"decision:{decision['decision_id']}", 
                    3600, 
                    str(decision)
                )
            
            self.logger.info("🎯 Decision made", 
                           decision_id=decision["decision_id"],
                           confidence=decision["confidence"])
            
            return decision
            
        except Exception as e:
            self.logger.error("❌ Decision making failed", error=str(e))
            raise
    
    async def _store_context(self, event: str, data: Dict[str, Any]):
        """Store context using Cognee"""
        await add(f"{event}: {datetime.utcnow().isoformat()}", data)
    
    async def _load_persistent_context(self):
        """Load context from Mem0"""
        try:
            memories = await self.memory_client.get_all(user_id="consciousness_core")
            if memories:
                self.state.context["persistent_memories"] = len(memories)
                self.logger.info("📥 Persistent context loaded", memories=len(memories))
        except Exception as e:
            self.logger.warning("⚠️ Failed to load persistent context", error=str(e))
    
    async def get_status(self) -> Dict[str, Any]:
        """Get consciousness status"""
        return {
            "state": {
                "level": self.state.level,
                "focus": self.state.focus,
                "context_size": len(self.state.context)
            },
            "timestamp": datetime.utcnow().isoformat()
        }
    
    async def cleanup(self):
        """Cleanup with proper resource management"""
        # Prune old memories with Cognee
        await prune()
        
        self.logger.info("🧹 Consciousness Core cleaned up")


# Singleton instance
consciousness_core = ConsciousnessCore()
