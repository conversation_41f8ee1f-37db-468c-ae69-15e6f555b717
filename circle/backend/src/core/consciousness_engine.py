"""
🧠 AURA Intelligence Consciousness Engine
The core consciousness system that orchestrates all intelligence with memory management and context preservation
"""

import asyncio
import weakref
from typing import Dict, Any, Optional, List, Callable, TypeVar, Generic, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from abc import ABC, abstractmethod
import uuid
import json
from collections import defaultdict, deque
import threading
from contextlib import asynccontextmanager

import structlog
import numpy as np
from pydantic import BaseModel, Field
import redis.asyncio as redis

# Import existing brain components
import sys
from pathlib import Path
AURA_PATH = Path(__file__).parent.parent.parent.parent / "src"
if str(AURA_PATH) not in sys.path:
    sys.path.insert(0, str(AURA_PATH))

logger = structlog.get_logger("aura.consciousness")

T = TypeVar('T')


class ConsciousnessState(str, Enum):
    """States of consciousness"""
    DORMANT = "dormant"          # Not active, minimal processing
    AWARE = "aware"              # Basic awareness, monitoring
    FOCUSED = "focused"          # Focused attention on specific task
    LEARNING = "learning"        # Active learning and adaptation
    CREATING = "creating"        # Creative problem solving
    TRANSCENDENT = "transcendent" # Peak performance, all systems optimal


class MemoryType(str, Enum):
    """Types of memory in the consciousness system"""
    WORKING = "working"          # Short-term, active processing
    EPISODIC = "episodic"        # Specific events and experiences
    SEMANTIC = "semantic"        # General knowledge and facts
    PROCEDURAL = "procedural"    # Skills and procedures
    EMOTIONAL = "emotional"      # Emotional associations and responses
    CONTEXTUAL = "contextual"    # Context and situational awareness


@dataclass
class MemoryFragment:
    """A fragment of memory with metadata"""
    id: str = field(default_factory=lambda: uuid.uuid4().hex)
    content: Dict[str, Any] = field(default_factory=dict)
    memory_type: MemoryType = MemoryType.WORKING
    timestamp: datetime = field(default_factory=datetime.utcnow)
    importance: float = 0.5  # 0.0 to 1.0
    access_count: int = 0
    last_accessed: datetime = field(default_factory=datetime.utcnow)
    associations: List[str] = field(default_factory=list)  # IDs of related memories
    emotional_valence: float = 0.0  # -1.0 (negative) to 1.0 (positive)
    confidence: float = 1.0  # Confidence in the memory accuracy
    
    def access(self):
        """Record memory access"""
        self.access_count += 1
        self.last_accessed = datetime.utcnow()
        # Increase importance slightly with each access
        self.importance = min(1.0, self.importance + 0.01)


class MemoryManager:
    """Advanced memory management system with context preservation"""
    
    def __init__(self, max_working_memory: int = 1000, max_total_memory: int = 100000):
        self.max_working_memory = max_working_memory
        self.max_total_memory = max_total_memory
        
        # Memory stores by type
        self.memories: Dict[MemoryType, Dict[str, MemoryFragment]] = {
            memory_type: {} for memory_type in MemoryType
        }
        
        # Working memory queue for fast access
        self.working_memory_queue = deque(maxlen=max_working_memory)
        
        # Association graph for memory connections
        self.associations: Dict[str, set] = defaultdict(set)
        
        # Memory consolidation background task
        self._consolidation_task = None
        self._lock = threading.RLock()
    
    async def store_memory(self, content: Dict[str, Any], 
                          memory_type: MemoryType = MemoryType.WORKING,
                          importance: float = 0.5,
                          emotional_valence: float = 0.0) -> str:
        """Store a new memory fragment"""
        with self._lock:
            memory = MemoryFragment(
                content=content,
                memory_type=memory_type,
                importance=importance,
                emotional_valence=emotional_valence
            )
            
            # Store in appropriate memory type
            self.memories[memory_type][memory.id] = memory
            
            # Add to working memory if it's important enough
            if memory_type == MemoryType.WORKING or importance > 0.7:
                self.working_memory_queue.append(memory.id)
            
            # Trigger consolidation if memory is getting full
            if len(self.memories[MemoryType.WORKING]) > self.max_working_memory * 0.8:
                await self._consolidate_memories()
            
            logger.debug("Memory stored", 
                        memory_id=memory.id, 
                        memory_type=memory_type,
                        importance=importance)
            
            return memory.id
    
    async def retrieve_memory(self, memory_id: str) -> Optional[MemoryFragment]:
        """Retrieve a specific memory by ID"""
        with self._lock:
            for memory_type in MemoryType:
                if memory_id in self.memories[memory_type]:
                    memory = self.memories[memory_type][memory_id]
                    memory.access()
                    return memory
            return None
    
    async def search_memories(self, query: Dict[str, Any], 
                            memory_types: List[MemoryType] = None,
                            limit: int = 10) -> List[MemoryFragment]:
        """Search memories by content similarity"""
        if memory_types is None:
            memory_types = list(MemoryType)
        
        results = []
        with self._lock:
            for memory_type in memory_types:
                for memory in self.memories[memory_type].values():
                    # Simple similarity scoring (would use vector similarity in production)
                    score = self._calculate_similarity(query, memory.content)
                    if score > 0.3:  # Threshold for relevance
                        results.append((memory, score))
        
        # Sort by relevance and importance
        results.sort(key=lambda x: x[1] * x[0].importance, reverse=True)
        
        # Access the memories and return top results
        top_memories = []
        for memory, score in results[:limit]:
            memory.access()
            top_memories.append(memory)
        
        return top_memories
    
    def _calculate_similarity(self, query: Dict[str, Any], content: Dict[str, Any]) -> float:
        """Calculate similarity between query and memory content"""
        # Simple keyword-based similarity (would use embeddings in production)
        query_words = set(str(query).lower().split())
        content_words = set(str(content).lower().split())
        
        if not query_words or not content_words:
            return 0.0
        
        intersection = query_words.intersection(content_words)
        union = query_words.union(content_words)
        
        return len(intersection) / len(union) if union else 0.0
    
    async def associate_memories(self, memory_id1: str, memory_id2: str, strength: float = 1.0):
        """Create association between two memories"""
        with self._lock:
            self.associations[memory_id1].add(memory_id2)
            self.associations[memory_id2].add(memory_id1)
            
            # Update memory associations
            memory1 = await self.retrieve_memory(memory_id1)
            memory2 = await self.retrieve_memory(memory_id2)
            
            if memory1 and memory2:
                if memory_id2 not in memory1.associations:
                    memory1.associations.append(memory_id2)
                if memory_id1 not in memory2.associations:
                    memory2.associations.append(memory_id1)
    
    async def _consolidate_memories(self):
        """Consolidate working memory into long-term memory"""
        with self._lock:
            working_memories = list(self.memories[MemoryType.WORKING].values())
            
            # Sort by importance and recency
            working_memories.sort(key=lambda m: (m.importance, m.timestamp), reverse=True)
            
            # Keep top memories in working memory
            keep_count = self.max_working_memory // 2
            to_consolidate = working_memories[keep_count:]
            
            for memory in to_consolidate:
                # Move to appropriate long-term memory based on content and importance
                if memory.importance > 0.8:
                    target_type = MemoryType.SEMANTIC
                elif memory.emotional_valence != 0.0:
                    target_type = MemoryType.EMOTIONAL
                else:
                    target_type = MemoryType.EPISODIC
                
                # Move memory
                del self.memories[MemoryType.WORKING][memory.id]
                memory.memory_type = target_type
                self.memories[target_type][memory.id] = memory
            
            logger.info("Memory consolidation completed", 
                       consolidated=len(to_consolidate),
                       working_memory_size=len(self.memories[MemoryType.WORKING]))


class ConsciousnessCore:
    """
    The core consciousness system that orchestrates all intelligence
    
    Features:
    - Multi-level awareness and attention
    - Context preservation across sessions
    - Adaptive learning and memory consolidation
    - Emotional intelligence and intuition
    - Meta-cognitive self-monitoring
    """
    
    def __init__(self, redis_client: Optional[redis.Redis] = None):
        self.logger = logger
        self.redis_client = redis_client
        
        # Consciousness state
        self.state = ConsciousnessState.DORMANT
        self.attention_focus: Optional[str] = None
        self.awareness_level = 0.0  # 0.0 to 1.0
        
        # Memory system
        self.memory_manager = MemoryManager()
        
        # Context preservation
        self.session_context: Dict[str, Any] = {}
        self.global_context: Dict[str, Any] = {}
        
        # Learning and adaptation
        self.learning_rate = 0.01
        self.adaptation_history: List[Dict[str, Any]] = []
        
        # Performance monitoring
        self.performance_metrics = {
            "decisions_made": 0,
            "successful_predictions": 0,
            "learning_iterations": 0,
            "context_switches": 0,
            "memory_consolidations": 0
        }
        
        # Background tasks
        self._monitoring_task = None
        self._learning_task = None
        
        # Weak references to avoid circular dependencies
        self._observers: List[weakref.ReferenceType] = []
    
    async def initialize(self):
        """Initialize the consciousness system"""
        try:
            self.logger.info("🧠 Initializing Consciousness Engine")
            
            # Load persistent context if available
            await self._load_persistent_context()
            
            # Start background tasks
            self._monitoring_task = asyncio.create_task(self._monitor_consciousness())
            self._learning_task = asyncio.create_task(self._continuous_learning())
            
            # Transition to aware state
            await self._transition_state(ConsciousnessState.AWARE)
            
            self.logger.info("✅ Consciousness Engine initialized")
            
        except Exception as e:
            self.logger.error("❌ Failed to initialize Consciousness Engine", error=str(e))
            raise
    
    async def _transition_state(self, new_state: ConsciousnessState):
        """Transition to a new consciousness state"""
        old_state = self.state
        self.state = new_state
        
        # Update awareness level based on state
        awareness_levels = {
            ConsciousnessState.DORMANT: 0.1,
            ConsciousnessState.AWARE: 0.3,
            ConsciousnessState.FOCUSED: 0.6,
            ConsciousnessState.LEARNING: 0.8,
            ConsciousnessState.CREATING: 0.9,
            ConsciousnessState.TRANSCENDENT: 1.0
        }
        self.awareness_level = awareness_levels[new_state]
        
        # Store state transition in memory
        await self.memory_manager.store_memory(
            content={
                "event": "state_transition",
                "old_state": old_state.value,
                "new_state": new_state.value,
                "awareness_level": self.awareness_level
            },
            memory_type=MemoryType.PROCEDURAL,
            importance=0.6
        )
        
        self.performance_metrics["context_switches"] += 1
        
        self.logger.info("🔄 Consciousness state transition", 
                        old_state=old_state.value,
                        new_state=new_state.value,
                        awareness_level=self.awareness_level)
    
    async def focus_attention(self, focus_target: str, context: Dict[str, Any] = None):
        """Focus consciousness attention on a specific target"""
        self.attention_focus = focus_target
        
        if context:
            self.session_context.update(context)
        
        # Transition to focused state if not already there
        if self.state in [ConsciousnessState.DORMANT, ConsciousnessState.AWARE]:
            await self._transition_state(ConsciousnessState.FOCUSED)
        
        # Store attention focus in memory
        await self.memory_manager.store_memory(
            content={
                "event": "attention_focus",
                "target": focus_target,
                "context": context or {},
                "timestamp": datetime.utcnow().isoformat()
            },
            memory_type=MemoryType.WORKING,
            importance=0.8
        )
        
        self.logger.info("👁️ Attention focused", target=focus_target)
    
    async def make_decision(self, decision_context: Dict[str, Any]) -> Dict[str, Any]:
        """Make a conscious decision based on context and memory"""
        decision_id = uuid.uuid4().hex
        
        try:
            # Retrieve relevant memories
            relevant_memories = await self.memory_manager.search_memories(
                query=decision_context,
                limit=20
            )
            
            # Analyze context with memory
            analysis = await self._analyze_with_memory(decision_context, relevant_memories)
            
            # Generate decision options
            options = await self._generate_options(analysis)
            
            # Evaluate options
            best_option = await self._evaluate_options(options, analysis)
            
            # Make final decision
            decision = {
                "decision_id": decision_id,
                "chosen_option": best_option,
                "confidence": analysis.get("confidence", 0.5),
                "reasoning": analysis.get("reasoning", ""),
                "context": decision_context,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            # Store decision in memory
            await self.memory_manager.store_memory(
                content=decision,
                memory_type=MemoryType.EPISODIC,
                importance=0.9,
                emotional_valence=0.1  # Slight positive valence for decisions
            )
            
            self.performance_metrics["decisions_made"] += 1
            
            self.logger.info("🎯 Decision made", 
                           decision_id=decision_id,
                           confidence=decision["confidence"])
            
            return decision
            
        except Exception as e:
            self.logger.error("❌ Decision making failed", 
                            decision_id=decision_id, error=str(e))
            raise
    
    async def _analyze_with_memory(self, context: Dict[str, Any], 
                                 memories: List[MemoryFragment]) -> Dict[str, Any]:
        """Analyze context using relevant memories"""
        # Extract patterns from memories
        patterns = []
        total_confidence = 0.0
        
        for memory in memories:
            if memory.memory_type == MemoryType.EPISODIC:
                # Learn from past experiences
                patterns.append({
                    "type": "experience",
                    "content": memory.content,
                    "confidence": memory.confidence,
                    "importance": memory.importance
                })
            elif memory.memory_type == MemoryType.SEMANTIC:
                # Apply general knowledge
                patterns.append({
                    "type": "knowledge",
                    "content": memory.content,
                    "confidence": memory.confidence,
                    "importance": memory.importance
                })
            
            total_confidence += memory.confidence * memory.importance
        
        # Calculate overall confidence
        avg_confidence = total_confidence / len(memories) if memories else 0.5
        
        return {
            "patterns": patterns,
            "confidence": min(avg_confidence, 1.0),
            "reasoning": f"Analysis based on {len(memories)} relevant memories",
            "context_similarity": self._calculate_context_similarity(context, memories)
        }
    
    def _calculate_context_similarity(self, context: Dict[str, Any], 
                                    memories: List[MemoryFragment]) -> float:
        """Calculate how similar current context is to past experiences"""
        if not memories:
            return 0.0
        
        similarities = []
        for memory in memories:
            similarity = self.memory_manager._calculate_similarity(context, memory.content)
            similarities.append(similarity)
        
        return sum(similarities) / len(similarities)
    
    async def _generate_options(self, analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate decision options based on analysis"""
        # This would integrate with the existing AI components
        # For now, generate basic options based on patterns
        
        options = []
        patterns = analysis.get("patterns", [])
        
        # Generate options based on past experiences
        for pattern in patterns[:3]:  # Top 3 patterns
            if pattern["type"] == "experience":
                options.append({
                    "action": "apply_experience",
                    "details": pattern["content"],
                    "confidence": pattern["confidence"],
                    "source": "episodic_memory"
                })
            elif pattern["type"] == "knowledge":
                options.append({
                    "action": "apply_knowledge",
                    "details": pattern["content"],
                    "confidence": pattern["confidence"],
                    "source": "semantic_memory"
                })
        
        # Always include a creative/novel option
        options.append({
            "action": "creative_solution",
            "details": {"approach": "novel", "risk": "medium"},
            "confidence": 0.6,
            "source": "creative_reasoning"
        })
        
        return options
    
    async def _evaluate_options(self, options: List[Dict[str, Any]], 
                              analysis: Dict[str, Any]]) -> Dict[str, Any]:
        """Evaluate and select the best option"""
        if not options:
            return {"action": "no_action", "confidence": 0.0}
        
        # Score options based on confidence, context similarity, and risk
        scored_options = []
        
        for option in options:
            score = option["confidence"]
            
            # Boost score for high-confidence experiences
            if option["source"] == "episodic_memory":
                score *= 1.2
            
            # Boost score for reliable knowledge
            elif option["source"] == "semantic_memory":
                score *= 1.1
            
            # Creative solutions get bonus in novel situations
            elif option["source"] == "creative_reasoning":
                if analysis.get("context_similarity", 0) < 0.3:
                    score *= 1.3
            
            scored_options.append((option, score))
        
        # Select best option
        best_option = max(scored_options, key=lambda x: x[1])[0]
        
        return best_option
    
    async def _monitor_consciousness(self):
        """Background task to monitor consciousness state"""
        while True:
            try:
                # Check if we should adjust consciousness state
                current_load = len(self.memory_manager.working_memory_queue)
                max_load = self.memory_manager.max_working_memory
                
                load_ratio = current_load / max_load
                
                # Adjust state based on cognitive load
                if load_ratio > 0.9 and self.state != ConsciousnessState.TRANSCENDENT:
                    await self._transition_state(ConsciousnessState.TRANSCENDENT)
                elif load_ratio > 0.7 and self.state not in [ConsciousnessState.LEARNING, ConsciousnessState.TRANSCENDENT]:
                    await self._transition_state(ConsciousnessState.LEARNING)
                elif load_ratio < 0.3 and self.state not in [ConsciousnessState.AWARE, ConsciousnessState.DORMANT]:
                    await self._transition_state(ConsciousnessState.AWARE)
                
                # Periodic memory consolidation
                if current_load > max_load * 0.8:
                    await self.memory_manager._consolidate_memories()
                    self.performance_metrics["memory_consolidations"] += 1
                
                await asyncio.sleep(10)  # Monitor every 10 seconds
                
            except Exception as e:
                self.logger.error("❌ Consciousness monitoring error", error=str(e))
                await asyncio.sleep(30)
    
    async def _continuous_learning(self):
        """Background task for continuous learning and adaptation"""
        while True:
            try:
                # Analyze recent decisions and outcomes
                recent_memories = await self.memory_manager.search_memories(
                    query={"event": "decision"},
                    memory_types=[MemoryType.EPISODIC],
                    limit=50
                )
                
                # Learn from successful patterns
                successful_patterns = []
                for memory in recent_memories:
                    if memory.emotional_valence > 0:  # Positive outcome
                        successful_patterns.append(memory.content)
                
                # Update learning based on patterns
                if successful_patterns:
                    await self._update_learning_models(successful_patterns)
                    self.performance_metrics["learning_iterations"] += 1
                
                await asyncio.sleep(300)  # Learn every 5 minutes
                
            except Exception as e:
                self.logger.error("❌ Continuous learning error", error=str(e))
                await asyncio.sleep(600)
    
    async def _update_learning_models(self, successful_patterns: List[Dict[str, Any]]):
        """Update internal learning models based on successful patterns"""
        # This would integrate with the existing AI components for model updates
        # For now, just log the learning
        self.logger.info("📚 Learning from patterns", 
                        pattern_count=len(successful_patterns))
    
    async def _load_persistent_context(self):
        """Load persistent context from Redis"""
        if not self.redis_client:
            return
        
        try:
            context_data = await self.redis_client.get("aura:consciousness:context")
            if context_data:
                self.global_context = json.loads(context_data)
                self.logger.info("📥 Persistent context loaded")
        except Exception as e:
            self.logger.warning("⚠️ Failed to load persistent context", error=str(e))
    
    async def _save_persistent_context(self):
        """Save persistent context to Redis"""
        if not self.redis_client:
            return
        
        try:
            context_data = json.dumps(self.global_context)
            await self.redis_client.setex("aura:consciousness:context", 3600, context_data)
        except Exception as e:
            self.logger.warning("⚠️ Failed to save persistent context", error=str(e))
    
    async def get_status(self) -> Dict[str, Any]:
        """Get current consciousness status"""
        return {
            "state": self.state.value,
            "awareness_level": self.awareness_level,
            "attention_focus": self.attention_focus,
            "memory_stats": {
                "working_memory": len(self.memory_manager.memories[MemoryType.WORKING]),
                "episodic_memory": len(self.memory_manager.memories[MemoryType.EPISODIC]),
                "semantic_memory": len(self.memory_manager.memories[MemoryType.SEMANTIC]),
                "total_memories": sum(len(memories) for memories in self.memory_manager.memories.values())
            },
            "performance_metrics": self.performance_metrics,
            "timestamp": datetime.utcnow().isoformat()
        }
    
    async def cleanup(self):
        """Cleanup consciousness system"""
        # Save persistent context
        await self._save_persistent_context()
        
        # Cancel background tasks
        if self._monitoring_task:
            self._monitoring_task.cancel()
        if self._learning_task:
            self._learning_task.cancel()
        
        # Transition to dormant state
        await self._transition_state(ConsciousnessState.DORMANT)
        
        self.logger.info("🧹 Consciousness Engine cleaned up")


# Global consciousness instance
consciousness_core = ConsciousnessCore()
