"""
🔧 AURA Intelligence Configuration
Production-grade settings with environment variable support
"""

import os
from functools import lru_cache
from typing import Optional, List
from pydantic import BaseSettings, Field, validator


class Settings(BaseSettings):
    """Application settings with environment variable support"""
    
    # === APPLICATION ===
    app_name: str = "AURA Intelligence"
    version: str = "1.0.0"
    debug: bool = Field(default=False, env="DEBUG")
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8000, env="PORT")
    
    # === SECURITY ===
    secret_key: str = Field(..., env="SECRET_KEY")
    algorithm: str = Field(default="HS256", env="ALGORITHM")
    access_token_expire_minutes: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    
    # === DATABASE ===
    # PostgreSQL
    postgres_host: str = Field(default="localhost", env="POSTGRES_HOST")
    postgres_port: int = Field(default=5432, env="POSTGRES_PORT")
    postgres_user: str = Field(default="aura", env="POSTGRES_USER")
    postgres_password: str = Field(..., env="POSTGRES_PASSWORD")
    postgres_db: str = Field(default="aura_intelligence", env="POSTGRES_DB")
    
    @property
    def postgres_url(self) -> str:
        return f"postgresql+asyncpg://{self.postgres_user}:{self.postgres_password}@{self.postgres_host}:{self.postgres_port}/{self.postgres_db}"
    
    # === NEO4J KNOWLEDGE GRAPH ===
    neo4j_uri: str = Field(default="bolt://localhost:7687", env="NEO4J_URI")
    neo4j_user: str = Field(default="neo4j", env="NEO4J_USER")
    neo4j_password: str = Field(..., env="NEO4J_PASSWORD")
    neo4j_database: str = Field(default="neo4j", env="NEO4J_DATABASE")
    
    # === REDIS ===
    redis_host: str = Field(default="localhost", env="REDIS_HOST")
    redis_port: int = Field(default=6379, env="REDIS_PORT")
    redis_password: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    redis_db: int = Field(default=0, env="REDIS_DB")
    
    @property
    def redis_url(self) -> str:
        if self.redis_password:
            return f"redis://:{self.redis_password}@{self.redis_host}:{self.redis_port}/{self.redis_db}"
        return f"redis://{self.redis_host}:{self.redis_port}/{self.redis_db}"
    
    # === KAFKA ===
    kafka_bootstrap_servers: List[str] = Field(
        default=["localhost:9092"], 
        env="KAFKA_BOOTSTRAP_SERVERS"
    )
    kafka_topic_prefix: str = Field(default="aura", env="KAFKA_TOPIC_PREFIX")
    
    @validator('kafka_bootstrap_servers', pre=True)
    def parse_kafka_servers(cls, v):
        if isinstance(v, str):
            return v.split(',')
        return v
    
    # === MACHINE LEARNING ===
    model_path: str = Field(default="./models", env="MODEL_PATH")
    gpu_enabled: bool = Field(default=True, env="GPU_ENABLED")
    batch_size: int = Field(default=32, env="BATCH_SIZE")
    topology_batch_size: int = Field(default=16, env="TOPOLOGY_BATCH_SIZE")
    
    # === TOPOLOGICAL DATA ANALYSIS ===
    tda_max_dimension: int = Field(default=2, env="TDA_MAX_DIMENSION")
    tda_max_edge_length: float = Field(default=1.0, env="TDA_MAX_EDGE_LENGTH")
    persistence_threshold: float = Field(default=0.1, env="PERSISTENCE_THRESHOLD")
    
    # === MONITORING ===
    prometheus_enabled: bool = Field(default=True, env="PROMETHEUS_ENABLED")
    jaeger_enabled: bool = Field(default=True, env="JAEGER_ENABLED")
    jaeger_endpoint: str = Field(default="http://localhost:14268/api/traces", env="JAEGER_ENDPOINT")
    
    # === LOGGING ===
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_format: str = Field(default="json", env="LOG_FORMAT")  # json or console
    
    # === AGENT CONFIGURATION ===
    max_agents: int = Field(default=7, env="MAX_AGENTS")
    agent_timeout: int = Field(default=30, env="AGENT_TIMEOUT")  # seconds
    
    # === PREDICTION ENGINE ===
    prediction_horizon_seconds: int = Field(default=300, env="PREDICTION_HORIZON_SECONDS")  # 5 minutes
    confidence_threshold: float = Field(default=0.8, env="CONFIDENCE_THRESHOLD")
    
    # === RATE LIMITING ===
    rate_limit_requests: int = Field(default=100, env="RATE_LIMIT_REQUESTS")
    rate_limit_window: int = Field(default=60, env="RATE_LIMIT_WINDOW")  # seconds
    
    # === CORS ===
    cors_origins: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:3001"], 
        env="CORS_ORIGINS"
    )
    
    @validator('cors_origins', pre=True)
    def parse_cors_origins(cls, v):
        if isinstance(v, str):
            return v.split(',')
        return v
    
    # === VECTOR DATABASE ===
    chroma_host: str = Field(default="localhost", env="CHROMA_HOST")
    chroma_port: int = Field(default=8000, env="CHROMA_PORT")
    embedding_model: str = Field(default="all-MiniLM-L6-v2", env="EMBEDDING_MODEL")
    
    # === CELERY ===
    celery_broker_url: str = Field(default="redis://localhost:6379/1", env="CELERY_BROKER_URL")
    celery_result_backend: str = Field(default="redis://localhost:6379/2", env="CELERY_RESULT_BACKEND")
    
    # === DEVELOPMENT ===
    reload: bool = Field(default=False, env="RELOAD")
    testing: bool = Field(default=False, env="TESTING")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


class DevelopmentSettings(Settings):
    """Development environment settings"""
    debug: bool = True
    reload: bool = True
    log_level: str = "DEBUG"
    cors_origins: List[str] = ["*"]


class ProductionSettings(Settings):
    """Production environment settings"""
    debug: bool = False
    reload: bool = False
    log_level: str = "INFO"
    
    # Enhanced security for production
    @validator('secret_key')
    def validate_secret_key(cls, v):
        if len(v) < 32:
            raise ValueError('Secret key must be at least 32 characters long')
        return v


class TestingSettings(Settings):
    """Testing environment settings"""
    testing: bool = True
    postgres_db: str = "aura_intelligence_test"
    neo4j_database: str = "test"
    redis_db: int = 15  # Use different Redis DB for tests


@lru_cache()
def get_settings() -> Settings:
    """Get settings based on environment"""
    env = os.getenv("ENVIRONMENT", "development").lower()
    
    if env == "production":
        return ProductionSettings()
    elif env == "testing":
        return TestingSettings()
    else:
        return DevelopmentSettings()


# Export settings instance
settings = get_settings()
