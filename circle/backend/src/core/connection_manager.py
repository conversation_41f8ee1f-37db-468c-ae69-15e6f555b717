"""
🔗 AURA Intelligence Connection Manager
Solid engineering connection between Frontend, Backend, and Brain
"""

import asyncio
import json
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum
import uuid
import weakref

import structlog
from fastapi import WebSocket
import redis.asyncio as redis

logger = structlog.get_logger("aura.connection")


class ConnectionType(str, Enum):
    """Types of connections in the system"""
    FRONTEND_WEBSOCKET = "frontend_websocket"
    BACKEND_API = "backend_api"
    BRAIN_PIPELINE = "brain_pipeline"
    MEMORY_STREAM = "memory_stream"


@dataclass
class ConnectionContext:
    """Context for each connection"""
    connection_id: str
    connection_type: ConnectionType
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.utcnow)
    last_activity: datetime = field(default_factory=datetime.utcnow)
    
    # Memory and state
    memory_buffer: List[Dict[str, Any]] = field(default_factory=list)
    context_state: Dict[str, Any] = field(default_factory=dict)
    preferences: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Message:
    """Message structure for inter-system communication"""
    message_id: str
    source: str
    destination: str
    message_type: str
    payload: Dict[str, Any]
    context: Optional[ConnectionContext] = None
    timestamp: datetime = field(default_factory=datetime.utcnow)
    priority: int = 1  # 1=low, 5=high
    requires_response: bool = False
    correlation_id: Optional[str] = None


class ConnectionManager:
    """
    Manages all connections between Frontend, Backend, and Brain
    
    Features:
    - WebSocket connections for real-time updates
    - Memory pipeline for context preservation
    - Message routing between systems
    - Connection health monitoring
    - Context engineering and state management
    """
    
    def __init__(self, redis_client: Optional[redis.Redis] = None):
        self.logger = logger
        
        # Connection storage
        self.active_connections: Dict[str, ConnectionContext] = {}
        self.websocket_connections: Dict[str, WebSocket] = {}
        
        # Message routing
        self.message_handlers: Dict[str, List[Callable]] = {}
        self.message_queue: asyncio.Queue = asyncio.Queue()
        
        # Memory and context
        self.redis_client = redis_client
        self.memory_pipeline: Dict[str, List[Dict[str, Any]]] = {}
        self.context_cache: Dict[str, Dict[str, Any]] = {}
        
        # System health
        self.connection_stats = {
            "total_connections": 0,
            "active_websockets": 0,
            "messages_processed": 0,
            "errors": 0
        }
        
        # Start background tasks
        self._message_processor_task = None
        self._health_monitor_task = None
    
    async def initialize(self):
        """Initialize the connection manager"""
        try:
            self.logger.info("🔗 Initializing Connection Manager")
            
            # Start message processor
            self._message_processor_task = asyncio.create_task(
                self._process_messages()
            )
            
            # Start health monitor
            self._health_monitor_task = asyncio.create_task(
                self._monitor_connections()
            )
            
            self.logger.info("✅ Connection Manager initialized")
            
        except Exception as e:
            self.logger.error("❌ Failed to initialize Connection Manager", error=str(e))
            raise
    
    # === FRONTEND CONNECTION METHODS ===
    
    async def connect_frontend(self, websocket: WebSocket, user_id: str = None) -> str:
        """Connect frontend via WebSocket"""
        connection_id = f"frontend_{uuid.uuid4().hex[:8]}"
        
        try:
            # Accept WebSocket connection
            await websocket.accept()
            
            # Create connection context
            context = ConnectionContext(
                connection_id=connection_id,
                connection_type=ConnectionType.FRONTEND_WEBSOCKET,
                user_id=user_id,
                session_id=f"session_{uuid.uuid4().hex[:8]}"
            )
            
            # Store connections
            self.active_connections[connection_id] = context
            self.websocket_connections[connection_id] = websocket
            
            # Initialize memory pipeline for this connection
            self.memory_pipeline[connection_id] = []
            
            # Update stats
            self.connection_stats["total_connections"] += 1
            self.connection_stats["active_websockets"] += 1
            
            self.logger.info("🌐 Frontend connected", 
                           connection_id=connection_id, user_id=user_id)
            
            # Send welcome message
            await self.send_to_frontend(connection_id, {
                "type": "connection_established",
                "connection_id": connection_id,
                "session_id": context.session_id,
                "timestamp": datetime.utcnow().isoformat()
            })
            
            return connection_id
            
        except Exception as e:
            self.logger.error("❌ Failed to connect frontend", error=str(e))
            raise
    
    async def disconnect_frontend(self, connection_id: str):
        """Disconnect frontend WebSocket"""
        try:
            # Close WebSocket
            if connection_id in self.websocket_connections:
                websocket = self.websocket_connections[connection_id]
                await websocket.close()
                del self.websocket_connections[connection_id]
            
            # Clean up connection context
            if connection_id in self.active_connections:
                context = self.active_connections[connection_id]
                
                # Save memory to Redis if available
                if self.redis_client and connection_id in self.memory_pipeline:
                    await self._save_memory_to_redis(connection_id, context)
                
                del self.active_connections[connection_id]
            
            # Clean up memory pipeline
            if connection_id in self.memory_pipeline:
                del self.memory_pipeline[connection_id]
            
            # Update stats
            self.connection_stats["active_websockets"] -= 1
            
            self.logger.info("🔌 Frontend disconnected", connection_id=connection_id)
            
        except Exception as e:
            self.logger.error("❌ Failed to disconnect frontend", 
                            connection_id=connection_id, error=str(e))
    
    async def send_to_frontend(self, connection_id: str, data: Dict[str, Any]):
        """Send data to frontend via WebSocket"""
        try:
            if connection_id not in self.websocket_connections:
                self.logger.warning("Frontend connection not found", 
                                  connection_id=connection_id)
                return False
            
            websocket = self.websocket_connections[connection_id]
            
            # Add metadata
            message = {
                "timestamp": datetime.utcnow().isoformat(),
                "connection_id": connection_id,
                **data
            }
            
            # Send message
            await websocket.send_text(json.dumps(message))
            
            # Update context
            if connection_id in self.active_connections:
                self.active_connections[connection_id].last_activity = datetime.utcnow()
            
            return True
            
        except Exception as e:
            self.logger.error("❌ Failed to send to frontend", 
                            connection_id=connection_id, error=str(e))
            return False
    
    # === BACKEND CONNECTION METHODS ===
    
    async def process_backend_request(self, request_data: Dict[str, Any], 
                                    connection_id: str = None) -> Dict[str, Any]:
        """Process request from backend API"""
        try:
            # Create message for brain processing
            message = Message(
                message_id=f"backend_{uuid.uuid4().hex[:8]}",
                source="backend",
                destination="brain",
                message_type=request_data.get("type", "analysis_request"),
                payload=request_data,
                requires_response=True
            )
            
            # Add to message queue
            await self.message_queue.put(message)
            
            # If connected to frontend, send update
            if connection_id and connection_id in self.websocket_connections:
                await self.send_to_frontend(connection_id, {
                    "type": "processing_started",
                    "message_id": message.message_id,
                    "request_type": message.message_type
                })
            
            self.logger.info("📨 Backend request queued", 
                           message_id=message.message_id,
                           message_type=message.message_type)
            
            return {
                "status": "queued",
                "message_id": message.message_id,
                "estimated_processing_time": "5-30 seconds"
            }
            
        except Exception as e:
            self.logger.error("❌ Failed to process backend request", error=str(e))
            raise
    
    # === BRAIN CONNECTION METHODS ===
    
    async def send_to_brain(self, message: Message) -> Optional[Dict[str, Any]]:
        """Send message to brain (AI core) for processing"""
        try:
            # Import brain components dynamically
            from src.aura_intelligence.core.architecture import AURAArchitecture
            from src.aura_intelligence.advanced.topological_vulnerability_scanner import TopologicalVulnerabilityScanner
            
            # Route message to appropriate brain component
            if message.message_type == "topology_analysis":
                result = await self._process_topology_analysis(message)
            elif message.message_type == "incident_analysis":
                result = await self._process_incident_analysis(message)
            elif message.message_type == "prediction_request":
                result = await self._process_prediction_request(message)
            else:
                result = {"error": f"Unknown message type: {message.message_type}"}
            
            # Add processing metadata
            result["message_id"] = message.message_id
            result["processing_time"] = (datetime.utcnow() - message.timestamp).total_seconds()
            result["processed_by"] = "brain"
            
            self.logger.info("🧠 Brain processing completed", 
                           message_id=message.message_id,
                           processing_time=result["processing_time"])
            
            return result
            
        except Exception as e:
            self.logger.error("❌ Brain processing failed", 
                            message_id=message.message_id, error=str(e))
            return {
                "error": str(e),
                "message_id": message.message_id,
                "status": "failed"
            }
    
    async def _process_topology_analysis(self, message: Message) -> Dict[str, Any]:
        """Process topology analysis using brain components"""
        # This will use the existing topological vulnerability scanner
        payload = message.payload
        
        # Mock implementation - will be replaced with actual brain integration
        return {
            "type": "topology_analysis_result",
            "betti_numbers": {"beta_0": 3, "beta_1": 2, "beta_2": 0},
            "anomaly_detected": True,
            "anomaly_score": 0.75,
            "vulnerability_signatures": ["cascade_failure", "resource_exhaustion"],
            "confidence": 0.85
        }
    
    async def _process_incident_analysis(self, message: Message) -> Dict[str, Any]:
        """Process incident analysis using brain components"""
        # This will use the existing AI/ML components
        payload = message.payload
        
        # Mock implementation - will be replaced with actual brain integration
        return {
            "type": "incident_analysis_result",
            "root_causes": ["database_connection_pool", "memory_leak"],
            "recommended_actions": ["restart_service", "scale_up"],
            "impact_assessment": "high",
            "confidence": 0.88
        }
    
    async def _process_prediction_request(self, message: Message) -> Dict[str, Any]:
        """Process prediction request using brain components"""
        # This will use the existing predictive modeling
        payload = message.payload
        
        # Mock implementation - will be replaced with actual brain integration
        return {
            "type": "prediction_result",
            "failure_probability": 0.72,
            "time_to_failure": 1800,  # 30 minutes
            "affected_services": ["payment-service", "user-service"],
            "confidence": 0.79
        }
    
    # === MEMORY PIPELINE METHODS ===
    
    async def add_to_memory(self, connection_id: str, data: Dict[str, Any]):
        """Add data to connection memory pipeline"""
        if connection_id not in self.memory_pipeline:
            self.memory_pipeline[connection_id] = []
        
        memory_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "data": data,
            "entry_id": uuid.uuid4().hex[:8]
        }
        
        self.memory_pipeline[connection_id].append(memory_entry)
        
        # Keep only last 100 entries per connection
        if len(self.memory_pipeline[connection_id]) > 100:
            self.memory_pipeline[connection_id] = self.memory_pipeline[connection_id][-100:]
    
    async def get_memory_context(self, connection_id: str) -> List[Dict[str, Any]]:
        """Get memory context for a connection"""
        return self.memory_pipeline.get(connection_id, [])
    
    async def _save_memory_to_redis(self, connection_id: str, context: ConnectionContext):
        """Save memory pipeline to Redis for persistence"""
        if not self.redis_client:
            return
        
        try:
            memory_data = {
                "connection_context": {
                    "user_id": context.user_id,
                    "session_id": context.session_id,
                    "metadata": context.metadata,
                    "preferences": context.preferences
                },
                "memory_pipeline": self.memory_pipeline.get(connection_id, []),
                "saved_at": datetime.utcnow().isoformat()
            }
            
            await self.redis_client.setex(
                f"memory:{connection_id}",
                3600,  # 1 hour TTL
                json.dumps(memory_data)
            )
            
            self.logger.info("💾 Memory saved to Redis", connection_id=connection_id)
            
        except Exception as e:
            self.logger.error("❌ Failed to save memory to Redis", 
                            connection_id=connection_id, error=str(e))
    
    # === BACKGROUND TASKS ===
    
    async def _process_messages(self):
        """Background task to process message queue"""
        while True:
            try:
                # Get message from queue
                message = await self.message_queue.get()
                
                # Process message
                if message.destination == "brain":
                    result = await self.send_to_brain(message)
                    
                    # Send result back if response required
                    if message.requires_response and result:
                        # Find connection to send result to
                        for conn_id, websocket in self.websocket_connections.items():
                            await self.send_to_frontend(conn_id, {
                                "type": "processing_result",
                                "original_message_id": message.message_id,
                                "result": result
                            })
                
                # Update stats
                self.connection_stats["messages_processed"] += 1
                
            except Exception as e:
                self.logger.error("❌ Message processing error", error=str(e))
                self.connection_stats["errors"] += 1
                await asyncio.sleep(1)
    
    async def _monitor_connections(self):
        """Background task to monitor connection health"""
        while True:
            try:
                current_time = datetime.utcnow()
                
                # Check for stale connections
                stale_connections = []
                for conn_id, context in self.active_connections.items():
                    if (current_time - context.last_activity).total_seconds() > 300:  # 5 minutes
                        stale_connections.append(conn_id)
                
                # Clean up stale connections
                for conn_id in stale_connections:
                    await self.disconnect_frontend(conn_id)
                
                # Log stats
                self.logger.info("📊 Connection stats", **self.connection_stats)
                
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                self.logger.error("❌ Connection monitoring error", error=str(e))
                await asyncio.sleep(60)
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get connection manager statistics"""
        return {
            **self.connection_stats,
            "active_connections": len(self.active_connections),
            "memory_pipelines": len(self.memory_pipeline),
            "queue_size": self.message_queue.qsize(),
            "timestamp": datetime.utcnow().isoformat()
        }
    
    async def cleanup(self):
        """Cleanup connection manager"""
        # Cancel background tasks
        if self._message_processor_task:
            self._message_processor_task.cancel()
        if self._health_monitor_task:
            self._health_monitor_task.cancel()
        
        # Close all WebSocket connections
        for conn_id in list(self.websocket_connections.keys()):
            await self.disconnect_frontend(conn_id)
        
        self.logger.info("🧹 Connection Manager cleaned up")
