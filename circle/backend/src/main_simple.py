"""
🚀 AURA Intelligence Backend - Simplified Version
FastAPI server with WebSocket streaming for consciousness dashboard
"""

import asyncio
import logging
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
import uvicorn

from src.api.routes import websocket
from src.api.websocket import websocket_manager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("aura.main")

# Global services (simplified)
services: Dict[str, Any] = {}

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management"""
    logger.info("🚀 Starting AURA Intelligence Backend (Simplified)")
    
    try:
        # Initialize WebSocket manager
        logger.info("🌐 Initializing WebSocket Manager")
        
        # Start WebSocket streaming
        await websocket_manager._start_streaming()
        
        logger.info("✅ All services initialized successfully")
        
        yield
        
    except Exception as e:
        logger.error("❌ Failed to initialize services", error=str(e))
        raise
    finally:
        # Cleanup
        logger.info("🧹 Shutting down services")
        await websocket_manager.stop_streaming()
        logger.info("✅ Services cleaned up")

# Create FastAPI application
app = FastAPI(
    title="AURA Intelligence API",
    description="Advanced AIOps platform with WebSocket streaming",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(GZipMiddleware, minimum_size=1000)

# Include WebSocket router
app.include_router(websocket.router, tags=["websocket"])

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "🚀 AURA Intelligence API (Simplified)",
        "version": "1.0.0",
        "status": "operational",
        "websocket_endpoints": [
            "/ws/tda",
            "/ws/agents", 
            "/ws/patterns",
            "/ws/health",
            "/ws/audit"
        ]
    }

@app.get("/api/v1/status")
async def get_status():
    """Get system status"""
    try:
        metrics = await websocket_manager.get_metrics()
        return {
            "status": "healthy",
            "websocket_metrics": metrics,
            "timestamp": "2025-01-28T05:10:00Z"
        }
    except Exception as e:
        logger.error("Status check failed", error=str(e))
        return {
            "status": "error",
            "error": str(e),
            "timestamp": "2025-01-28T05:10:00Z"
        }

if __name__ == "__main__":
    uvicorn.run(
        "main_simple:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    ) 