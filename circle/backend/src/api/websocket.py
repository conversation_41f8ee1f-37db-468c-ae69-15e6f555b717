"""
🌐 AURA Intelligence WebSocket Streaming API
Real-time data streaming for consciousness dashboard
"""

import asyncio
import json
import uuid
from typing import Dict, Any, Optional, List, Set
from datetime import datetime, timedelta
from enum import Enum
import logging

from fastapi import WebSocket, WebSocketDisconnect, HTTPException
from fastapi.responses import HTMLResponse
from starlette.websockets import WebSocketState

from src.core.config import get_settings
from src.services.topology_analyzer import TopologyAnalyzer
from src.services.agent_orchestrator import AgentOrchestrator
from src.services.knowledge_graph import KnowledgeGraphService
from src.models.topology import TopologySnapshot, TopologyFeatures
from src.models.agents import Agent, AgentType, AgentStatus


class ConnectionType(str, Enum):
    """WebSocket connection types"""
    TDA_STREAM = "tda"
    AGENT_STATUS = "agents"
    PATTERN_ANALYSIS = "patterns"
    SYSTEM_HEALTH = "health"
    AUDIT_TRAIL = "audit"


class WebSocketManager:
    """Manages WebSocket connections and real-time data streaming"""
    
    def __init__(self):
        self.logger = logging.getLogger("aura.websocket")
        self.settings = get_settings()
        
        # Connection management
        self.active_connections: Dict[ConnectionType, Set[WebSocket]] = {
            conn_type: set() for conn_type in ConnectionType
        }
        
        # Service references (injected during startup)
        self.topology_analyzer: Optional[TopologyAnalyzer] = None
        self.agent_orchestrator: Optional[AgentOrchestrator] = None
        self.knowledge_graph: Optional[KnowledgeGraphService] = None
        
        # Streaming state
        self.is_streaming = False
        self.stream_tasks: Dict[ConnectionType, asyncio.Task] = {}
        
        # Performance metrics
        self.metrics = {
            "total_connections": 0,
            "active_connections": 0,
            "messages_sent": 0,
            "errors": 0
        }
    
    def set_services(self, topology_analyzer: TopologyAnalyzer, 
                    agent_orchestrator: AgentOrchestrator,
                    knowledge_graph: KnowledgeGraphService):
        """Set service references"""
        self.topology_analyzer = topology_analyzer
        self.agent_orchestrator = agent_orchestrator
        self.knowledge_graph = knowledge_graph
    
    async def connect(self, websocket: WebSocket, connection_type: ConnectionType):
        """Accept WebSocket connection"""
        try:
            await websocket.accept()
            
            # Add to active connections
            self.active_connections[connection_type].add(websocket)
            self.metrics["total_connections"] += 1
            self.metrics["active_connections"] += 1
            
            # Start streaming if not already active
            if not self.is_streaming:
                await self._start_streaming()
            
            self.logger.info("🔌 WebSocket connected", 
                           connection_type=connection_type.value,
                           total_connections=self.metrics["active_connections"])
            
            # Send initial data
            await self._send_initial_data(websocket, connection_type)
            
            # Keep connection alive
            await self._handle_connection(websocket, connection_type)
            
        except Exception as e:
            self.logger.error("❌ WebSocket connection failed", error=str(e))
            self.metrics["errors"] += 1
            raise
    
    async def disconnect(self, websocket: WebSocket, connection_type: ConnectionType):
        """Handle WebSocket disconnection"""
        try:
            self.active_connections[connection_type].discard(websocket)
            self.metrics["active_connections"] -= 1
            
            self.logger.info("🔌 WebSocket disconnected", 
                           connection_type=connection_type.value,
                           active_connections=self.metrics["active_connections"])
            
        except Exception as e:
            self.logger.error("❌ WebSocket disconnect error", error=str(e))
    
    async def _handle_connection(self, websocket: WebSocket, connection_type: ConnectionType):
        """Handle WebSocket connection lifecycle"""
        try:
            while websocket.client_state != WebSocketState.DISCONNECTED:
                # Wait for messages (ping/pong for keepalive)
                try:
                    data = await websocket.receive_text()
                    message = json.loads(data)
                    
                    # Handle client messages
                    await self._handle_client_message(websocket, connection_type, message)
                    
                except WebSocketDisconnect:
                    break
                except Exception as e:
                    self.logger.error("❌ WebSocket message error", error=str(e))
                    break
                    
        finally:
            await self.disconnect(websocket, connection_type)
    
    async def _handle_client_message(self, websocket: WebSocket, 
                                   connection_type: ConnectionType, message: Dict[str, Any]):
        """Handle client messages"""
        try:
            message_type = message.get("type")
            
            if message_type == "ping":
                await websocket.send_text(json.dumps({"type": "pong", "timestamp": datetime.now().isoformat()}))
            
            elif message_type == "request_data":
                await self._send_data_update(websocket, connection_type)
            
            elif message_type == "agent_control":
                await self._handle_agent_control(message)
            
            elif message_type == "pattern_analysis":
                await self._handle_pattern_analysis_request(websocket, message)
            
        except Exception as e:
            self.logger.error("❌ Client message handling error", error=str(e))
            await websocket.send_text(json.dumps({
                "type": "error",
                "message": "Failed to process message",
                "timestamp": datetime.now().isoformat()
            }))
    
    async def _start_streaming(self):
        """Start real-time data streaming"""
        if self.is_streaming:
            return
        
        self.is_streaming = True
        self.logger.info("🚀 Starting real-time data streaming")
        
        # Start streaming tasks for each connection type
        for connection_type in ConnectionType:
            task = asyncio.create_task(self._stream_data(connection_type))
            self.stream_tasks[connection_type] = task
    
    async def _stream_data(self, connection_type: ConnectionType):
        """Stream data for specific connection type"""
        try:
            while self.is_streaming:
                # Generate data based on connection type
                data = await self._generate_stream_data(connection_type)
                
                # Send to all connected clients
                await self._broadcast_data(connection_type, data)
                
                # Wait before next update
                await asyncio.sleep(self._get_update_interval(connection_type))
                
        except Exception as e:
            self.logger.error(f"❌ Streaming error for {connection_type.value}", error=str(e))
            self.metrics["errors"] += 1
    
    async def _generate_stream_data(self, connection_type: ConnectionType) -> Dict[str, Any]:
        """Generate streaming data based on connection type"""
        timestamp = datetime.now().isoformat()
        
        if connection_type == ConnectionType.TDA_STREAM:
            return await self._generate_tda_data(timestamp)
        
        elif connection_type == ConnectionType.AGENT_STATUS:
            return await self._generate_agent_data(timestamp)
        
        elif connection_type == ConnectionType.PATTERN_ANALYSIS:
            return await self._generate_pattern_data(timestamp)
        
        elif connection_type == ConnectionType.SYSTEM_HEALTH:
            return await self._generate_health_data(timestamp)
        
        elif connection_type == ConnectionType.AUDIT_TRAIL:
            return await self._generate_audit_data(timestamp)
        
        return {"type": "unknown", "timestamp": timestamp}
    
    async def _generate_tda_data(self, timestamp: str) -> Dict[str, Any]:
        """Generate TDA streaming data"""
        try:
            if not self.topology_analyzer:
                return {"type": "tda", "timestamp": timestamp, "error": "Topology analyzer not available"}
            
            # Generate synthetic topology data for demo
            # In production, this would come from real system metrics
            topology_data = {
                "type": "tda",
                "timestamp": timestamp,
                "topologicalData": {
                    "anomalyScore": self._generate_anomaly_score(),
                    "bettiNumbers": self._generate_betti_numbers(),
                    "persistenceEntropy": self._generate_entropy(),
                    "patternClassification": self._generate_pattern_classification(),
                    "confidence": self._generate_confidence(),
                    "recommendedAgent": self._generate_recommended_agent(),
                    "routingPriority": self._generate_routing_priority(),
                    "persistenceDiagram": self._generate_persistence_diagram()
                }
            }
            
            return topology_data
            
        except Exception as e:
            self.logger.error("❌ TDA data generation error", error=str(e))
            return {"type": "tda", "timestamp": timestamp, "error": str(e)}
    
    async def _generate_agent_data(self, timestamp: str) -> Dict[str, Any]:
        """Generate agent status data"""
        try:
            if not self.agent_orchestrator:
                return {"type": "agents", "timestamp": timestamp, "error": "Agent orchestrator not available"}
            
            # Get agent status from orchestrator
            agents = []
            for agent_type in AgentType:
                agent = self.agent_orchestrator.agents.get(agent_type)
                if agent:
                    agents.append({
                        "id": agent.agent_type.value,
                        "name": agent.name,
                        "status": self.agent_orchestrator.agent_status.get(agent_type, "unknown").value,
                        "capabilities": agent.capabilities,
                        "lastActivity": timestamp,
                        "performance": {
                            "tasksCompleted": 0,  # Would be real metrics
                            "averageLatency": 0.1,
                            "successRate": 0.95
                        }
                    })
            
            return {
                "type": "agents",
                "timestamp": timestamp,
                "agents": agents,
                "workflowState": "active",
                "routingDecision": "auto"
            }
            
        except Exception as e:
            self.logger.error("❌ Agent data generation error", error=str(e))
            return {"type": "agents", "timestamp": timestamp, "error": str(e)}
    
    async def _generate_pattern_data(self, timestamp: str) -> Dict[str, Any]:
        """Generate pattern analysis data"""
        try:
            return {
                "type": "patterns",
                "timestamp": timestamp,
                "causalPatterns": [
                    {
                        "id": f"pattern_{uuid.uuid4().hex[:8]}",
                        "confidence": 0.85,
                        "description": "Service dependency chain anomaly",
                        "impact": "high",
                        "recommendation": "Check database connection pool"
                    }
                ],
                "temporalPatterns": [
                    {
                        "id": f"temp_{uuid.uuid4().hex[:8]}",
                        "trend": "increasing",
                        "duration": "5m",
                        "significance": 0.7
                    }
                ]
            }
            
        except Exception as e:
            self.logger.error("❌ Pattern data generation error", error=str(e))
            return {"type": "patterns", "timestamp": timestamp, "error": str(e)}
    
    async def _generate_health_data(self, timestamp: str) -> Dict[str, Any]:
        """Generate system health data"""
        try:
            return {
                "type": "health",
                "timestamp": timestamp,
                "systemHealth": {
                    "overall": "healthy",
                    "cpu": 0.45,
                    "memory": 0.62,
                    "disk": 0.23,
                    "network": 0.18
                },
                "alerts": [],
                "metrics": {
                    "requestsPerSecond": 1250,
                    "averageResponseTime": 45,
                    "errorRate": 0.02
                }
            }
            
        except Exception as e:
            self.logger.error("❌ Health data generation error", error=str(e))
            return {"type": "health", "timestamp": timestamp, "error": str(e)}
    
    async def _generate_audit_data(self, timestamp: str) -> Dict[str, Any]:
        """Generate audit trail data"""
        try:
            return {
                "type": "audit",
                "timestamp": timestamp,
                "auditTrail": [
                    {
                        "id": f"audit_{uuid.uuid4().hex[:8]}",
                        "action": "agent_activated",
                        "agent": "topology_analyzer",
                        "timestamp": timestamp,
                        "operator": "system",
                        "reason": "Anomaly detected"
                    }
                ]
            }
            
        except Exception as e:
            self.logger.error("❌ Audit data generation error", error=str(e))
            return {"type": "audit", "timestamp": timestamp, "error": str(e)}
    
    async def _broadcast_data(self, connection_type: ConnectionType, data: Dict[str, Any]):
        """Broadcast data to all connected clients"""
        if not self.active_connections[connection_type]:
            return
        
        message = json.dumps(data)
        disconnected = set()
        
        for websocket in self.active_connections[connection_type]:
            try:
                await websocket.send_text(message)
                self.metrics["messages_sent"] += 1
            except Exception as e:
                self.logger.error("❌ Failed to send message", error=str(e))
                disconnected.add(websocket)
        
        # Remove disconnected websockets
        for websocket in disconnected:
            await self.disconnect(websocket, connection_type)
    
    async def _send_initial_data(self, websocket: WebSocket, connection_type: ConnectionType):
        """Send initial data when client connects"""
        try:
            data = await self._generate_stream_data(connection_type)
            data["type"] = "initial"
            await websocket.send_text(json.dumps(data))
            
        except Exception as e:
            self.logger.error("❌ Failed to send initial data", error=str(e))
    
    def _get_update_interval(self, connection_type: ConnectionType) -> float:
        """Get update interval for connection type"""
        intervals = {
            ConnectionType.TDA_STREAM: 1.0,      # 1 second
            ConnectionType.AGENT_STATUS: 2.0,     # 2 seconds
            ConnectionType.PATTERN_ANALYSIS: 5.0, # 5 seconds
            ConnectionType.SYSTEM_HEALTH: 3.0,    # 3 seconds
            ConnectionType.AUDIT_TRAIL: 10.0      # 10 seconds
        }
        return intervals.get(connection_type, 5.0)
    
    # Synthetic data generators for demo
    def _generate_anomaly_score(self) -> float:
        """Generate synthetic anomaly score"""
        import random
        return random.uniform(0.1, 0.9)
    
    def _generate_betti_numbers(self) -> List[int]:
        """Generate synthetic Betti numbers"""
        import random
        return [random.randint(0, 10), random.randint(0, 15), random.randint(0, 8)]
    
    def _generate_entropy(self) -> float:
        """Generate synthetic persistence entropy"""
        import random
        return random.uniform(0.5, 2.5)
    
    def _generate_pattern_classification(self) -> str:
        """Generate synthetic pattern classification"""
        import random
        patterns = ["normal", "warning", "critical", "anomalous", "stable"]
        return random.choice(patterns)
    
    def _generate_confidence(self) -> float:
        """Generate synthetic confidence score"""
        import random
        return random.uniform(0.6, 0.95)
    
    def _generate_recommended_agent(self) -> str:
        """Generate synthetic recommended agent"""
        import random
        agents = ["monitor", "analyze", "remediate", "investigate", "synthesize"]
        return random.choice(agents)
    
    def _generate_routing_priority(self) -> str:
        """Generate synthetic routing priority"""
        import random
        priorities = ["low", "medium", "high", "critical"]
        return random.choice(priorities)
    
    def _generate_persistence_diagram(self) -> Dict[str, Any]:
        """Generate synthetic persistence diagram"""
        import random
        return {
            "dimensions": [0, 1, 2],
            "persistence_pairs": [
                {"birth": random.uniform(0, 1), "death": random.uniform(1, 2)} 
                for _ in range(random.randint(5, 15))
            ]
        }
    
    async def _handle_agent_control(self, message: Dict[str, Any]):
        """Handle agent control messages"""
        try:
            action = message.get("action")
            agent_id = message.get("agentId")
            
            if action == "pause" and self.agent_orchestrator:
                # Pause agent logic
                pass
            elif action == "resume" and self.agent_orchestrator:
                # Resume agent logic
                pass
            
        except Exception as e:
            self.logger.error("❌ Agent control error", error=str(e))
    
    async def _handle_pattern_analysis_request(self, websocket: WebSocket, message: Dict[str, Any]):
        """Handle pattern analysis requests"""
        try:
            # Generate detailed pattern analysis
            analysis = await self._generate_pattern_data(datetime.now().isoformat())
            analysis["type"] = "pattern_analysis_response"
            analysis["requestId"] = message.get("requestId")
            
            await websocket.send_text(json.dumps(analysis))
            
        except Exception as e:
            self.logger.error("❌ Pattern analysis error", error=str(e))
    
    async def _send_data_update(self, websocket: WebSocket, connection_type: ConnectionType):
        """Send immediate data update"""
        try:
            data = await self._generate_stream_data(connection_type)
            data["type"] = "data_update"
            await websocket.send_text(json.dumps(data))
            
        except Exception as e:
            self.logger.error("❌ Data update error", error=str(e))
    
    async def stop_streaming(self):
        """Stop all streaming tasks"""
        self.is_streaming = False
        
        # Cancel all streaming tasks
        for task in self.stream_tasks.values():
            task.cancel()
        
        # Wait for tasks to complete
        if self.stream_tasks:
            await asyncio.gather(*self.stream_tasks.values(), return_exceptions=True)
        
        self.stream_tasks.clear()
        self.logger.info("🛑 Streaming stopped")
    
    async def get_metrics(self) -> Dict[str, Any]:
        """Get WebSocket metrics"""
        return {
            **self.metrics,
            "connection_types": {
                conn_type.value: len(connections) 
                for conn_type, connections in self.active_connections.items()
            }
        }


# Global WebSocket manager instance
websocket_manager = WebSocketManager() 