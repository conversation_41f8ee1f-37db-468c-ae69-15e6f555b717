"""
🏥 AURA Intelligence Health Check Routes
System health monitoring and diagnostics endpoints
"""

from datetime import datetime
from typing import Dict, Any

from fastapi import <PERSON><PERSON>outer, Depends, HTTPException
import structlog

from src.services.knowledge_graph import KnowledgeGraphService
from src.services.topology_analyzer import TopologyAnalyzer
from src.services.agent_orchestrator import AgentOrchestrator
from src.services.prediction_engine import PredictionEngine
from src.main import (
    get_knowledge_graph_service,
    get_topology_analyzer, 
    get_agent_orchestrator,
    get_prediction_engine
)

router = APIRouter()
logger = structlog.get_logger("aura.api.health")


@router.get("/health")
async def health_check():
    """Basic health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "AURA Intelligence API",
        "version": "1.0.0"
    }


@router.get("/health/detailed")
async def detailed_health_check(
    kg_service: KnowledgeGraphService = Depends(get_knowledge_graph_service),
    topology_analyzer: TopologyAnalyzer = Depends(get_topology_analyzer),
    agent_orchestrator: AgentOrchestrator = Depends(get_agent_orchestrator),
    prediction_engine: PredictionEngine = Depends(get_prediction_engine)
):
    """Detailed health check for all services"""
    
    health_status = {
        "overall_status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "services": {}
    }
    
    services_healthy = 0
    total_services = 4
    
    # Knowledge Graph Service
    try:
        kg_health = await kg_service.health_check()
        health_status["services"]["knowledge_graph"] = kg_health
        if kg_health.get("status") == "healthy":
            services_healthy += 1
    except Exception as e:
        logger.error("Knowledge Graph health check failed", error=str(e))
        health_status["services"]["knowledge_graph"] = {
            "status": "unhealthy",
            "error": str(e)
        }
    
    # Topology Analyzer
    try:
        topology_health = await topology_analyzer.health_check()
        health_status["services"]["topology_analyzer"] = topology_health
        if topology_health.get("status") == "healthy":
            services_healthy += 1
    except Exception as e:
        logger.error("Topology Analyzer health check failed", error=str(e))
        health_status["services"]["topology_analyzer"] = {
            "status": "unhealthy",
            "error": str(e)
        }
    
    # Agent Orchestrator
    try:
        orchestrator_health = await agent_orchestrator.health_check()
        health_status["services"]["agent_orchestrator"] = orchestrator_health
        if orchestrator_health.get("status") == "healthy":
            services_healthy += 1
    except Exception as e:
        logger.error("Agent Orchestrator health check failed", error=str(e))
        health_status["services"]["agent_orchestrator"] = {
            "status": "unhealthy",
            "error": str(e)
        }
    
    # Prediction Engine
    try:
        prediction_health = await prediction_engine.health_check()
        health_status["services"]["prediction_engine"] = prediction_health
        if prediction_health.get("status") == "healthy":
            services_healthy += 1
    except Exception as e:
        logger.error("Prediction Engine health check failed", error=str(e))
        health_status["services"]["prediction_engine"] = {
            "status": "unhealthy",
            "error": str(e)
        }
    
    # Determine overall status
    if services_healthy == total_services:
        health_status["overall_status"] = "healthy"
    elif services_healthy > 0:
        health_status["overall_status"] = "degraded"
    else:
        health_status["overall_status"] = "unhealthy"
    
    health_status["services_healthy"] = services_healthy
    health_status["total_services"] = total_services
    health_status["health_percentage"] = (services_healthy / total_services) * 100
    
    return health_status


@router.get("/health/readiness")
async def readiness_check(
    kg_service: KnowledgeGraphService = Depends(get_knowledge_graph_service),
    topology_analyzer: TopologyAnalyzer = Depends(get_topology_analyzer),
    agent_orchestrator: AgentOrchestrator = Depends(get_agent_orchestrator),
    prediction_engine: PredictionEngine = Depends(get_prediction_engine)
):
    """Kubernetes readiness probe endpoint"""
    import asyncio

    try:
        # Quick health checks for all services
        checks = await asyncio.gather(
            kg_service.health_check(),
            topology_analyzer.health_check(),
            agent_orchestrator.health_check(),
            prediction_engine.health_check(),
            return_exceptions=True
        )

        # Check if any service is unhealthy
        for check in checks:
            if isinstance(check, Exception):
                raise HTTPException(status_code=503, detail="Service not ready")
            if check.get("status") != "healthy":
                raise HTTPException(status_code=503, detail="Service not ready")

        return {"status": "ready", "timestamp": datetime.utcnow().isoformat()}

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Readiness check failed", error=str(e))
        raise HTTPException(status_code=503, detail="Service not ready")


@router.get("/health/liveness")
async def liveness_check():
    """Kubernetes liveness probe endpoint"""
    return {
        "status": "alive",
        "timestamp": datetime.utcnow().isoformat()
    }


@router.get("/metrics/system")
async def system_metrics():
    """System performance metrics"""
    import psutil
    import os
    
    try:
        # CPU metrics
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        
        # Memory metrics
        memory = psutil.virtual_memory()
        
        # Disk metrics
        disk = psutil.disk_usage('/')
        
        # Process metrics
        process = psutil.Process(os.getpid())
        process_memory = process.memory_info()
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "cpu": {
                "percent": cpu_percent,
                "count": cpu_count
            },
            "memory": {
                "total": memory.total,
                "available": memory.available,
                "percent": memory.percent,
                "used": memory.used
            },
            "disk": {
                "total": disk.total,
                "used": disk.used,
                "free": disk.free,
                "percent": (disk.used / disk.total) * 100
            },
            "process": {
                "memory_rss": process_memory.rss,
                "memory_vms": process_memory.vms,
                "cpu_percent": process.cpu_percent(),
                "num_threads": process.num_threads()
            }
        }
        
    except Exception as e:
        logger.error("Failed to get system metrics", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get system metrics")


@router.get("/metrics/application")
async def application_metrics(
    kg_service: KnowledgeGraphService = Depends(get_knowledge_graph_service),
    topology_analyzer: TopologyAnalyzer = Depends(get_topology_analyzer),
    agent_orchestrator: AgentOrchestrator = Depends(get_agent_orchestrator)
):
    """Application-specific metrics"""
    
    try:
        # Get service-specific metrics
        kg_health = await kg_service.health_check()
        topology_health = await topology_analyzer.health_check()
        orchestrator_health = await agent_orchestrator.health_check()
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "knowledge_graph": {
                "node_counts": kg_health.get("node_counts", {}),
                "status": kg_health.get("status")
            },
            "topology_analyzer": {
                "cache_size": topology_health.get("cache_size", 0),
                "historical_features": topology_health.get("historical_features", 0),
                "gpu_enabled": topology_health.get("gpu_enabled", False)
            },
            "agent_orchestrator": {
                "agent_count": orchestrator_health.get("agent_count", 0),
                "active_tasks": orchestrator_health.get("active_tasks", 0),
                "execution_history": orchestrator_health.get("execution_history", 0)
            }
        }
        
    except Exception as e:
        logger.error("Failed to get application metrics", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get application metrics")


@router.get("/debug/info")
async def debug_info():
    """Debug information for troubleshooting"""
    import sys
    import platform
    import os

    try:
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "python": {
                "version": sys.version,
                "executable": sys.executable,
                "platform": platform.platform()
            },
            "environment": {
                "cwd": os.getcwd(),
                "pid": os.getpid(),
                "ppid": os.getppid()
            },
            "packages": {
                "fastapi": "0.104.1",
                "uvicorn": "0.24.0",
                "neo4j": "5.15.0",
                "torch": "2.1.1"
            }
        }

    except Exception as e:
        logger.error("Failed to get debug info", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get debug info")
