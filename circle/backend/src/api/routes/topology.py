"""
🔍 AURA Intelligence Topology Analysis API Routes
Topological data analysis and system topology endpoints
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel
import structlog

from src.services.topology_analyzer import TopologyAnalyzer
from src.models.topology import (
    TopologySnapshot, TopologyFeatures, AnomalyDetection,
    TopologyPrediction, TopologyAnalysisResult
)
from src.main import get_topology_analyzer

router = APIRouter()
logger = structlog.get_logger("aura.api.topology")


# === REQUEST/RESPONSE MODELS ===

class AnalyzeTopologyRequest(BaseModel):
    """Request to analyze system topology"""
    snapshot: TopologySnapshot


class TopologyAnalysisResponse(BaseModel):
    """Response from topology analysis"""
    analysis_id: str
    features: TopologyFeatures
    anomaly_detection: AnomalyDetection
    analysis_time: float
    timestamp: datetime


class TopologyHealthResponse(BaseModel):
    """Topology analyzer health response"""
    status: str
    gpu_enabled: bool
    cache_size: int
    historical_features: int
    timestamp: datetime


# === ANALYSIS ENDPOINTS ===

@router.post("/analyze", response_model=TopologyAnalysisResponse)
async def analyze_topology(
    request: AnalyzeTopologyRequest,
    topology_analyzer: TopologyAnalyzer = Depends(get_topology_analyzer)
):
    """Analyze system topology and detect anomalies"""
    
    analysis_id = f"topo_analysis_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
    start_time = datetime.utcnow()
    
    try:
        logger.info("Starting topology analysis", 
                   analysis_id=analysis_id,
                   snapshot_id=request.snapshot.id)
        
        # Analyze topology
        features = await topology_analyzer.analyze_topology(request.snapshot)
        
        # Detect anomalies
        anomaly_detection = await topology_analyzer.detect_anomalies(features)
        
        analysis_time = (datetime.utcnow() - start_time).total_seconds()
        
        logger.info("Topology analysis completed",
                   analysis_id=analysis_id,
                   anomaly_detected=anomaly_detection.is_anomaly,
                   anomaly_score=anomaly_detection.anomaly_score,
                   analysis_time=analysis_time)
        
        return TopologyAnalysisResponse(
            analysis_id=analysis_id,
            features=features,
            anomaly_detection=anomaly_detection,
            analysis_time=analysis_time,
            timestamp=datetime.utcnow()
        )
        
    except Exception as e:
        logger.error("Topology analysis failed", 
                    analysis_id=analysis_id, error=str(e))
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")


@router.get("/features/{snapshot_id}")
async def get_topology_features(
    snapshot_id: str,
    topology_analyzer: TopologyAnalyzer = Depends(get_topology_analyzer)
):
    """Get cached topology features for a snapshot"""
    
    try:
        # Check cache for features
        if snapshot_id in topology_analyzer.feature_cache:
            features = topology_analyzer.feature_cache[snapshot_id]
            
            logger.info("Topology features retrieved from cache", 
                       snapshot_id=snapshot_id)
            
            return {
                "snapshot_id": snapshot_id,
                "features": features,
                "cached": True,
                "timestamp": datetime.utcnow().isoformat()
            }
        else:
            raise HTTPException(status_code=404, detail="Features not found in cache")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get topology features", 
                    snapshot_id=snapshot_id, error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get features: {str(e)}")


@router.get("/anomalies")
async def get_recent_anomalies(
    limit: int = Query(default=10, ge=1, le=100),
    topology_analyzer: TopologyAnalyzer = Depends(get_topology_analyzer)
):
    """Get recent topology anomalies"""
    
    try:
        # This would query a database of anomalies in production
        # For now, return mock data based on historical features
        
        recent_anomalies = []
        
        # Check recent features for anomalies
        for features in topology_analyzer.historical_features[-limit:]:
            # Simple anomaly detection based on complexity
            if features.complexity_score > 2.0:
                anomaly = {
                    "snapshot_id": features.snapshot_id,
                    "timestamp": features.timestamp.isoformat(),
                    "anomaly_score": min(features.complexity_score / 5.0, 1.0),
                    "anomaly_type": "high_complexity",
                    "features": {
                        "complexity_score": features.complexity_score,
                        "betti_numbers": features.betti_numbers.dict()
                    }
                }
                recent_anomalies.append(anomaly)
        
        logger.info("Recent anomalies retrieved", count=len(recent_anomalies))
        
        return {
            "anomalies": recent_anomalies,
            "total_count": len(recent_anomalies),
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error("Failed to get recent anomalies", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get anomalies: {str(e)}")


# === STATISTICS ENDPOINTS ===

@router.get("/statistics")
async def get_topology_statistics(
    topology_analyzer: TopologyAnalyzer = Depends(get_topology_analyzer)
):
    """Get topology analysis statistics"""
    
    try:
        historical_features = topology_analyzer.historical_features
        
        if not historical_features:
            return {
                "message": "No historical data available",
                "timestamp": datetime.utcnow().isoformat()
            }
        
        # Calculate statistics
        complexity_scores = [f.complexity_score for f in historical_features]
        betti_0_values = [f.betti_numbers.beta_0 for f in historical_features]
        betti_1_values = [f.betti_numbers.beta_1 for f in historical_features]
        
        statistics = {
            "total_analyses": len(historical_features),
            "complexity_stats": {
                "mean": sum(complexity_scores) / len(complexity_scores),
                "min": min(complexity_scores),
                "max": max(complexity_scores)
            },
            "betti_0_stats": {
                "mean": sum(betti_0_values) / len(betti_0_values),
                "min": min(betti_0_values),
                "max": max(betti_0_values)
            },
            "betti_1_stats": {
                "mean": sum(betti_1_values) / len(betti_1_values),
                "min": min(betti_1_values),
                "max": max(betti_1_values)
            },
            "timestamp": datetime.utcnow().isoformat()
        }
        
        logger.info("Topology statistics calculated", 
                   total_analyses=statistics["total_analyses"])
        
        return statistics
        
    except Exception as e:
        logger.error("Failed to get topology statistics", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get statistics: {str(e)}")


@router.get("/trends")
async def get_topology_trends(
    hours: int = Query(default=24, ge=1, le=168),  # Max 1 week
    topology_analyzer: TopologyAnalyzer = Depends(get_topology_analyzer)
):
    """Get topology trends over time"""
    
    try:
        # Filter features by time window
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        recent_features = [
            f for f in topology_analyzer.historical_features
            if f.timestamp >= cutoff_time
        ]
        
        if not recent_features:
            return {
                "message": f"No data available for the last {hours} hours",
                "timestamp": datetime.utcnow().isoformat()
            }
        
        # Group by hour for trend analysis
        hourly_data = {}
        for features in recent_features:
            hour_key = features.timestamp.strftime('%Y-%m-%d %H:00')
            if hour_key not in hourly_data:
                hourly_data[hour_key] = []
            hourly_data[hour_key].append(features)
        
        # Calculate hourly averages
        trends = []
        for hour, features_list in sorted(hourly_data.items()):
            avg_complexity = sum(f.complexity_score for f in features_list) / len(features_list)
            avg_betti_1 = sum(f.betti_numbers.beta_1 for f in features_list) / len(features_list)
            
            trends.append({
                "timestamp": hour,
                "complexity_score": avg_complexity,
                "betti_1": avg_betti_1,
                "sample_count": len(features_list)
            })
        
        logger.info("Topology trends calculated", 
                   hours=hours, data_points=len(trends))
        
        return {
            "trends": trends,
            "time_window_hours": hours,
            "total_data_points": len(trends),
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error("Failed to get topology trends", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get trends: {str(e)}")


# === HEALTH AND STATUS ENDPOINTS ===

@router.get("/health", response_model=TopologyHealthResponse)
async def topology_health_check(
    topology_analyzer: TopologyAnalyzer = Depends(get_topology_analyzer)
):
    """Get topology analyzer health status"""
    
    try:
        health_data = await topology_analyzer.health_check()
        
        return TopologyHealthResponse(
            status=health_data["status"],
            gpu_enabled=health_data["gpu_enabled"],
            cache_size=health_data["cache_size"],
            historical_features=health_data["historical_features"],
            timestamp=datetime.utcnow()
        )
        
    except Exception as e:
        logger.error("Topology health check failed", error=str(e))
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")


@router.get("/cache/status")
async def get_cache_status(
    topology_analyzer: TopologyAnalyzer = Depends(get_topology_analyzer)
):
    """Get topology analysis cache status"""
    
    try:
        cache_info = {
            "feature_cache_size": len(topology_analyzer.feature_cache),
            "historical_features_count": len(topology_analyzer.historical_features),
            "vulnerability_signatures_count": len(topology_analyzer.vulnerability_signatures),
            "cache_keys": list(topology_analyzer.feature_cache.keys())[-10:],  # Last 10 keys
            "timestamp": datetime.utcnow().isoformat()
        }
        
        return cache_info
        
    except Exception as e:
        logger.error("Failed to get cache status", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get cache status: {str(e)}")


@router.delete("/cache")
async def clear_cache(
    topology_analyzer: TopologyAnalyzer = Depends(get_topology_analyzer)
):
    """Clear topology analysis cache"""
    
    try:
        # Clear caches
        cache_size_before = len(topology_analyzer.feature_cache)
        topology_analyzer.feature_cache.clear()
        
        # Keep only recent historical features (last 100)
        historical_count_before = len(topology_analyzer.historical_features)
        topology_analyzer.historical_features = topology_analyzer.historical_features[-100:]
        
        logger.info("Cache cleared", 
                   feature_cache_cleared=cache_size_before,
                   historical_features_kept=len(topology_analyzer.historical_features))
        
        return {
            "message": "Cache cleared successfully",
            "feature_cache_cleared": cache_size_before,
            "historical_features_before": historical_count_before,
            "historical_features_after": len(topology_analyzer.historical_features),
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error("Failed to clear cache", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to clear cache: {str(e)}")
