"""
🤖 AURA Intelligence Agent Orchestration API Routes
Multi-agent system control and incident analysis endpoints
"""

from datetime import datetime
from typing import Dict, List, Optional, Any

from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel
import structlog

from src.services.agent_orchestrator import AgentOrchestrator
from src.models.agents import (
    AgentType, IncidentAnalysis, RemediationPlan,
    AgentPerformanceMetrics, OrchestratorHealthCheck
)
from src.main import get_agent_orchestrator

router = APIRouter()
logger = structlog.get_logger("aura.api.agents")


# === REQUEST/RESPONSE MODELS ===

class AnalyzeIncidentRequest(BaseModel):
    """Request to analyze an incident using multi-agent system"""
    incident_data: Dict[str, Any]
    priority: str = "medium"
    timeout: int = 300  # 5 minutes default


class IncidentAnalysisResponse(BaseModel):
    """Response from incident analysis"""
    analysis: IncidentAnalysis
    execution_time: float
    agents_used: List[str]
    timestamp: datetime


class AgentStatusResponse(BaseModel):
    """Agent status response"""
    agent_type: str
    status: str
    last_execution: Optional[datetime]
    success_rate: float
    average_execution_time: float


# === INCIDENT ANALYSIS ENDPOINTS ===

@router.post("/analyze-incident", response_model=IncidentAnalysisResponse)
async def analyze_incident(
    request: AnalyzeIncidentRequest,
    orchestrator: AgentOrchestrator = Depends(get_agent_orchestrator)
):
    """Analyze incident using multi-agent system"""
    
    start_time = datetime.utcnow()
    
    try:
        logger.info("Starting incident analysis", 
                   incident_id=request.incident_data.get('id'),
                   priority=request.priority)
        
        # Analyze incident using orchestrator
        analysis = await orchestrator.analyze_incident(request.incident_data)
        
        execution_time = (datetime.utcnow() - start_time).total_seconds()
        
        # Extract agents used
        agents_used = [
            agent_type.value for agent_type in analysis.agent_results.keys()
        ]
        
        logger.info("Incident analysis completed",
                   incident_id=request.incident_data.get('id'),
                   analysis_id=analysis.analysis_id,
                   confidence=analysis.confidence,
                   execution_time=execution_time,
                   agents_used=len(agents_used))
        
        return IncidentAnalysisResponse(
            analysis=analysis,
            execution_time=execution_time,
            agents_used=agents_used,
            timestamp=datetime.utcnow()
        )
        
    except Exception as e:
        logger.error("Incident analysis failed", 
                    incident_id=request.incident_data.get('id'), 
                    error=str(e))
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")


@router.get("/analysis/{analysis_id}")
async def get_analysis_result(
    analysis_id: str,
    orchestrator: AgentOrchestrator = Depends(get_agent_orchestrator)
):
    """Get stored analysis result"""
    
    try:
        # This would query a database in production
        # For now, check execution history
        for execution in orchestrator.execution_history:
            if execution.get('analysis_id') == analysis_id:
                return {
                    "analysis_id": analysis_id,
                    "execution_info": execution,
                    "timestamp": datetime.utcnow().isoformat()
                }
        
        raise HTTPException(status_code=404, detail="Analysis not found")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get analysis result", 
                    analysis_id=analysis_id, error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get analysis: {str(e)}")


# === AGENT MANAGEMENT ENDPOINTS ===

@router.get("/status")
async def get_agents_status(
    orchestrator: AgentOrchestrator = Depends(get_agent_orchestrator)
):
    """Get status of all agents"""
    
    try:
        agent_statuses = []
        
        for agent_type, agent in orchestrator.agents.items():
            status = orchestrator.agent_status.get(agent_type, "unknown")
            
            # Calculate metrics from execution history
            agent_executions = [
                exec for exec in orchestrator.execution_history
                if agent_type.value in exec.get('agent_results', {})
            ]
            
            success_count = sum(1 for exec in agent_executions if exec.get('success', False))
            total_count = len(agent_executions)
            success_rate = success_count / total_count if total_count > 0 else 1.0
            
            avg_execution_time = (
                sum(exec.get('execution_time', 0) for exec in agent_executions) / total_count
                if total_count > 0 else 0.0
            )
            
            agent_statuses.append(AgentStatusResponse(
                agent_type=agent_type.value,
                status=status.value,
                last_execution=None,  # Would be tracked in production
                success_rate=success_rate,
                average_execution_time=avg_execution_time
            ))
        
        return {
            "agents": agent_statuses,
            "total_agents": len(agent_statuses),
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error("Failed to get agents status", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get status: {str(e)}")


@router.get("/agent/{agent_type}/performance")
async def get_agent_performance(
    agent_type: str,
    orchestrator: AgentOrchestrator = Depends(get_agent_orchestrator)
):
    """Get performance metrics for specific agent"""
    
    try:
        # Validate agent type
        try:
            agent_enum = AgentType(agent_type)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"Invalid agent type: {agent_type}")
        
        # Calculate performance metrics
        agent_executions = [
            exec for exec in orchestrator.execution_history
            if agent_type in exec.get('agent_results', {})
        ]
        
        if not agent_executions:
            return {
                "agent_type": agent_type,
                "message": "No execution history available",
                "timestamp": datetime.utcnow().isoformat()
            }
        
        successful_executions = [exec for exec in agent_executions if exec.get('success', False)]
        failed_executions = [exec for exec in agent_executions if not exec.get('success', False)]
        
        execution_times = [exec.get('execution_time', 0) for exec in agent_executions]
        
        performance = {
            "agent_type": agent_type,
            "total_executions": len(agent_executions),
            "successful_executions": len(successful_executions),
            "failed_executions": len(failed_executions),
            "success_rate": len(successful_executions) / len(agent_executions),
            "average_execution_time": sum(execution_times) / len(execution_times),
            "min_execution_time": min(execution_times) if execution_times else 0,
            "max_execution_time": max(execution_times) if execution_times else 0,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        return performance
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get agent performance", 
                    agent_type=agent_type, error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get performance: {str(e)}")


# === ORCHESTRATION ENDPOINTS ===

@router.get("/execution-history")
async def get_execution_history(
    limit: int = Query(default=50, ge=1, le=200),
    orchestrator: AgentOrchestrator = Depends(get_agent_orchestrator)
):
    """Get recent execution history"""
    
    try:
        # Get recent executions
        recent_executions = orchestrator.execution_history[-limit:]
        
        # Add summary statistics
        total_executions = len(orchestrator.execution_history)
        successful_executions = sum(1 for exec in orchestrator.execution_history if exec.get('success', False))
        
        return {
            "executions": recent_executions,
            "summary": {
                "total_executions": total_executions,
                "successful_executions": successful_executions,
                "success_rate": successful_executions / total_executions if total_executions > 0 else 0,
                "returned_count": len(recent_executions)
            },
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error("Failed to get execution history", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get history: {str(e)}")


@router.get("/metrics")
async def get_orchestrator_metrics(
    orchestrator: AgentOrchestrator = Depends(get_agent_orchestrator)
):
    """Get orchestrator performance metrics"""
    
    try:
        # Calculate overall metrics
        total_executions = len(orchestrator.execution_history)
        successful_executions = sum(1 for exec in orchestrator.execution_history if exec.get('success', False))
        
        execution_times = [
            exec.get('execution_time', 0) 
            for exec in orchestrator.execution_history 
            if 'execution_time' in exec
        ]
        
        # Agent-specific metrics
        agent_metrics = {}
        for agent_type in orchestrator.agents.keys():
            agent_executions = [
                exec for exec in orchestrator.execution_history
                if agent_type.value in str(exec.get('agent_results', {}))
            ]
            
            agent_metrics[agent_type.value] = {
                "total_executions": len(agent_executions),
                "success_rate": (
                    sum(1 for exec in agent_executions if exec.get('success', False)) / len(agent_executions)
                    if agent_executions else 0
                )
            }
        
        metrics = {
            "overall": {
                "total_executions": total_executions,
                "successful_executions": successful_executions,
                "success_rate": successful_executions / total_executions if total_executions > 0 else 0,
                "average_execution_time": sum(execution_times) / len(execution_times) if execution_times else 0,
                "active_agents": len(orchestrator.agents)
            },
            "agents": agent_metrics,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        return metrics
        
    except Exception as e:
        logger.error("Failed to get orchestrator metrics", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get metrics: {str(e)}")


# === HEALTH AND STATUS ENDPOINTS ===

@router.get("/health")
async def orchestrator_health_check(
    orchestrator: AgentOrchestrator = Depends(get_agent_orchestrator)
):
    """Get orchestrator health status"""
    
    try:
        health_data = await orchestrator.health_check()
        
        return OrchestratorHealthCheck(
            status=health_data["status"],
            agent_count=health_data["agent_count"],
            active_tasks=health_data["active_tasks"],
            agent_statuses=health_data["agent_statuses"],
            execution_history=health_data["execution_history"],
            timestamp=datetime.utcnow()
        )
        
    except Exception as e:
        logger.error("Orchestrator health check failed", error=str(e))
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")


# === TESTING ENDPOINTS ===

@router.post("/test-agent/{agent_type}")
async def test_agent(
    agent_type: str,
    test_data: Dict[str, Any] = {},
    orchestrator: AgentOrchestrator = Depends(get_agent_orchestrator)
):
    """Test individual agent execution"""
    
    try:
        # Validate agent type
        try:
            agent_enum = AgentType(agent_type)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"Invalid agent type: {agent_type}")
        
        # Create test incident data
        test_incident = {
            "id": f"test_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
            "type": "test",
            "severity": "low",
            "title": f"Test incident for {agent_type}",
            "description": "Test incident for agent validation",
            **test_data
        }
        
        # Execute single agent (this would need to be implemented in orchestrator)
        logger.info("Testing agent", agent_type=agent_type)
        
        # For now, return mock test result
        test_result = {
            "agent_type": agent_type,
            "test_status": "completed",
            "test_incident": test_incident,
            "execution_time": 1.5,  # Mock execution time
            "success": True,
            "message": f"Agent {agent_type} test completed successfully",
            "timestamp": datetime.utcnow().isoformat()
        }
        
        return test_result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Agent test failed", agent_type=agent_type, error=str(e))
        raise HTTPException(status_code=500, detail=f"Agent test failed: {str(e)}")
