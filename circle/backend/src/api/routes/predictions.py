"""
🧠 AURA Intelligence Prediction Engine API Routes
ML-based failure prediction and forecasting endpoints
"""

from datetime import datetime
from typing import Dict, List, Optional, Any

from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel
import structlog

from src.services.prediction_engine import PredictionEngine
from src.models.topology import TopologyFeatures, TopologyPrediction
from src.main import get_prediction_engine

router = APIRouter()
logger = structlog.get_logger("aura.api.predictions")


# === REQUEST/RESPONSE MODELS ===

class PredictFailureRequest(BaseModel):
    """Request to predict system failure"""
    topology_features: TopologyFeatures
    prediction_horizon: Optional[int] = 300  # 5 minutes default


class PredictionResponse(BaseModel):
    """Response from failure prediction"""
    prediction: TopologyPrediction
    model_confidence: float
    prediction_time: float
    timestamp: datetime


class TrainModelRequest(BaseModel):
    """Request to train prediction model"""
    training_data: List[Dict[str, Any]]
    model_type: str = "failure_classifier"


class ModelTrainingResponse(BaseModel):
    """Response from model training"""
    training_success: bool
    model_metrics: Dict[str, float]
    training_samples: int
    training_time: float
    timestamp: datetime


class PredictionHealthResponse(BaseModel):
    """Prediction engine health response"""
    status: str
    models_loaded: Dict[str, bool]
    model_metrics: Dict[str, float]
    prediction_cache_size: int
    timestamp: datetime


# === PREDICTION ENDPOINTS ===

@router.post("/predict-failure", response_model=PredictionResponse)
async def predict_failure(
    request: PredictFailureRequest,
    prediction_engine: PredictionEngine = Depends(get_prediction_engine)
):
    """Predict system failure based on topological features"""
    
    start_time = datetime.utcnow()
    
    try:
        logger.info("Starting failure prediction", 
                   snapshot_id=request.topology_features.snapshot_id)
        
        # Make prediction
        prediction = await prediction_engine.predict_failure(request.topology_features)
        
        prediction_time = (datetime.utcnow() - start_time).total_seconds()
        
        logger.info("Failure prediction completed",
                   prediction_id=prediction.prediction_id,
                   failure_probability=prediction.failure_probability,
                   confidence=prediction.confidence,
                   prediction_time=prediction_time)
        
        return PredictionResponse(
            prediction=prediction,
            model_confidence=prediction_engine.model_metrics.get("accuracy", 0.0),
            prediction_time=prediction_time,
            timestamp=datetime.utcnow()
        )
        
    except Exception as e:
        logger.error("Failure prediction failed", 
                    snapshot_id=request.topology_features.snapshot_id, 
                    error=str(e))
        raise HTTPException(status_code=500, detail=f"Prediction failed: {str(e)}")


@router.get("/prediction/{prediction_id}")
async def get_prediction(
    prediction_id: str,
    prediction_engine: PredictionEngine = Depends(get_prediction_engine)
):
    """Get cached prediction result"""
    
    try:
        # Check cache for prediction
        if prediction_id in prediction_engine.prediction_cache:
            prediction = prediction_engine.prediction_cache[prediction_id]
            
            logger.info("Prediction retrieved from cache", 
                       prediction_id=prediction_id)
            
            return {
                "prediction_id": prediction_id,
                "prediction": prediction,
                "cached": True,
                "timestamp": datetime.utcnow().isoformat()
            }
        else:
            raise HTTPException(status_code=404, detail="Prediction not found in cache")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get prediction", 
                    prediction_id=prediction_id, error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get prediction: {str(e)}")


@router.get("/predictions/recent")
async def get_recent_predictions(
    limit: int = Query(default=10, ge=1, le=100),
    prediction_engine: PredictionEngine = Depends(get_prediction_engine)
):
    """Get recent predictions"""
    
    try:
        # Get recent predictions from cache
        recent_predictions = list(prediction_engine.prediction_cache.values())[-limit:]
        
        # Sort by timestamp
        recent_predictions.sort(key=lambda p: p.prediction_timestamp, reverse=True)
        
        # Create summary
        summary = {
            "total_predictions": len(prediction_engine.prediction_cache),
            "high_risk_predictions": sum(
                1 for p in recent_predictions 
                if p.failure_probability > 0.7
            ),
            "average_confidence": (
                sum(p.confidence for p in recent_predictions) / len(recent_predictions)
                if recent_predictions else 0.0
            )
        }
        
        logger.info("Recent predictions retrieved", count=len(recent_predictions))
        
        return {
            "predictions": recent_predictions,
            "summary": summary,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error("Failed to get recent predictions", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get predictions: {str(e)}")


# === MODEL TRAINING ENDPOINTS ===

@router.post("/train-model", response_model=ModelTrainingResponse)
async def train_model(
    request: TrainModelRequest,
    prediction_engine: PredictionEngine = Depends(get_prediction_engine)
):
    """Train prediction model with new data"""
    
    start_time = datetime.utcnow()
    
    try:
        logger.info("Starting model training", 
                   samples=len(request.training_data),
                   model_type=request.model_type)
        
        # Train model
        model_metrics = await prediction_engine.train_model(request.training_data)
        
        training_time = (datetime.utcnow() - start_time).total_seconds()
        
        logger.info("Model training completed",
                   accuracy=model_metrics.get("accuracy", 0.0),
                   training_samples=model_metrics.get("training_samples", 0),
                   training_time=training_time)
        
        return ModelTrainingResponse(
            training_success=True,
            model_metrics=model_metrics,
            training_samples=model_metrics.get("training_samples", 0),
            training_time=training_time,
            timestamp=datetime.utcnow()
        )
        
    except Exception as e:
        logger.error("Model training failed", error=str(e))
        return ModelTrainingResponse(
            training_success=False,
            model_metrics={},
            training_samples=0,
            training_time=(datetime.utcnow() - start_time).total_seconds(),
            timestamp=datetime.utcnow()
        )


@router.get("/model/metrics")
async def get_model_metrics(
    prediction_engine: PredictionEngine = Depends(get_prediction_engine)
):
    """Get current model performance metrics"""
    
    try:
        metrics = prediction_engine.model_metrics.copy()
        
        # Add additional information
        metrics.update({
            "models_loaded": {
                "failure_classifier": prediction_engine.failure_classifier is not None,
                "time_regressor": prediction_engine.time_to_failure_regressor is not None,
                "impact_predictor": prediction_engine.impact_predictor is not None
            },
            "cache_size": len(prediction_engine.prediction_cache),
            "timestamp": datetime.utcnow().isoformat()
        })
        
        return metrics
        
    except Exception as e:
        logger.error("Failed to get model metrics", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get metrics: {str(e)}")


@router.post("/model/validate")
async def validate_model(
    validation_data: List[Dict[str, Any]],
    prediction_engine: PredictionEngine = Depends(get_prediction_engine)
):
    """Validate model performance on test data"""
    
    try:
        if not validation_data:
            raise HTTPException(status_code=400, detail="No validation data provided")
        
        # This would implement model validation
        # For now, return mock validation results
        
        validation_results = {
            "validation_samples": len(validation_data),
            "accuracy": 0.85,  # Mock accuracy
            "precision": 0.82,  # Mock precision
            "recall": 0.88,     # Mock recall
            "f1_score": 0.85,   # Mock F1 score
            "validation_time": 2.5,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        logger.info("Model validation completed", 
                   samples=len(validation_data),
                   accuracy=validation_results["accuracy"])
        
        return validation_results
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Model validation failed", error=str(e))
        raise HTTPException(status_code=500, detail=f"Validation failed: {str(e)}")


# === ANALYTICS ENDPOINTS ===

@router.get("/analytics/trends")
async def get_prediction_trends(
    hours: int = Query(default=24, ge=1, le=168),  # Max 1 week
    prediction_engine: PredictionEngine = Depends(get_prediction_engine)
):
    """Get prediction trends over time"""
    
    try:
        # Filter predictions by time window
        from datetime import timedelta
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        
        recent_predictions = [
            p for p in prediction_engine.prediction_cache.values()
            if p.prediction_timestamp >= cutoff_time
        ]
        
        if not recent_predictions:
            return {
                "message": f"No predictions available for the last {hours} hours",
                "timestamp": datetime.utcnow().isoformat()
            }
        
        # Calculate trends
        high_risk_count = sum(1 for p in recent_predictions if p.failure_probability > 0.7)
        medium_risk_count = sum(1 for p in recent_predictions if 0.3 < p.failure_probability <= 0.7)
        low_risk_count = sum(1 for p in recent_predictions if p.failure_probability <= 0.3)
        
        avg_failure_probability = sum(p.failure_probability for p in recent_predictions) / len(recent_predictions)
        avg_confidence = sum(p.confidence for p in recent_predictions) / len(recent_predictions)
        
        trends = {
            "time_window_hours": hours,
            "total_predictions": len(recent_predictions),
            "risk_distribution": {
                "high_risk": high_risk_count,
                "medium_risk": medium_risk_count,
                "low_risk": low_risk_count
            },
            "averages": {
                "failure_probability": avg_failure_probability,
                "confidence": avg_confidence
            },
            "timestamp": datetime.utcnow().isoformat()
        }
        
        logger.info("Prediction trends calculated", 
                   hours=hours, predictions=len(recent_predictions))
        
        return trends
        
    except Exception as e:
        logger.error("Failed to get prediction trends", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get trends: {str(e)}")


@router.get("/analytics/accuracy")
async def get_prediction_accuracy(
    prediction_engine: PredictionEngine = Depends(get_prediction_engine)
):
    """Get prediction accuracy analytics"""
    
    try:
        # This would analyze historical predictions vs actual outcomes
        # For now, return current model metrics
        
        accuracy_data = {
            "current_model_accuracy": prediction_engine.model_metrics.get("accuracy", 0.0),
            "precision": prediction_engine.model_metrics.get("precision", 0.0),
            "recall": prediction_engine.model_metrics.get("recall", 0.0),
            "last_trained": prediction_engine.model_metrics.get("last_trained"),
            "training_samples": prediction_engine.model_metrics.get("training_samples", 0),
            "model_age_hours": 0,  # Would calculate based on last_trained
            "timestamp": datetime.utcnow().isoformat()
        }
        
        return accuracy_data
        
    except Exception as e:
        logger.error("Failed to get prediction accuracy", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get accuracy: {str(e)}")


# === HEALTH AND STATUS ENDPOINTS ===

@router.get("/health", response_model=PredictionHealthResponse)
async def prediction_health_check(
    prediction_engine: PredictionEngine = Depends(get_prediction_engine)
):
    """Get prediction engine health status"""
    
    try:
        health_data = await prediction_engine.health_check()
        
        return PredictionHealthResponse(
            status=health_data["status"],
            models_loaded=health_data["models_loaded"],
            model_metrics=health_data["model_metrics"],
            prediction_cache_size=health_data["prediction_cache_size"],
            timestamp=datetime.utcnow()
        )
        
    except Exception as e:
        logger.error("Prediction health check failed", error=str(e))
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")


@router.delete("/cache")
async def clear_prediction_cache(
    prediction_engine: PredictionEngine = Depends(get_prediction_engine)
):
    """Clear prediction cache"""
    
    try:
        cache_size_before = len(prediction_engine.prediction_cache)
        prediction_engine.prediction_cache.clear()
        
        logger.info("Prediction cache cleared", 
                   predictions_cleared=cache_size_before)
        
        return {
            "message": "Prediction cache cleared successfully",
            "predictions_cleared": cache_size_before,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error("Failed to clear prediction cache", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to clear cache: {str(e)}")
