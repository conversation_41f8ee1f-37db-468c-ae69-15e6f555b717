"""
🧠 AURA Intelligence Knowledge Graph API Routes
Knowledge graph operations and incident analysis endpoints
"""

from datetime import datetime
from typing import Dict, List, Optional, Any

from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel
import structlog

from src.services.knowledge_graph import KnowledgeGraphService
from src.models.knowledge_graph import (
    ServiceNode, IncidentNode, SimilarityResult,
    KnowledgeGraphQuery, GraphResponse
)
from src.main import get_knowledge_graph_service

router = APIRouter()
logger = structlog.get_logger("aura.api.knowledge_graph")


# === REQUEST/RESPONSE MODELS ===

class AddServiceRequest(BaseModel):
    """Request to add a service to the knowledge graph"""
    name: str
    type: str
    version: str
    status: str = "active"
    metadata: Dict[str, Any] = {}


class AddIncidentRequest(BaseModel):
    """Request to add an incident to the knowledge graph"""
    title: str
    description: str
    severity: str
    status: str = "investigating"
    tags: List[str] = []
    affected_services: List[str] = []
    metadata: Dict[str, Any] = {}


class AddCausalRelationshipRequest(BaseModel):
    """Request to add a causal relationship"""
    cause_id: str
    effect_id: str
    confidence: float
    evidence: Dict[str, Any] = {}


class SimilarIncidentsResponse(BaseModel):
    """Response for similar incidents query"""
    incident_id: str
    similar_incidents: List[SimilarityResult]
    total_found: int
    query_time: float


class RootCauseResponse(BaseModel):
    """Response for root cause analysis"""
    incident_id: str
    causal_chains: List[Dict[str, Any]]
    most_likely_root_cause: Optional[Dict[str, Any]]
    confidence: float
    analysis_time: float


# === SERVICE ENDPOINTS ===

@router.post("/services", response_model=GraphResponse)
async def add_service(
    request: AddServiceRequest,
    kg_service: KnowledgeGraphService = Depends(get_knowledge_graph_service)
):
    """Add a new service to the knowledge graph"""
    
    try:
        # Create service node
        service = ServiceNode(
            id=f"service_{request.name}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
            name=request.name,
            type=request.type,
            version=request.version,
            status=request.status,
            metadata=request.metadata
        )
        
        # Add to knowledge graph
        service_id = await kg_service.add_service(service)
        
        logger.info("Service added to knowledge graph", 
                   service_id=service_id, name=request.name)
        
        return GraphResponse(
            success=True,
            message=f"Service {request.name} added successfully",
            data={"service_id": service_id}
        )
        
    except Exception as e:
        logger.error("Failed to add service", error=str(e), name=request.name)
        raise HTTPException(status_code=500, detail=f"Failed to add service: {str(e)}")


@router.get("/services/{service_id}")
async def get_service(
    service_id: str,
    kg_service: KnowledgeGraphService = Depends(get_knowledge_graph_service)
):
    """Get service details from knowledge graph"""
    
    try:
        # This would be implemented in the KnowledgeGraphService
        # For now, return a placeholder response
        return {
            "service_id": service_id,
            "message": "Service retrieval not yet implemented",
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error("Failed to get service", error=str(e), service_id=service_id)
        raise HTTPException(status_code=500, detail=f"Failed to get service: {str(e)}")


# === INCIDENT ENDPOINTS ===

@router.post("/incidents", response_model=GraphResponse)
async def add_incident(
    request: AddIncidentRequest,
    kg_service: KnowledgeGraphService = Depends(get_knowledge_graph_service)
):
    """Add a new incident to the knowledge graph"""
    
    try:
        # Create incident node
        incident = IncidentNode(
            id=f"incident_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
            title=request.title,
            description=request.description,
            severity=request.severity,
            status=request.status,
            timestamp=datetime.utcnow(),
            tags=request.tags,
            affected_services=request.affected_services,
            metadata=request.metadata
        )
        
        # Add to knowledge graph
        incident_id = await kg_service.add_incident(incident)
        
        logger.info("Incident added to knowledge graph", 
                   incident_id=incident_id, title=request.title)
        
        return GraphResponse(
            success=True,
            message=f"Incident '{request.title}' added successfully",
            data={"incident_id": incident_id}
        )
        
    except Exception as e:
        logger.error("Failed to add incident", error=str(e), title=request.title)
        raise HTTPException(status_code=500, detail=f"Failed to add incident: {str(e)}")


@router.get("/incidents/{incident_id}/similar", response_model=SimilarIncidentsResponse)
async def find_similar_incidents(
    incident_id: str,
    limit: int = Query(default=5, ge=1, le=20),
    kg_service: KnowledgeGraphService = Depends(get_knowledge_graph_service)
):
    """Find incidents similar to the given incident"""
    
    start_time = datetime.utcnow()
    
    try:
        # Find similar incidents using vector similarity
        similar_incidents = await kg_service.find_similar_incidents(incident_id, limit)
        
        query_time = (datetime.utcnow() - start_time).total_seconds()
        
        logger.info("Similar incidents found", 
                   incident_id=incident_id, 
                   count=len(similar_incidents),
                   query_time=query_time)
        
        return SimilarIncidentsResponse(
            incident_id=incident_id,
            similar_incidents=similar_incidents,
            total_found=len(similar_incidents),
            query_time=query_time
        )
        
    except Exception as e:
        logger.error("Failed to find similar incidents", 
                    error=str(e), incident_id=incident_id)
        raise HTTPException(status_code=500, detail=f"Failed to find similar incidents: {str(e)}")


@router.get("/incidents/{incident_id}/root-cause", response_model=RootCauseResponse)
async def trace_root_cause(
    incident_id: str,
    max_depth: int = Query(default=5, ge=1, le=10),
    kg_service: KnowledgeGraphService = Depends(get_knowledge_graph_service)
):
    """Trace root cause of an incident using causal relationships"""
    
    start_time = datetime.utcnow()
    
    try:
        # Trace causal chains
        causal_chains = await kg_service.trace_root_cause(incident_id, max_depth)
        
        # Find most likely root cause
        most_likely_root_cause = None
        if causal_chains:
            # Sort by overall confidence and take the highest
            sorted_chains = sorted(causal_chains, 
                                 key=lambda x: x.get('overall_confidence', 0.0), 
                                 reverse=True)
            most_likely_root_cause = sorted_chains[0] if sorted_chains else None
        
        # Calculate overall confidence
        confidences = [chain.get('overall_confidence', 0.0) for chain in causal_chains]
        overall_confidence = max(confidences) if confidences else 0.0
        
        analysis_time = (datetime.utcnow() - start_time).total_seconds()
        
        logger.info("Root cause analysis completed", 
                   incident_id=incident_id,
                   chains_found=len(causal_chains),
                   confidence=overall_confidence,
                   analysis_time=analysis_time)
        
        return RootCauseResponse(
            incident_id=incident_id,
            causal_chains=causal_chains,
            most_likely_root_cause=most_likely_root_cause,
            confidence=overall_confidence,
            analysis_time=analysis_time
        )
        
    except Exception as e:
        logger.error("Failed to trace root cause", 
                    error=str(e), incident_id=incident_id)
        raise HTTPException(status_code=500, detail=f"Failed to trace root cause: {str(e)}")


# === RELATIONSHIP ENDPOINTS ===

@router.post("/relationships/causal", response_model=GraphResponse)
async def add_causal_relationship(
    request: AddCausalRelationshipRequest,
    kg_service: KnowledgeGraphService = Depends(get_knowledge_graph_service)
):
    """Add a causal relationship between two entities"""
    
    try:
        # Add causal relationship
        await kg_service.add_causal_relationship(
            cause_id=request.cause_id,
            effect_id=request.effect_id,
            confidence=request.confidence,
            evidence=request.evidence
        )
        
        logger.info("Causal relationship added", 
                   cause=request.cause_id, 
                   effect=request.effect_id,
                   confidence=request.confidence)
        
        return GraphResponse(
            success=True,
            message="Causal relationship added successfully",
            data={
                "cause_id": request.cause_id,
                "effect_id": request.effect_id,
                "confidence": request.confidence
            }
        )
        
    except Exception as e:
        logger.error("Failed to add causal relationship", 
                    error=str(e), 
                    cause=request.cause_id, 
                    effect=request.effect_id)
        raise HTTPException(status_code=500, detail=f"Failed to add causal relationship: {str(e)}")


# === QUERY ENDPOINTS ===

@router.post("/query")
async def execute_query(
    query: KnowledgeGraphQuery,
    kg_service: KnowledgeGraphService = Depends(get_knowledge_graph_service)
):
    """Execute a custom knowledge graph query"""
    
    try:
        # This would be implemented to handle various query types
        # For now, return a placeholder response
        return {
            "query_type": query.query_type,
            "parameters": query.parameters,
            "message": "Custom queries not yet implemented",
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error("Failed to execute query", error=str(e), query_type=query.query_type)
        raise HTTPException(status_code=500, detail=f"Failed to execute query: {str(e)}")


@router.get("/analytics")
async def get_graph_analytics(
    kg_service: KnowledgeGraphService = Depends(get_knowledge_graph_service)
):
    """Get knowledge graph analytics and statistics"""
    
    try:
        # Get basic health check which includes node counts
        health_data = await kg_service.health_check()
        
        # Add additional analytics
        analytics = {
            "timestamp": datetime.utcnow().isoformat(),
            "node_counts": health_data.get("node_counts", {}),
            "status": health_data.get("status"),
            "analytics": {
                "total_nodes": sum(health_data.get("node_counts", {}).values()),
                "node_types": len(health_data.get("node_counts", {})),
                "last_updated": health_data.get("timestamp")
            }
        }
        
        return analytics
        
    except Exception as e:
        logger.error("Failed to get graph analytics", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get analytics: {str(e)}")


# === SEARCH ENDPOINTS ===

@router.get("/search")
async def search_graph(
    q: str = Query(..., description="Search query"),
    node_types: Optional[List[str]] = Query(None, description="Node types to search"),
    limit: int = Query(default=10, ge=1, le=50),
    kg_service: KnowledgeGraphService = Depends(get_knowledge_graph_service)
):
    """Search the knowledge graph"""
    
    try:
        # This would implement full-text search across the graph
        # For now, return a placeholder response
        return {
            "query": q,
            "node_types": node_types,
            "limit": limit,
            "results": [],
            "message": "Search functionality not yet implemented",
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error("Failed to search graph", error=str(e), query=q)
        raise HTTPException(status_code=500, detail=f"Failed to search: {str(e)}")
