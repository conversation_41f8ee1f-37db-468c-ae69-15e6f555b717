"""
🌐 AURA Intelligence WebSocket Routes
Real-time streaming endpoints for consciousness dashboard
"""

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends
from fastapi.responses import HTMLResponse
import logging

from src.api.websocket import websocket_manager, ConnectionType
from src.services.topology_analyzer import get_topology_analyzer
from src.services.agent_orchestrator import get_agent_orchestrator
from src.services.knowledge_graph import get_knowledge_graph_service

router = APIRouter()
logger = logging.getLogger("aura.websocket.routes")


@router.get("/ws")
async def get_websocket_info():
    """Get WebSocket connection information"""
    return {
        "message": "AURA Intelligence WebSocket API",
        "endpoints": {
            "/ws/tda": "Topological Data Analysis stream",
            "/ws/agents": "Agent status and orchestration stream", 
            "/ws/patterns": "Pattern analysis and causal inference stream",
            "/ws/health": "System health and metrics stream",
            "/ws/audit": "Audit trail and compliance stream"
        },
        "protocol": "WebSocket",
        "format": "JSON"
    }


@router.websocket("/ws/tda")
async def websocket_tda_endpoint(
    websocket: WebSocket,
    topology_analyzer = Depends(get_topology_analyzer)
):
    """WebSocket endpoint for TDA streaming"""
    try:
        # Set service reference
        websocket_manager.set_services(
            topology_analyzer=topology_analyzer,
            agent_orchestrator=None,  # Will be set by other endpoints
            knowledge_graph=None
        )
        
        # Connect to TDA stream
        await websocket_manager.connect(websocket, ConnectionType.TDA_STREAM)
        
    except WebSocketDisconnect:
        logger.info("🔌 TDA WebSocket disconnected")
    except Exception as e:
        logger.error("❌ TDA WebSocket error", error=str(e))


@router.websocket("/ws/agents")
async def websocket_agents_endpoint(
    websocket: WebSocket,
    agent_orchestrator = Depends(get_agent_orchestrator)
):
    """WebSocket endpoint for agent status streaming"""
    try:
        # Set service reference
        websocket_manager.set_services(
            topology_analyzer=None,  # Will be set by other endpoints
            agent_orchestrator=agent_orchestrator,
            knowledge_graph=None
        )
        
        # Connect to agent status stream
        await websocket_manager.connect(websocket, ConnectionType.AGENT_STATUS)
        
    except WebSocketDisconnect:
        logger.info("🔌 Agents WebSocket disconnected")
    except Exception as e:
        logger.error("❌ Agents WebSocket error", error=str(e))


@router.websocket("/ws/patterns")
async def websocket_patterns_endpoint(
    websocket: WebSocket,
    knowledge_graph = Depends(get_knowledge_graph_service)
):
    """WebSocket endpoint for pattern analysis streaming"""
    try:
        # Set service reference
        websocket_manager.set_services(
            topology_analyzer=None,
            agent_orchestrator=None,
            knowledge_graph=knowledge_graph
        )
        
        # Connect to pattern analysis stream
        await websocket_manager.connect(websocket, ConnectionType.PATTERN_ANALYSIS)
        
    except WebSocketDisconnect:
        logger.info("🔌 Patterns WebSocket disconnected")
    except Exception as e:
        logger.error("❌ Patterns WebSocket error", error=str(e))


@router.websocket("/ws/health")
async def websocket_health_endpoint(websocket: WebSocket):
    """WebSocket endpoint for system health streaming"""
    try:
        # Connect to health stream
        await websocket_manager.connect(websocket, ConnectionType.SYSTEM_HEALTH)
        
    except WebSocketDisconnect:
        logger.info("🔌 Health WebSocket disconnected")
    except Exception as e:
        logger.error("❌ Health WebSocket error", error=str(e))


@router.websocket("/ws/audit")
async def websocket_audit_endpoint(websocket: WebSocket):
    """WebSocket endpoint for audit trail streaming"""
    try:
        # Connect to audit stream
        await websocket_manager.connect(websocket, ConnectionType.AUDIT_TRAIL)
        
    except WebSocketDisconnect:
        logger.info("🔌 Audit WebSocket disconnected")
    except Exception as e:
        logger.error("❌ Audit WebSocket error", error=str(e))


@router.get("/ws/metrics")
async def get_websocket_metrics():
    """Get WebSocket streaming metrics"""
    try:
        metrics = await websocket_manager.get_metrics()
        return {
            "status": "success",
            "metrics": metrics,
            "timestamp": "2025-01-28T04:55:00Z"
        }
    except Exception as e:
        logger.error("❌ Failed to get WebSocket metrics", error=str(e))
        return {
            "status": "error",
            "message": str(e),
            "timestamp": "2025-01-28T04:55:00Z"
        }


@router.post("/ws/control")
async def control_websocket_streaming(action: str):
    """Control WebSocket streaming (start/stop)"""
    try:
        if action == "start":
            # Start streaming if not already active
            if not websocket_manager.is_streaming:
                await websocket_manager._start_streaming()
                return {"status": "success", "message": "Streaming started"}
            else:
                return {"status": "info", "message": "Streaming already active"}
        
        elif action == "stop":
            # Stop streaming
            await websocket_manager.stop_streaming()
            return {"status": "success", "message": "Streaming stopped"}
        
        else:
            return {"status": "error", "message": "Invalid action. Use 'start' or 'stop'"}
            
    except Exception as e:
        logger.error("❌ WebSocket control error", error=str(e))
        return {"status": "error", "message": str(e)}


# HTML test page for WebSocket connections
@router.get("/ws/test")
async def get_websocket_test_page():
    """Get HTML test page for WebSocket connections"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>AURA Intelligence WebSocket Test</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .container { max-width: 1200px; margin: 0 auto; }
            .connection { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
            .status { padding: 5px 10px; border-radius: 3px; margin-left: 10px; }
            .connected { background-color: #d4edda; color: #155724; }
            .disconnected { background-color: #f8d7da; color: #721c24; }
            .data { background-color: #f8f9fa; padding: 10px; border-radius: 3px; margin-top: 10px; }
            button { padding: 8px 16px; margin: 5px; border: none; border-radius: 3px; cursor: pointer; }
            .connect { background-color: #007bff; color: white; }
            .disconnect { background-color: #dc3545; color: white; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🌐 AURA Intelligence WebSocket Test</h1>
            <p>Test real-time data streaming from the AURA Intelligence backend.</p>
            
            <div class="connection">
                <h3>🔍 TDA Stream</h3>
                <span id="tda-status" class="status disconnected">Disconnected</span>
                <button class="connect" onclick="connectTDA()">Connect</button>
                <button class="disconnect" onclick="disconnectTDA()">Disconnect</button>
                <div id="tda-data" class="data"></div>
            </div>
            
            <div class="connection">
                <h3>🤖 Agent Status</h3>
                <span id="agents-status" class="status disconnected">Disconnected</span>
                <button class="connect" onclick="connectAgents()">Connect</button>
                <button class="disconnect" onclick="disconnectAgents()">Disconnect</button>
                <div id="agents-data" class="data"></div>
            </div>
            
            <div class="connection">
                <h3>🧠 Pattern Analysis</h3>
                <span id="patterns-status" class="status disconnected">Disconnected</span>
                <button class="connect" onclick="connectPatterns()">Connect</button>
                <button class="disconnect" onclick="disconnectPatterns()">Disconnect</button>
                <div id="patterns-data" class="data"></div>
            </div>
            
            <div class="connection">
                <h3>💚 System Health</h3>
                <span id="health-status" class="status disconnected">Disconnected</span>
                <button class="connect" onclick="connectHealth()">Connect</button>
                <button class="disconnect" onclick="disconnectHealth()">Disconnect</button>
                <div id="health-data" class="data"></div>
            </div>
            
            <div class="connection">
                <h3>📋 Audit Trail</h3>
                <span id="audit-status" class="status disconnected">Disconnected</span>
                <button class="connect" onclick="connectAudit()">Connect</button>
                <button class="disconnect" onclick="disconnectAudit()">Disconnect</button>
                <div id="audit-data" class="data"></div>
            </div>
        </div>
        
        <script>
            const connections = {};
            
            function connectWebSocket(type, url) {
                if (connections[type]) {
                    connections[type].close();
                }
                
                const ws = new WebSocket(url);
                connections[type] = ws;
                
                ws.onopen = function() {
                    document.getElementById(type + '-status').textContent = 'Connected';
                    document.getElementById(type + '-status').className = 'status connected';
                };
                
                ws.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    document.getElementById(type + '-data').textContent = 
                        JSON.stringify(data, null, 2);
                };
                
                ws.onclose = function() {
                    document.getElementById(type + '-status').textContent = 'Disconnected';
                    document.getElementById(type + '-status').className = 'status disconnected';
                };
                
                ws.onerror = function(error) {
                    console.error('WebSocket error:', error);
                };
            }
            
            function disconnectWebSocket(type) {
                if (connections[type]) {
                    connections[type].close();
                    connections[type] = null;
                }
            }
            
            function connectTDA() {
                connectWebSocket('tda', 'ws://localhost:8000/ws/tda');
            }
            
            function disconnectTDA() {
                disconnectWebSocket('tda');
            }
            
            function connectAgents() {
                connectWebSocket('agents', 'ws://localhost:8000/ws/agents');
            }
            
            function disconnectAgents() {
                disconnectWebSocket('agents');
            }
            
            function connectPatterns() {
                connectWebSocket('patterns', 'ws://localhost:8000/ws/patterns');
            }
            
            function disconnectPatterns() {
                disconnectWebSocket('patterns');
            }
            
            function connectHealth() {
                connectWebSocket('health', 'ws://localhost:8000/ws/health');
            }
            
            function disconnectHealth() {
                disconnectWebSocket('health');
            }
            
            function connectAudit() {
                connectWebSocket('audit', 'ws://localhost:8000/ws/audit');
            }
            
            function disconnectAudit() {
                disconnectWebSocket('audit');
            }
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content) 