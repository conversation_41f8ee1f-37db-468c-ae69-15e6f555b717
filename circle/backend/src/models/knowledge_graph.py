"""
📊 AURA Intelligence Knowledge Graph Models
Pydantic models for knowledge graph entities and relationships
"""

from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from enum import Enum

from pydantic import BaseModel, Field, validator
import numpy as np


class NodeType(str, Enum):
    """Node types in the knowledge graph"""
    SERVICE = "service"
    INCIDENT = "incident"
    SYSTEM = "system"
    METRIC = "metric"
    ALERT = "alert"
    REMEDIATION = "remediation"


class EdgeType(str, Enum):
    """Edge types in the knowledge graph"""
    DEPENDS_ON = "depends_on"
    CAUSES = "causes"
    PRECEDES = "precedes"
    INFLUENCES = "influences"
    RESOLVES = "resolves"
    TRIGGERS = "triggers"


class Severity(str, Enum):
    """Incident severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class Status(str, Enum):
    """Status values"""
    ACTIVE = "active"
    RESOLVED = "resolved"
    INVESTIGATING = "investigating"
    MONITORING = "monitoring"


# === BASE MODELS ===

class BaseNode(BaseModel):
    """Base node model"""
    id: str = Field(..., description="Unique identifier")
    type: NodeType = Field(..., description="Node type")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    metadata: Dict[str, Any] = Field(default_factory=dict)


class BaseEdge(BaseModel):
    """Base edge model"""
    source_id: str = Field(..., description="Source node ID")
    target_id: str = Field(..., description="Target node ID")
    type: EdgeType = Field(..., description="Edge type")
    weight: float = Field(default=1.0, ge=0.0, le=1.0)
    confidence: float = Field(default=1.0, ge=0.0, le=1.0)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    metadata: Dict[str, Any] = Field(default_factory=dict)


# === SPECIFIC NODE TYPES ===

class SystemNode(BaseNode):
    """System/infrastructure node"""
    type: NodeType = Field(default=NodeType.SYSTEM, const=True)
    name: str = Field(..., description="System name")
    environment: str = Field(..., description="Environment (prod, staging, dev)")
    region: str = Field(..., description="Geographic region")
    cluster: str = Field(..., description="Cluster identifier")
    health_score: float = Field(default=1.0, ge=0.0, le=1.0)


class ServiceNode(BaseNode):
    """Service node"""
    type: NodeType = Field(default=NodeType.SERVICE, const=True)
    name: str = Field(..., description="Service name")
    version: str = Field(..., description="Service version")
    status: Status = Field(default=Status.ACTIVE)
    health_score: float = Field(default=1.0, ge=0.0, le=1.0)
    dependencies: List[str] = Field(default_factory=list)
    
    # Performance metrics
    cpu_usage: Optional[float] = Field(None, ge=0.0, le=100.0)
    memory_usage: Optional[float] = Field(None, ge=0.0, le=100.0)
    request_rate: Optional[float] = Field(None, ge=0.0)
    error_rate: Optional[float] = Field(None, ge=0.0, le=100.0)
    response_time: Optional[float] = Field(None, ge=0.0)


class IncidentNode(BaseNode):
    """Incident node"""
    type: NodeType = Field(default=NodeType.INCIDENT, const=True)
    title: str = Field(..., description="Incident title")
    description: str = Field(..., description="Incident description")
    severity: Severity = Field(..., description="Incident severity")
    status: Status = Field(default=Status.INVESTIGATING)
    
    # Timestamps
    timestamp: datetime = Field(..., description="Incident start time")
    resolved_at: Optional[datetime] = Field(None, description="Resolution time")
    
    # Classification
    tags: List[str] = Field(default_factory=list)
    category: Optional[str] = Field(None, description="Incident category")
    
    # Impact
    affected_services: List[str] = Field(default_factory=list)
    impact_score: float = Field(default=0.0, ge=0.0, le=1.0)
    
    # Resolution
    resolution_steps: List[str] = Field(default_factory=list)
    root_cause: Optional[str] = Field(None)


class MetricNode(BaseNode):
    """Metric/measurement node"""
    type: NodeType = Field(default=NodeType.METRIC, const=True)
    name: str = Field(..., description="Metric name")
    value: float = Field(..., description="Metric value")
    unit: str = Field(..., description="Metric unit")
    timestamp: datetime = Field(..., description="Measurement timestamp")
    source: str = Field(..., description="Metric source")
    
    # Thresholds
    warning_threshold: Optional[float] = Field(None)
    critical_threshold: Optional[float] = Field(None)
    
    # Context
    labels: Dict[str, str] = Field(default_factory=dict)


class AlertNode(BaseNode):
    """Alert node"""
    type: NodeType = Field(default=NodeType.ALERT, const=True)
    title: str = Field(..., description="Alert title")
    message: str = Field(..., description="Alert message")
    severity: Severity = Field(..., description="Alert severity")
    status: Status = Field(default=Status.ACTIVE)
    
    # Source
    source_metric: Optional[str] = Field(None)
    source_service: Optional[str] = Field(None)
    
    # Timing
    triggered_at: datetime = Field(..., description="Alert trigger time")
    acknowledged_at: Optional[datetime] = Field(None)
    resolved_at: Optional[datetime] = Field(None)


class RemediationNode(BaseNode):
    """Remediation action node"""
    type: NodeType = Field(default=NodeType.REMEDIATION, const=True)
    name: str = Field(..., description="Remediation name")
    description: str = Field(..., description="Remediation description")
    action_type: str = Field(..., description="Type of action")
    
    # Execution
    command: Optional[str] = Field(None, description="Command to execute")
    parameters: Dict[str, Any] = Field(default_factory=dict)
    
    # Safety
    safety_checks: List[str] = Field(default_factory=list)
    rollback_command: Optional[str] = Field(None)
    
    # Success metrics
    success_rate: float = Field(default=0.0, ge=0.0, le=1.0)
    average_execution_time: Optional[float] = Field(None, ge=0.0)


# === SPECIFIC EDGE TYPES ===

class DependencyEdge(BaseEdge):
    """Service dependency edge"""
    type: EdgeType = Field(default=EdgeType.DEPENDS_ON, const=True)
    dependency_type: str = Field(..., description="Type of dependency")
    criticality: float = Field(default=0.5, ge=0.0, le=1.0)
    
    # Performance impact
    latency_impact: Optional[float] = Field(None, ge=0.0)
    throughput_impact: Optional[float] = Field(None, ge=0.0)


class CausalEdge(BaseEdge):
    """Causal relationship edge"""
    type: EdgeType = Field(default=EdgeType.CAUSES, const=True)
    causal_strength: float = Field(..., ge=0.0, le=1.0)
    evidence: Dict[str, Any] = Field(default_factory=dict)
    
    # Temporal information
    time_delay: Optional[float] = Field(None, ge=0.0, description="Delay in seconds")
    frequency: Optional[float] = Field(None, ge=0.0, description="How often this causation occurs")


class TemporalEdge(BaseEdge):
    """Temporal relationship edge"""
    type: EdgeType = Field(default=EdgeType.PRECEDES, const=True)
    time_difference: float = Field(..., description="Time difference in seconds")
    correlation: float = Field(default=0.0, ge=-1.0, le=1.0)


# === QUERY MODELS ===

class KnowledgeGraphQuery(BaseModel):
    """Query model for knowledge graph operations"""
    query_type: str = Field(..., description="Type of query")
    parameters: Dict[str, Any] = Field(default_factory=dict)
    filters: Dict[str, Any] = Field(default_factory=dict)
    limit: int = Field(default=100, ge=1, le=1000)
    offset: int = Field(default=0, ge=0)


class SimilarityResult(BaseModel):
    """Result from similarity search"""
    id: str = Field(..., description="Node ID")
    title: str = Field(..., description="Node title")
    description: Optional[str] = Field(None)
    severity: Optional[Severity] = Field(None)
    timestamp: Optional[datetime] = Field(None)
    similarity_score: float = Field(..., ge=0.0, le=1.0)
    metadata: Dict[str, Any] = Field(default_factory=dict)


class CausalChain(BaseModel):
    """Causal chain result"""
    chain_id: str = Field(..., description="Chain identifier")
    nodes: List[Dict[str, Any]] = Field(..., description="Nodes in the chain")
    edges: List[Dict[str, Any]] = Field(..., description="Edges in the chain")
    overall_confidence: float = Field(..., ge=0.0, le=1.0)
    depth: int = Field(..., ge=1)
    
    # Analysis
    root_cause_probability: float = Field(default=0.0, ge=0.0, le=1.0)
    impact_score: float = Field(default=0.0, ge=0.0, le=1.0)


class GraphAnalytics(BaseModel):
    """Graph analytics results"""
    node_count: int = Field(..., ge=0)
    edge_count: int = Field(..., ge=0)
    connected_components: int = Field(..., ge=0)
    average_clustering: float = Field(..., ge=0.0, le=1.0)
    
    # Centrality measures
    most_central_nodes: List[Dict[str, Any]] = Field(default_factory=list)
    most_influential_nodes: List[Dict[str, Any]] = Field(default_factory=list)
    
    # Temporal patterns
    incident_frequency: Dict[str, int] = Field(default_factory=dict)
    resolution_times: Dict[str, float] = Field(default_factory=dict)


# === RESPONSE MODELS ===

class GraphResponse(BaseModel):
    """Standard graph operation response"""
    success: bool = Field(..., description="Operation success")
    message: str = Field(..., description="Response message")
    data: Optional[Dict[str, Any]] = Field(None, description="Response data")
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class HealthCheckResponse(BaseModel):
    """Health check response"""
    status: str = Field(..., description="Service status")
    node_counts: Dict[str, int] = Field(default_factory=dict)
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    error: Optional[str] = Field(None, description="Error message if unhealthy")
