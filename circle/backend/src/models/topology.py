"""
🔍 AURA Intelligence Topology Models
Pydantic models for topological data analysis and system topology
"""

from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum

from pydantic import BaseModel, Field, validator
import numpy as np


class TopologyType(str, Enum):
    """Types of topology snapshots"""
    SYSTEM = "system"
    SERVICE = "service"
    NETWORK = "network"
    HYBRID = "hybrid"


class AnalysisMethod(str, Enum):
    """Topological analysis methods"""
    PERSISTENT_HOMOLOGY = "persistent_homology"
    MAPPER = "mapper"
    WITNESS_COMPLEX = "witness_complex"
    ALPHA_COMPLEX = "alpha_complex"


# === CORE TOPOLOGY MODELS ===

class TopologySnapshot(BaseModel):
    """System topology snapshot for TDA analysis"""
    id: str = Field(..., description="Unique snapshot identifier")
    timestamp: datetime = Field(..., description="Snapshot timestamp")
    topology_type: TopologyType = Field(..., description="Type of topology")
    
    # System components
    services: List['ServiceSnapshot'] = Field(default_factory=list)
    metrics: List['MetricSnapshot'] = Field(default_factory=list)
    dependencies: List['DependencySnapshot'] = Field(default_factory=list)
    
    # Metadata
    environment: str = Field(..., description="Environment (prod, staging, dev)")
    cluster: str = Field(..., description="Cluster identifier")
    region: str = Field(..., description="Geographic region")
    
    # Analysis parameters
    analysis_method: AnalysisMethod = Field(default=AnalysisMethod.PERSISTENT_HOMOLOGY)
    max_dimension: int = Field(default=2, ge=0, le=3)
    max_edge_length: float = Field(default=1.0, gt=0.0)


class ServiceSnapshot(BaseModel):
    """Service state snapshot"""
    id: str = Field(..., description="Service identifier")
    name: str = Field(..., description="Service name")
    version: str = Field(..., description="Service version")
    
    # Performance metrics
    cpu_usage: Optional[float] = Field(None, ge=0.0, le=100.0, description="CPU usage %")
    memory_usage: Optional[float] = Field(None, ge=0.0, le=100.0, description="Memory usage %")
    disk_usage: Optional[float] = Field(None, ge=0.0, le=100.0, description="Disk usage %")
    
    # Request metrics
    request_rate: Optional[float] = Field(None, ge=0.0, description="Requests per second")
    error_rate: Optional[float] = Field(None, ge=0.0, le=100.0, description="Error rate %")
    response_time: Optional[float] = Field(None, ge=0.0, description="Average response time (ms)")
    
    # Health indicators
    health_score: float = Field(default=1.0, ge=0.0, le=1.0, description="Overall health score")
    status: str = Field(default="healthy", description="Service status")
    
    # Network metrics
    network_in: Optional[float] = Field(None, ge=0.0, description="Network input (bytes/s)")
    network_out: Optional[float] = Field(None, ge=0.0, description="Network output (bytes/s)")
    
    # Dependencies
    dependency_count: int = Field(default=0, ge=0, description="Number of dependencies")
    dependent_count: int = Field(default=0, ge=0, description="Number of dependents")


class MetricSnapshot(BaseModel):
    """System metric snapshot"""
    id: str = Field(..., description="Metric identifier")
    name: str = Field(..., description="Metric name")
    value: float = Field(..., description="Metric value")
    unit: str = Field(..., description="Metric unit")
    
    # Thresholds
    warning_threshold: Optional[float] = Field(None, description="Warning threshold")
    critical_threshold: Optional[float] = Field(None, description="Critical threshold")
    
    # Context
    source: str = Field(..., description="Metric source")
    labels: Dict[str, str] = Field(default_factory=dict, description="Metric labels")
    
    # Temporal information
    timestamp: datetime = Field(..., description="Metric timestamp")
    previous_value: Optional[float] = Field(None, description="Previous metric value")
    trend: Optional[str] = Field(None, description="Trend direction (up/down/stable)")


class DependencySnapshot(BaseModel):
    """Service dependency snapshot"""
    source_id: str = Field(..., description="Source service ID")
    target_id: str = Field(..., description="Target service ID")
    dependency_type: str = Field(..., description="Type of dependency")
    
    # Strength indicators
    weight: float = Field(default=1.0, ge=0.0, le=1.0, description="Dependency weight")
    criticality: float = Field(default=0.5, ge=0.0, le=1.0, description="Criticality score")
    
    # Performance impact
    latency: Optional[float] = Field(None, ge=0.0, description="Dependency latency (ms)")
    throughput: Optional[float] = Field(None, ge=0.0, description="Throughput (req/s)")
    error_rate: Optional[float] = Field(None, ge=0.0, le=100.0, description="Error rate %")


# === TOPOLOGICAL ANALYSIS RESULTS ===

class PersistenceDiagram(BaseModel):
    """Persistent homology diagram"""
    dimension_0: List[List[float]] = Field(default_factory=list, description="0-dimensional features (connected components)")
    dimension_1: List[List[float]] = Field(default_factory=list, description="1-dimensional features (loops)")
    dimension_2: List[List[float]] = Field(default_factory=list, description="2-dimensional features (voids)")
    
    # Analysis parameters
    max_dimension: int = Field(default=2, ge=0, le=3)
    max_edge_length: float = Field(default=1.0, gt=0.0)
    
    # Computed properties
    @property
    def total_features(self) -> int:
        """Total number of topological features"""
        return len(self.dimension_0) + len(self.dimension_1) + len(self.dimension_2)
    
    @property
    def persistence_entropy(self) -> float:
        """Compute persistence entropy"""
        all_persistences = []
        
        for dim_features in [self.dimension_0, self.dimension_1, self.dimension_2]:
            for birth, death in dim_features:
                if death != float('inf'):
                    all_persistences.append(death - birth)
        
        if not all_persistences:
            return 0.0
        
        # Normalize persistences
        total_persistence = sum(all_persistences)
        if total_persistence == 0:
            return 0.0
        
        probabilities = [p / total_persistence for p in all_persistences]
        
        # Compute entropy
        entropy = -sum(p * np.log(p) for p in probabilities if p > 0)
        return float(entropy)


class BettiNumbers(BaseModel):
    """Betti numbers for different dimensions"""
    beta_0: int = Field(default=0, ge=0, description="0th Betti number (connected components)")
    beta_1: int = Field(default=0, ge=0, description="1st Betti number (loops)")
    beta_2: int = Field(default=0, ge=0, description="2nd Betti number (voids)")
    
    # Analysis parameters
    threshold: float = Field(default=0.1, gt=0.0, description="Persistence threshold")
    
    @property
    def euler_characteristic(self) -> int:
        """Compute Euler characteristic"""
        return self.beta_0 - self.beta_1 + self.beta_2
    
    @property
    def total_betti(self) -> int:
        """Total Betti number"""
        return self.beta_0 + self.beta_1 + self.beta_2


class TopologyFeatures(BaseModel):
    """Comprehensive topological features"""
    # Basic information
    snapshot_id: str = Field(..., description="Source snapshot ID")
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    computation_time: float = Field(default=0.0, ge=0.0, description="Computation time (seconds)")
    
    # Core topological features
    betti_numbers: BettiNumbers = Field(..., description="Betti numbers")
    persistence_diagram: PersistenceDiagram = Field(..., description="Persistence diagram")
    
    # Persistence statistics
    mean_persistence_0: float = Field(default=0.0, ge=0.0, description="Mean persistence (dim 0)")
    mean_persistence_1: float = Field(default=0.0, ge=0.0, description="Mean persistence (dim 1)")
    mean_persistence_2: float = Field(default=0.0, ge=0.0, description="Mean persistence (dim 2)")
    
    max_persistence_0: float = Field(default=0.0, ge=0.0, description="Max persistence (dim 0)")
    max_persistence_1: float = Field(default=0.0, ge=0.0, description="Max persistence (dim 1)")
    max_persistence_2: float = Field(default=0.0, ge=0.0, description="Max persistence (dim 2)")
    
    total_persistence_0: float = Field(default=0.0, ge=0.0, description="Total persistence (dim 0)")
    total_persistence_1: float = Field(default=0.0, ge=0.0, description="Total persistence (dim 1)")
    total_persistence_2: float = Field(default=0.0, ge=0.0, description="Total persistence (dim 2)")
    
    # Derived features
    total_features: int = Field(default=0, ge=0, description="Total topological features")
    complexity_score: float = Field(default=0.0, ge=0.0, description="Topological complexity score")
    
    # Point cloud information
    point_cloud_size: int = Field(default=0, ge=0, description="Point cloud size")
    point_cloud_dimension: int = Field(default=0, ge=0, description="Point cloud dimension")
    
    # Stability measures
    stability_score: float = Field(default=1.0, ge=0.0, le=1.0, description="Topological stability")
    noise_level: float = Field(default=0.0, ge=0.0, description="Estimated noise level")


class VulnerabilitySignature(BaseModel):
    """Known vulnerability topological signature"""
    name: str = Field(..., description="Signature name")
    description: str = Field(..., description="Signature description")
    signature_vector: List[float] = Field(..., description="Feature vector signature")
    confidence_threshold: float = Field(default=0.8, ge=0.0, le=1.0)
    
    # Pattern characteristics
    typical_betti_0: Optional[int] = Field(None, ge=0)
    typical_betti_1: Optional[int] = Field(None, ge=0)
    typical_betti_2: Optional[int] = Field(None, ge=0)
    
    # Historical data
    occurrence_count: int = Field(default=0, ge=0)
    last_seen: Optional[datetime] = Field(None)
    severity_distribution: Dict[str, int] = Field(default_factory=dict)


class AnomalyDetection(BaseModel):
    """Topological anomaly detection result"""
    is_anomaly: bool = Field(..., description="Whether an anomaly was detected")
    anomaly_score: float = Field(..., ge=0.0, le=1.0, description="Anomaly score")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Detection confidence")
    
    # Anomaly details
    anomaly_type: Optional[str] = Field(None, description="Type of anomaly")
    affected_dimensions: List[int] = Field(default_factory=list, description="Affected topological dimensions")
    
    # Vulnerability matching
    vulnerability_matches: List[str] = Field(default_factory=list, description="Matched vulnerability signatures")
    
    # Feature analysis
    feature_deviations: Dict[str, float] = Field(default_factory=dict, description="Feature deviations from normal")
    most_anomalous_features: List[str] = Field(default_factory=list, description="Most anomalous features")
    
    # Temporal information
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    detection_latency: float = Field(default=0.0, ge=0.0, description="Detection latency (seconds)")


class TopologyPrediction(BaseModel):
    """Topology-based failure prediction"""
    prediction_id: str = Field(..., description="Prediction identifier")
    snapshot_id: str = Field(..., description="Source snapshot ID")
    
    # Prediction results
    failure_probability: float = Field(..., ge=0.0, le=1.0, description="Failure probability")
    time_to_failure: Optional[float] = Field(None, ge=0.0, description="Predicted time to failure (seconds)")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Prediction confidence")
    
    # Failure characteristics
    predicted_failure_type: Optional[str] = Field(None, description="Predicted failure type")
    affected_services: List[str] = Field(default_factory=list, description="Potentially affected services")
    impact_score: float = Field(default=0.0, ge=0.0, le=1.0, description="Predicted impact score")
    
    # Topological indicators
    warning_indicators: List[str] = Field(default_factory=list, description="Topological warning indicators")
    critical_features: List[str] = Field(default_factory=list, description="Critical topological features")
    
    # Temporal information
    prediction_timestamp: datetime = Field(default_factory=datetime.utcnow)
    prediction_horizon: float = Field(default=300.0, gt=0.0, description="Prediction horizon (seconds)")


# === ANALYSIS RESULTS ===

class TopologyAnalysisResult(BaseModel):
    """Complete topology analysis result"""
    snapshot: TopologySnapshot = Field(..., description="Source snapshot")
    features: TopologyFeatures = Field(..., description="Extracted features")
    anomaly_detection: AnomalyDetection = Field(..., description="Anomaly detection result")
    prediction: Optional[TopologyPrediction] = Field(None, description="Failure prediction")
    
    # Analysis metadata
    analysis_id: str = Field(..., description="Analysis identifier")
    analysis_timestamp: datetime = Field(default_factory=datetime.utcnow)
    total_analysis_time: float = Field(default=0.0, ge=0.0, description="Total analysis time (seconds)")
    
    # Quality metrics
    data_quality_score: float = Field(default=1.0, ge=0.0, le=1.0, description="Input data quality")
    analysis_confidence: float = Field(default=1.0, ge=0.0, le=1.0, description="Overall analysis confidence")


class TopologyHealthCheck(BaseModel):
    """Topology analyzer health check"""
    status: str = Field(..., description="Service status")
    gpu_enabled: bool = Field(..., description="GPU acceleration status")
    cache_size: int = Field(..., ge=0, description="Feature cache size")
    historical_features: int = Field(..., ge=0, description="Historical features count")
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    
    # Performance metrics
    average_analysis_time: Optional[float] = Field(None, ge=0.0, description="Average analysis time")
    throughput: Optional[float] = Field(None, ge=0.0, description="Analysis throughput (snapshots/second)")
    
    # Resource usage
    memory_usage: Optional[float] = Field(None, ge=0.0, description="Memory usage (MB)")
    gpu_memory_usage: Optional[float] = Field(None, ge=0.0, description="GPU memory usage (MB)")


# Forward references
TopologySnapshot.model_rebuild()
ServiceSnapshot.model_rebuild()
MetricSnapshot.model_rebuild()
DependencySnapshot.model_rebuild()
