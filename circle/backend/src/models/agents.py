"""
🤖 AURA Intelligence Agent Models
Pydantic models for multi-agent system components
"""

from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from enum import Enum

from pydantic import BaseModel, Field, validator


class AgentType(str, Enum):
    """Types of agents in the system"""
    INTENT = "intent"
    TOPOLOGY = "topology"
    TEMPORAL = "temporal"
    CAUSAL = "causal"
    REMEDIATION = "remediation"
    INTEGRATION = "integration"
    SYNTHESIS = "synthesis"


class TaskStatus(str, Enum):
    """Task execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"
    CANCELLED = "cancelled"


class Priority(str, Enum):
    """Task priority levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


# === CORE AGENT MODELS ===

class Agent(BaseModel):
    """Agent definition and capabilities"""
    agent_type: AgentType = Field(..., description="Agent type")
    name: str = Field(..., description="Human-readable agent name")
    description: str = Field(..., description="Agent description")
    version: str = Field(default="1.0.0", description="Agent version")
    
    # Capabilities
    capabilities: List[str] = Field(default_factory=list, description="Agent capabilities")
    supported_tasks: List[str] = Field(default_factory=list, description="Supported task types")
    
    # Configuration
    max_concurrent_tasks: int = Field(default=1, ge=1, le=10, description="Max concurrent tasks")
    default_timeout: int = Field(default=30, ge=1, le=300, description="Default timeout (seconds)")
    
    # Performance metrics
    success_rate: float = Field(default=1.0, ge=0.0, le=1.0, description="Historical success rate")
    average_execution_time: float = Field(default=0.0, ge=0.0, description="Average execution time")
    
    # Status
    is_active: bool = Field(default=True, description="Whether agent is active")
    last_health_check: Optional[datetime] = Field(None, description="Last health check")
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    metadata: Dict[str, Any] = Field(default_factory=dict)


class AgentTask(BaseModel):
    """Task assigned to an agent"""
    task_id: str = Field(..., description="Unique task identifier")
    agent_type: AgentType = Field(..., description="Target agent type")
    task_type: str = Field(default="analysis", description="Type of task")
    
    # Task data
    incident_data: Dict[str, Any] = Field(..., description="Incident data to analyze")
    context: Dict[str, Any] = Field(default_factory=dict, description="Additional context")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Task parameters")
    
    # Execution settings
    priority: Priority = Field(default=Priority.MEDIUM, description="Task priority")
    timeout: int = Field(default=30, ge=1, le=300, description="Task timeout (seconds)")
    retry_count: int = Field(default=0, ge=0, le=3, description="Number of retries")
    max_retries: int = Field(default=2, ge=0, le=5, description="Maximum retries")
    
    # Status tracking
    status: TaskStatus = Field(default=TaskStatus.PENDING, description="Task status")
    assigned_at: Optional[datetime] = Field(None, description="Task assignment time")
    started_at: Optional[datetime] = Field(None, description="Task start time")
    completed_at: Optional[datetime] = Field(None, description="Task completion time")
    
    # Dependencies
    depends_on: List[str] = Field(default_factory=list, description="Task dependencies")
    blocks: List[str] = Field(default_factory=list, description="Tasks blocked by this task")
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    created_by: str = Field(default="orchestrator", description="Task creator")
    
    @property
    def execution_time(self) -> Optional[float]:
        """Calculate task execution time"""
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None
    
    @property
    def is_overdue(self) -> bool:
        """Check if task is overdue"""
        if self.started_at:
            elapsed = (datetime.utcnow() - self.started_at).total_seconds()
            return elapsed > self.timeout
        return False


class AgentResult(BaseModel):
    """Result from agent execution"""
    agent_type: AgentType = Field(..., description="Agent that produced the result")
    task_id: Optional[str] = Field(None, description="Associated task ID")
    
    # Execution results
    success: bool = Field(..., description="Whether execution was successful")
    data: Optional[Dict[str, Any]] = Field(None, description="Result data")
    error: Optional[str] = Field(None, description="Error message if failed")
    
    # Performance metrics
    execution_time: float = Field(..., ge=0.0, description="Execution time (seconds)")
    memory_usage: Optional[float] = Field(None, ge=0.0, description="Memory usage (MB)")
    cpu_usage: Optional[float] = Field(None, ge=0.0, description="CPU usage (%)")
    
    # Quality metrics
    confidence: float = Field(default=1.0, ge=0.0, le=1.0, description="Result confidence")
    quality_score: float = Field(default=1.0, ge=0.0, le=1.0, description="Result quality")
    
    # Metadata
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    version: str = Field(default="1.0.0", description="Agent version")
    
    # Validation
    @validator('error')
    def error_required_if_failed(cls, v, values):
        if not values.get('success') and not v:
            raise ValueError('Error message required when success is False')
        return v


# === ORCHESTRATION MODELS ===

class OrchestrationPlan(BaseModel):
    """Plan for orchestrating multiple agents"""
    plan_id: str = Field(..., description="Unique plan identifier")
    incident_data: Dict[str, Any] = Field(..., description="Incident data")
    
    # Execution phases
    execution_phases: List[Dict[str, Any]] = Field(..., description="Execution phases")
    total_timeout: int = Field(..., ge=1, description="Total plan timeout (seconds)")
    
    # Dependencies and constraints
    agent_dependencies: Dict[AgentType, List[AgentType]] = Field(
        default_factory=dict, description="Agent dependencies"
    )
    resource_constraints: Dict[str, Any] = Field(
        default_factory=dict, description="Resource constraints"
    )
    
    # Status
    status: TaskStatus = Field(default=TaskStatus.PENDING, description="Plan status")
    progress: float = Field(default=0.0, ge=0.0, le=1.0, description="Execution progress")
    
    # Timing
    created_at: datetime = Field(default_factory=datetime.utcnow)
    started_at: Optional[datetime] = Field(None)
    completed_at: Optional[datetime] = Field(None)
    
    @property
    def execution_time(self) -> Optional[float]:
        """Calculate plan execution time"""
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None


class IncidentAnalysis(BaseModel):
    """Complete incident analysis result"""
    analysis_id: str = Field(..., description="Unique analysis identifier")
    incident_id: str = Field(..., description="Source incident ID")
    
    # Analysis results
    incident_type: str = Field(..., description="Classified incident type")
    severity: str = Field(..., description="Assessed severity")
    priority_score: float = Field(..., ge=0.0, le=1.0, description="Priority score")
    
    # Root cause analysis
    root_causes: List[str] = Field(default_factory=list, description="Identified root causes")
    contributing_factors: List[str] = Field(default_factory=list, description="Contributing factors")
    affected_services: List[str] = Field(default_factory=list, description="Affected services")
    
    # Topological insights
    topological_signature: Optional[str] = Field(None, description="Topological signature")
    anomaly_score: float = Field(default=0.0, ge=0.0, le=1.0, description="Anomaly score")
    complexity_score: float = Field(default=0.0, ge=0.0, description="System complexity score")
    
    # Temporal patterns
    temporal_patterns: List[str] = Field(default_factory=list, description="Temporal patterns")
    trend_analysis: Optional[str] = Field(None, description="Trend analysis")
    
    # Recommendations
    recommended_actions: List[str] = Field(default_factory=list, description="Recommended actions")
    preventive_measures: List[str] = Field(default_factory=list, description="Preventive measures")
    
    # Confidence and quality
    confidence: float = Field(..., ge=0.0, le=1.0, description="Overall analysis confidence")
    quality_score: float = Field(default=1.0, ge=0.0, le=1.0, description="Analysis quality")
    
    # Agent contributions
    agent_results: Dict[AgentType, AgentResult] = Field(
        default_factory=dict, description="Individual agent results"
    )
    
    # Metadata
    analysis_timestamp: datetime = Field(default_factory=datetime.utcnow)
    analysis_duration: Optional[float] = Field(None, ge=0.0, description="Analysis duration (seconds)")
    
    # Business impact
    estimated_impact: Optional[str] = Field(None, description="Estimated business impact")
    cost_estimate: Optional[float] = Field(None, ge=0.0, description="Estimated cost impact")


class RemediationPlan(BaseModel):
    """Automated remediation plan"""
    plan_id: str = Field(..., description="Unique plan identifier")
    incident_id: str = Field(..., description="Source incident ID")
    analysis_id: str = Field(..., description="Source analysis ID")
    
    # Remediation steps
    steps: List['RemediationStep'] = Field(..., description="Remediation steps")
    estimated_duration: float = Field(..., ge=0.0, description="Estimated duration (seconds)")
    
    # Safety and validation
    safety_checks: List[str] = Field(default_factory=list, description="Required safety checks")
    rollback_plan: Optional['RollbackPlan'] = Field(None, description="Rollback plan")
    approval_required: bool = Field(default=True, description="Whether human approval is required")
    
    # Success criteria
    success_criteria: List[str] = Field(default_factory=list, description="Success criteria")
    validation_steps: List[str] = Field(default_factory=list, description="Validation steps")
    
    # Risk assessment
    risk_level: str = Field(default="medium", description="Risk level")
    potential_side_effects: List[str] = Field(default_factory=list, description="Potential side effects")
    
    # Confidence and metadata
    confidence: float = Field(..., ge=0.0, le=1.0, description="Plan confidence")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    created_by: AgentType = Field(default=AgentType.REMEDIATION, description="Creating agent")


class RemediationStep(BaseModel):
    """Individual remediation step"""
    step_id: str = Field(..., description="Step identifier")
    name: str = Field(..., description="Step name")
    description: str = Field(..., description="Step description")
    
    # Execution details
    action_type: str = Field(..., description="Type of action")
    command: Optional[str] = Field(None, description="Command to execute")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Step parameters")
    
    # Dependencies and ordering
    depends_on: List[str] = Field(default_factory=list, description="Step dependencies")
    order: int = Field(..., ge=1, description="Execution order")
    
    # Timing and safety
    timeout: int = Field(default=60, ge=1, description="Step timeout (seconds)")
    retry_count: int = Field(default=0, ge=0, description="Retry attempts")
    max_retries: int = Field(default=2, ge=0, description="Maximum retries")
    
    # Validation
    pre_conditions: List[str] = Field(default_factory=list, description="Pre-conditions")
    post_conditions: List[str] = Field(default_factory=list, description="Post-conditions")
    
    # Status
    status: TaskStatus = Field(default=TaskStatus.PENDING, description="Step status")
    executed_at: Optional[datetime] = Field(None, description="Execution timestamp")
    execution_time: Optional[float] = Field(None, ge=0.0, description="Execution time")


class RollbackPlan(BaseModel):
    """Rollback plan for remediation"""
    rollback_id: str = Field(..., description="Rollback plan identifier")
    remediation_plan_id: str = Field(..., description="Associated remediation plan")
    
    # Rollback steps
    rollback_steps: List[RemediationStep] = Field(..., description="Rollback steps")
    
    # Triggers
    automatic_triggers: List[str] = Field(default_factory=list, description="Automatic rollback triggers")
    manual_trigger_conditions: List[str] = Field(default_factory=list, description="Manual trigger conditions")
    
    # Safety
    safety_checks: List[str] = Field(default_factory=list, description="Rollback safety checks")
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    confidence: float = Field(default=0.9, ge=0.0, le=1.0, description="Rollback confidence")


# === PERFORMANCE AND MONITORING ===

class AgentPerformanceMetrics(BaseModel):
    """Agent performance metrics"""
    agent_type: AgentType = Field(..., description="Agent type")
    
    # Execution metrics
    total_tasks: int = Field(default=0, ge=0, description="Total tasks executed")
    successful_tasks: int = Field(default=0, ge=0, description="Successful tasks")
    failed_tasks: int = Field(default=0, ge=0, description="Failed tasks")
    
    # Performance statistics
    average_execution_time: float = Field(default=0.0, ge=0.0, description="Average execution time")
    min_execution_time: float = Field(default=0.0, ge=0.0, description="Minimum execution time")
    max_execution_time: float = Field(default=0.0, ge=0.0, description="Maximum execution time")
    
    # Quality metrics
    average_confidence: float = Field(default=1.0, ge=0.0, le=1.0, description="Average confidence")
    average_quality: float = Field(default=1.0, ge=0.0, le=1.0, description="Average quality")
    
    # Resource usage
    average_memory_usage: Optional[float] = Field(None, ge=0.0, description="Average memory usage")
    average_cpu_usage: Optional[float] = Field(None, ge=0.0, description="Average CPU usage")
    
    # Time period
    period_start: datetime = Field(..., description="Metrics period start")
    period_end: datetime = Field(..., description="Metrics period end")
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate"""
        if self.total_tasks == 0:
            return 1.0
        return self.successful_tasks / self.total_tasks
    
    @property
    def failure_rate(self) -> float:
        """Calculate failure rate"""
        return 1.0 - self.success_rate


class OrchestratorHealthCheck(BaseModel):
    """Orchestrator health check result"""
    status: str = Field(..., description="Overall status")
    agent_count: int = Field(..., ge=0, description="Number of agents")
    active_tasks: int = Field(..., ge=0, description="Active tasks")
    
    # Agent statuses
    agent_statuses: Dict[str, str] = Field(default_factory=dict, description="Individual agent statuses")
    
    # Performance
    execution_history: int = Field(..., ge=0, description="Execution history count")
    average_analysis_time: Optional[float] = Field(None, ge=0.0, description="Average analysis time")
    
    # Resource usage
    memory_usage: Optional[float] = Field(None, ge=0.0, description="Memory usage (MB)")
    cpu_usage: Optional[float] = Field(None, ge=0.0, description="CPU usage (%)")
    
    # Timestamp
    timestamp: datetime = Field(default_factory=datetime.utcnow)


# Forward references
RemediationPlan.model_rebuild()
RemediationStep.model_rebuild()
RollbackPlan.model_rebuild()
